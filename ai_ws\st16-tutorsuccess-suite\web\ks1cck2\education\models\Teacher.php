<?php
/**
 * 教师数据模型
 * 创建时间：2025-01-22
 */

require_once 'BaseModel.php';

class Teacher extends BaseModel {
    
    public function __construct($conn) {
        parent::__construct($conn);
        $this->table = TABLE_TEACHER;
    }
    
    /**
     * 根据用户ID查找教师信息
     * @param int $user_id
     * @return array|null
     */
    public function findByUserId($user_id) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? AND status = ? LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('ii', $user_id, STATUS_ACTIVE);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
    
    /**
     * 获取教师列表（包含用户信息）
     * @param array $conditions
     * @param string $order_by
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getTeachersWithUser($conditions = [], $order_by = 't.create_time DESC', $limit = 0, $offset = 0) {
        $sql = "SELECT t.*, u.username, u.email 
                FROM {$this->table} t 
                LEFT JOIN user u ON t.user_id = u.userid 
                WHERE t.status = ?";
        
        $params = [STATUS_ACTIVE];
        $types = 'i';
        
        if (!empty($conditions)) {
            foreach ($conditions as $field => $value) {
                $sql .= " AND t.{$field} = ?";
                $params[] = $value;
                $types .= is_int($value) ? 'i' : 's';
            }
        }
        
        if ($order_by) {
            $sql .= " ORDER BY {$order_by}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $teachers = [];
        while ($row = $result->fetch_assoc()) {
            $teachers[] = $row;
        }
        
        return $teachers;
    }
    
    /**
     * 获取教师的课程列表
     * @param int $teacher_id
     * @return array
     */
    public function getCourses($teacher_id) {
        $sql = "SELECT c.*, 
                COUNT(DISTINCT sc.student_id) as student_count,
                COUNT(DISTINCT cs.id) as schedule_count
                FROM " . TABLE_COURSE . " c
                LEFT JOIN " . TABLE_STUDENT_COURSE . " sc ON c.id = sc.course_id AND sc.status = ?
                LEFT JOIN " . TABLE_COURSE_SCHEDULE . " cs ON c.id = cs.course_id AND cs.status = ?
                WHERE c.teacher_id = ? AND c.status = ?
                GROUP BY c.id
                ORDER BY c.create_time DESC";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('iiii', $active, $active, $teacher_id, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        
        return $courses;
    }
    
    /**
     * 获取教师的课程安排
     * @param int $teacher_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getSchedule($teacher_id, $start_date = null, $end_date = null) {
        $sql = "SELECT cs.*, c.name as course_name, cr.name as classroom_name
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                JOIN " . TABLE_CLASSROOM . " cr ON cs.classroom_id = cr.id
                WHERE c.teacher_id = ? AND cs.status = ? AND c.status = ?";
        
        $params = [$teacher_id, STATUS_ACTIVE, STATUS_ACTIVE];
        $types = 'iii';
        
        if ($start_date) {
            $sql .= " AND cs.start_date >= ?";
            $params[] = $start_date;
            $types .= 's';
        }
        
        if ($end_date) {
            $sql .= " AND (cs.end_date IS NULL OR cs.end_date <= ?)";
            $params[] = $end_date;
            $types .= 's';
        }
        
        $sql .= " ORDER BY cs.day_of_week, cs.start_time";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $schedules = [];
        while ($row = $result->fetch_assoc()) {
            $schedules[] = $row;
        }
        
        return $schedules;
    }
    
    /**
     * 获取教师的请假申请列表
     * @param int $teacher_id
     * @param int $status 请假状态
     * @return array
     */
    public function getLeaveRequests($teacher_id, $status = null) {
        $sql = "SELECT lr.*, s.name as student_name, c.name as course_name, cs.day_of_week, cs.start_time, cs.end_time
                FROM " . TABLE_LEAVE_REQUEST . " lr
                JOIN " . TABLE_STUDENT . " s ON lr.student_id = s.id
                JOIN " . TABLE_COURSE_SCHEDULE . " cs ON lr.course_schedule_id = cs.id
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                WHERE c.teacher_id = ?";
        
        $params = [$teacher_id];
        $types = 'i';
        
        if ($status !== null) {
            $sql .= " AND lr.status = ?";
            $params[] = $status;
            $types .= 'i';
        }
        
        $sql .= " ORDER BY lr.create_time DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $requests = [];
        while ($row = $result->fetch_assoc()) {
            $requests[] = $row;
        }
        
        return $requests;
    }
    
    /**
     * 审批请假申请
     * @param int $request_id
     * @param int $status
     * @param string $remark
     * @param int $approve_by
     * @return bool
     */
    public function approveLeaveRequest($request_id, $status, $remark = '', $approve_by = null) {
        $sql = "UPDATE " . TABLE_LEAVE_REQUEST . " 
                SET status = ?, approve_by = ?, approve_time = NOW(), approve_remark = ?
                WHERE id = ?";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('iisi', $status, $approve_by, $remark, $request_id);
        
        return $stmt->execute();
    }
    
    /**
     * 获取教师统计信息
     * @param int $teacher_id
     * @return array
     */
    public function getStatistics($teacher_id) {
        // 课程数量
        $course_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_COURSE . " WHERE teacher_id = ? AND status = ?",
            [$teacher_id, STATUS_ACTIVE]
        )[0]['count'];
        
        // 学生数量
        $student_count = $this->query(
            "SELECT COUNT(DISTINCT sc.student_id) as count 
             FROM " . TABLE_STUDENT_COURSE . " sc
             JOIN " . TABLE_COURSE . " c ON sc.course_id = c.id
             WHERE c.teacher_id = ? AND sc.status = ? AND c.status = ?",
            [$teacher_id, STATUS_ACTIVE, STATUS_ACTIVE]
        )[0]['count'];
        
        // 待审批请假数量
        $pending_leave_count = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_LEAVE_REQUEST . " lr
             JOIN " . TABLE_COURSE_SCHEDULE . " cs ON lr.course_schedule_id = cs.id
             JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
             WHERE c.teacher_id = ? AND lr.status = ?",
            [$teacher_id, LEAVE_STATUS_PENDING]
        )[0]['count'];
        
        // 本周课程数量
        $week_schedule_count = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_COURSE_SCHEDULE . " cs
             JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
             WHERE c.teacher_id = ? AND cs.status = ? AND c.status = ?
             AND cs.start_date <= CURDATE() 
             AND (cs.end_date IS NULL OR cs.end_date >= CURDATE())",
            [$teacher_id, STATUS_ACTIVE, STATUS_ACTIVE]
        )[0]['count'];
        
        return [
            'course_count' => $course_count,
            'student_count' => $student_count,
            'pending_leave_count' => $pending_leave_count,
            'week_schedule_count' => $week_schedule_count
        ];
    }
    
    /**
     * 创建或更新教师信息
     * @param int $user_id
     * @param array $data
     * @return int|bool
     */
    public function createOrUpdate($user_id, $data) {
        $existing = $this->findByUserId($user_id);
        
        $data['user_id'] = $user_id;
        $data['update_time'] = date('Y-m-d H:i:s');
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            $data['create_time'] = date('Y-m-d H:i:s');
            return $this->create($data);
        }
    }
}
