<?php
require_once('admin/mysqlconn.php');
$row = $conn->get('main','*',['id'=>1]);
$wxappletaddress = $row["wxappletaddress"];
$license = $row["license"];

$httpprotocol = "http";
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $httpprotocol = "https";
}
$target = file_get_contents($wxappletaddress . "?token=" . $license . "&url=" . base64_encode($httpprotocol . "://" . $_SERVER['HTTP_HOST'] . "/wxappletuserlogin.php?from=android&userrndstr=" . $_GET["s"] . "&openid="));
if ($_GET['from'] == 'android') {
    echo $target;
} else {
    header("Location: " . $target);
}
