<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_POST["action"])) {
    $conn->update('asr', ['asrmodel' => $_POST["asrmodel"], 'openaiapikey' => $_POST["openaiapikey"], 'openaiapiaddress' => $_POST["openaiapiaddress"]], ['id' => 1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('asr', '*', ['id' => 1]);
$asrmodel = $row["asrmodel"];
$openaiapikey = $row["openaiapikey"];
$openaiapiaddress = $row["openaiapiaddress"];
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>语音识别模型配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script src="/js/howler.min.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: left;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">语音识别模型配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <form class="form-horizontal" method=post target=temp>
                <input type=hidden name=action value=set>
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div style="padding-left:50px;">
                        <div style="font-size:20px;padding-bottom:20px;"><input type=radio name=asrmodel value="openai" <?php if ($asrmodel == 'openai') echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">OpenAI语音识别（whisper模型）</div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">APIKEY：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $openaiapikey; ?>" style="text-align:left;" id="openaiapikey" name="openaiapikey" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 control-label">API地址：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $openaiapiaddress; ?>" style="text-align:left;" id="openaiapiaddress" name="openaiapiaddress" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                    <div class="col-lg-6 col-lg-offset-3">
                        <button type="submit" class="btn btn-primary">确认设置</submit>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>