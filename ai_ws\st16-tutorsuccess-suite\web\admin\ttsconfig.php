<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_POST["action"])) {
    $conn->update('tts', ['ttsmodel' => $_POST["ttsmodel"], 'microsoftapiaddress' => $_POST["microsoftapiaddress"], 'microsoftrole' => $_POST["microsoftrole"], 'microsoftspeed' => $_POST["microsoftspeed"], 'microsoftvolume' => $_POST["microsoftvolume"], 'xunfeiapikey' => $_POST["xunfeiapikey"], 'xunfeiapiaddress' => $_POST["xunfeiapiaddress"], 'xunfeirole' => $_POST["xunfeirole"], 'xunfeipitch' => $_POST["xunfeipitch"], 'xunfeivolume' => $_POST["xunfeivolume"], 'xunfeispeed' => $_POST["xunfeispeed"], 'openaiapikey' => $_POST["openaiapikey"], 'openaiapiaddress' => $_POST["openaiapiaddress"], 'openaimodel' => $_POST["openaimodel"], 'openairole' => $_POST["openairole"], 'openaispeed' => $_POST["openaispeed"]], ['id' => 1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('tts', '*', ['id' => 1]);
$ttsmodel = $row["ttsmodel"];
$microsoftapiaddress = $row["microsoftapiaddress"];
$microsoftrole = $row["microsoftrole"];
$microsoftspeed = $row["microsoftspeed"];
$microsoftvolume = $row["microsoftvolume"];
$xunfeiapikey = $row["xunfeiapikey"];
$xunfeiapiaddress = $row["xunfeiapiaddress"];
$xunfeirole = $row["xunfeirole"];
$xunfeipitch = $row["xunfeipitch"];
$xunfeispeed = $row["xunfeispeed"];
$xunfeivolume = $row["xunfeivolume"];
$openaiapikey = $row["openaiapikey"];
$openaiapiaddress = $row["openaiapiaddress"];
$openaimodel = $row["openaimodel"];
$openairole = $row["openairole"];
$openaispeed = $row["openaispeed"];

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>朗读模型配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script src="/js/howler.min.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: left;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">朗读模型配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <form class="form-horizontal" method=post target=temp>
                <input type=hidden name=action value=set>
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div style="padding-left:50px;">
                        <div style="border-radius: 10px;padding:30px 20px 15px 20px;line-height:20px;margin:0 50px 20px 0;border:1px solid silver;">
                            <div class="form-group">
                                <label class="col-lg-2 control-label">朗读效果测试：</label>
                                <div class="col-lg-7">
                                    <input id="ttstext" type=text style="text-align:left;width:100%;" placeholder="请输入文字" class="bg-focus form-control" autoComplete="off">
                                </div>
                                <input type=button onclick="readtext();" class="btn" style="height:34px;padding:0 10px;" value="开始朗读">
                            </div>
                        </div>
                        <div style="font-size:20px;padding-bottom:20px;"><input type=radio name=ttsmodel value="openai" <?php if ($ttsmodel == 'openai') echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">OpenAI TTS</div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">APIKEY：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $openaiapikey; ?>" style="text-align:left;" id="openaiapikey" name="openaiapikey" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 control-label">API地址：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $openaiapiaddress; ?>" style="text-align:left;" id="openaiapiaddress" name="openaiapiaddress" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 control-label">模型：</label>

                            <div class="col-lg-4">
                                <select name=openaimodel>
                                    <option value=tts-1 <?php if ($openaimodel == "tts-1") echo "selected"; ?>>tts-1</option>
                                    <option value=tts-1-hd <?php if ($openaimodel == "tts-1-hd") echo "selected"; ?>>tts-1-hd</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">发音人：</label>

                            <div class="col-lg-4">
                                <select name=openairole>
                                    <option value="">请选择</option>
                                    <option value=alloy <?php if ($openairole == "alloy") echo "selected"; ?>>alloy</option>
                                    <option value=echo <?php if ($openairole == "echo") echo "selected"; ?>>echo</option>
                                    <option value=fable <?php if ($openairole == "fable") echo "selected"; ?>>fable</option>
                                    <option value=nova <?php if ($openairole == "nova") echo "selected"; ?>>nova</option>
                                    <option value=onyx <?php if ($openairole == "onyx") echo "selected"; ?>>onyx</option>
                                    <option value=shimmer <?php if ($openairole == "shimmer") echo "selected"; ?>>shimmer</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">语速：</label>

                            <div class="col-lg-2">
                                <input type="text" value="<?php echo $openaispeed; ?>" style="text-align:left;" id="openaispeed" name="openaispeed" class="bg-focus form-control" autoComplete="off" placeholder="填写0.25到4.0之间的整数，默认为1.0">
                            </div>
                        </div>
                    </div>

                    <div style="padding:30px 0 0 50px;">
                        <div style="font-size:20px;padding-bottom:20px;"><input type=radio name=ttsmodel value="xunfei" <?php if ($ttsmodel == 'xunfei') echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">科大讯飞TTS</div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">APIKEY：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $xunfeiapikey; ?>" style="text-align:left;" id="xunfeiapikey" name="xunfeiapikey" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 control-label">API地址：</label>

                            <div class="col-lg-8">
                                <input type="text" value="<?php echo $xunfeiapiaddress; ?>" style="text-align:left;" id="xunfeiapiaddress" name="xunfeiapiaddress" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">发音人：</label>

                            <div class="col-lg-4">
                                <?php
                                if (($xunfeirole == "xiaoyan") || ($xunfeirole == "aisjiuxu") || ($xunfeirole == "aisxping") || ($xunfeirole == "aisjinger") || ($xunfeirole == "aisbabyxu")) {
                                ?>
                                    <select name=xunfeirole onchange='changecontent(this.value);'>
                                        <option value="">请选择</option>
                                        <option value=xiaoyan <?php if ($xunfeirole == "xiaoyan") echo "selected"; ?>>xiaoyan</option>
                                        <option value=aisjiuxu <?php if ($xunfeirole == "aisjiuxu") echo "selected"; ?>>aisjiuxu</option>
                                        <option value=aisxping <?php if ($xunfeirole == "aisxping") echo "selected"; ?>>aisxping</option>
                                        <option value=aisjinger <?php if ($xunfeirole == "aisjinger") echo "selected"; ?>>aisjinger</option>
                                        <option value=aisbabyxu <?php if ($xunfeirole == "aisbabyxu") echo "selected"; ?>>aisbabyxu</option>
                                        <option value=其他>其他</option>
                                    </select>
                                <?php
                                } else {
                                    echo '<input type=text name=xunfeirole value="' . $xunfeirole . '">';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">语速：</label>

                            <div class="col-lg-2">
                                <input type="text" value="<?php echo $xunfeispeed; ?>" style="text-align:left;" id="xunfeispeed" name="xunfeispeed" class="bg-focus form-control" autoComplete="off" placeholder="填写0到100之间的整数，默认为50">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">音量：</label>

                            <div class="col-lg-2">
                                <input type="text" value="<?php echo $xunfeivolume; ?>" style="text-align:left;" id="xunfeivolume" name="xunfeivolume" class="bg-focus form-control" autoComplete="off" placeholder="填写0到100之间的整数，默认为50">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-2 control-label">音高：</label>

                            <div class="col-lg-2">
                                <input type="text" value="<?php echo $xunfeipitch; ?>" style="text-align:left;" id="xunfeipitch" name="xunfeipitch" class="bg-focus form-control" autoComplete="off" placeholder="填写0到100之间的整数，默认为50">
                            </div>
                        </div>
                    </div>
                </div>

                <div style="padding:30px 0 0 50px;">
                    <div style="font-size:20px;padding-bottom:20px;"><input type=radio name=ttsmodel value="microsoft" <?php if ($ttsmodel == 'microsoft') echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">微软TTS</div>

                    <div class="form-group">
                        <label class="col-lg-2 control-label">API地址：</label>

                        <div class="col-lg-8">
                            <input type="text" value="<?php echo $microsoftapiaddress; ?>" style="text-align:left;" id="microsoftapiaddress" name="microsoftapiaddress" class="bg-focus form-control" autoComplete="off">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 control-label">发音人：</label>

                        <div class="col-lg-4">
                            <select name=microsoftrole onchange='changecontent(this.value);'>
                                <option value="">请选择</option>
                                <option value="zh-CN-XiaoxiaoNeural" <?php if ($microsoftrole == "zh-CN-XiaoxiaoNeural") echo "selected"; ?>>中国大陆-Xiaoxiao</option>
                                <option value="zh-CN-XiaoyiNeural" <?php if ($microsoftrole == "zh-CN-XiaoyiNeural") echo "selected"; ?>>中国大陆-Xiaoyi</option>
                                <option value="zh-CN-YunjianNeural" <?php if ($microsoftrole == "zh-CN-YunjianNeural") echo "selected"; ?>>中国大陆-Yunjian</option>
                                <option value="zh-CN-YunxiNeural" <?php if ($microsoftrole == "zh-CN-YunxiNeural") echo "selected"; ?>>中国大陆-Yunxi</option>
                                <option value="zh-CN-YunxiaNeural" <?php if ($microsoftrole == "zh-CN-YunxiaNeural") echo "selected"; ?>>中国大陆-Yunxia</option>
                                <option value="zh-CN-YunyangNeural" <?php if ($microsoftrole == "zh-CN-YunyangNeural") echo "selected"; ?>>中国大陆-Yunyang</option>
                                <option value="zh-CN-liaoning-XiaobeiNeural" <?php if ($microsoftrole == "zh-CN-liaoning-XiaobeiNeural") echo "selected"; ?>>中国辽宁-Xiaobei</option>
                                <option value="zh-CN-shaanxi-XiaoniNeural" <?php if ($microsoftrole == "zh-CN-shaanxi-XiaoniNeural") echo "selected"; ?>>中国陕西-Xiaoni</option>
                                <option value="zh-HK-HiuGaaiNeural" <?php if ($microsoftrole == "zh-HK-HiuGaaiNeural") echo "selected"; ?>>中国香港-HiuGaai</option>
                                <option value="zh-HK-HiuMaanNeural" <?php if ($microsoftrole == "zh-HK-HiuMaanNeural") echo "selected"; ?>>中国香港-HiuMaan</option>
                                <option value="zh-HK-WanLungNeural" <?php if ($microsoftrole == "zh-HK-WanLungNeural") echo "selected"; ?>>中国香港-WanLung</option>
                                <option value="zh-TW-HsiaoChenNeural" <?php if ($microsoftrole == "zh-TW-HsiaoChenNeural") echo "selected"; ?>>中国台湾-HsiaoChen</option>
                                <option value="zh-TW-HsiaoYuNeural" <?php if ($microsoftrole == "zh-TW-HsiaoYuNeural") echo "selected"; ?>>中国台湾-HsiaoYu</option>
                                <option value="zh-TW-YunJheNeural" <?php if ($microsoftrole == "zh-TW-YunJheNeural") echo "selected"; ?>>中国台湾-YunJhe</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 control-label">语速：</label>

                        <div class="col-lg-2">
                            <input type="text" value="<?php echo $microsoftspeed; ?>" style="text-align:left;" id="microsoftspeed" name="microsoftspeed" class="bg-focus form-control" autoComplete="off" placeholder="填写0到100之间的整数，默认为50">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 control-label">音量：</label>

                        <div class="col-lg-2">
                            <input type="text" value="<?php echo $microsoftvolume; ?>" style="text-align:left;" id="microsoftvolume" name="microsoftvolume" class="bg-focus form-control" autoComplete="off" placeholder="填写0到100之间的整数，默认为50">
                        </div>
                    </div>
                </div>
                <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                    <div class="col-lg-6 col-lg-offset-3">
                        <button type="submit" class="btn btn-primary">确认设置</submit>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
    <script>
        function changecontent(value) {
            if (value === "其他") {
                var inputElement = document.createElement("input");
                inputElement.setAttribute("type", "text");
                inputElement.setAttribute("id", "xunfeirole");
                inputElement.setAttribute("name", "xunfeirole");
                inputElement.setAttribute("required", "required");
                inputElement.setAttribute("placeholder", "请输入发音人");

                var selectElement = document.getElementById("xunfeirole");
                selectElement.parentNode.replaceChild(inputElement, selectElement);
            }
        }

        function readtext() {
            var newsound = new Howl({
                src: ["/tts.php?userrndstr=admin&msg=" + encodeURIComponent($("#ttstext").val())],
                html5: true,
                pool: 10,
                preload: 'metadata',
                format: ['mp3']
            });
            newsound.play();
        }
    </script>
</body>

</html>