<?php
/**
 * 管理员仪表板
 * 创建时间：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../../_ks1.php';
require_once '../../mysqlconn.php';
require_once '../../education/init.php';

// 检查管理员权限
require_permission(ROLE_ADMIN, '../../login.php');

// 引入模型
require_once '../../education/models/Teacher.php';
require_once '../../education/models/Student.php';
require_once '../../education/models/Course.php';
require_once '../../education/models/Classroom.php';

$teacher_model = new Teacher($conn);
$student_model = new Student($conn);
$course_model = new Course($conn);
$classroom_model = new Classroom($conn);

// 获取统计数据
$stats = [
    'teacher_count' => $teacher_model->count(['status' => STATUS_ACTIVE]),
    'student_count' => $student_model->count(['status' => STATUS_ACTIVE]),
    'course_count' => $course_model->count(['status' => STATUS_ACTIVE]),
    'classroom_count' => $classroom_model->count(['status' => STATUS_ACTIVE])
];

// 获取最近的数据
$recent_teachers = $teacher_model->getTeachersWithUser([], 't.create_time DESC', 5);
$recent_students = $student_model->getStudentsWithUser([], 's.create_time DESC', 5);
$recent_courses = $course_model->getCoursesWithTeacher([], 'c.create_time DESC', 5);

$page_title = '管理员仪表板';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-icon {
            font-size: 3rem;
            opacity: 0.8;
        }
        .quick-action {
            text-align: center;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .quick-action:hover {
            background-color: #f8f9fa;
        }
        .quick-action i {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统 - 管理员
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fa fa-dashboard"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fa fa-chalkboard-teacher"></i> 教师管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fa fa-user-graduate"></i> 学生管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.php">
                            <i class="fa fa-door-open"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../../role_manage.php">
                            <i class="fa fa-users"></i> 角色管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../../index.php">
                            <i class="fa fa-home"></i> 返回首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../../login.php?action=logout">
                            <i class="fa fa-sign-out"></i> 退出
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="mb-0"><?php echo $stats['teacher_count']; ?></h3>
                                <p class="mb-0">教师总数</p>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-chalkboard-teacher stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="mb-0"><?php echo $stats['student_count']; ?></h3>
                                <p class="mb-0">学生总数</p>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-user-graduate stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="mb-0"><?php echo $stats['course_count']; ?></h3>
                                <p class="mb-0">课程总数</p>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-book stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col">
                                <h3 class="mb-0"><?php echo $stats['classroom_count']; ?></h3>
                                <p class="mb-0">教室总数</p>
                            </div>
                            <div class="col-auto">
                                <i class="fa fa-door-open stat-icon"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-bolt"></i> 快速操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='teacher_form.php'">
                                    <i class="fa fa-plus"></i>
                                    <div>添加教师</div>
                                </div>
                            </div>
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='student_form.php'">
                                    <i class="fa fa-user-plus"></i>
                                    <div>添加学生</div>
                                </div>
                            </div>
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='course_form.php'">
                                    <i class="fa fa-book-open"></i>
                                    <div>创建课程</div>
                                </div>
                            </div>
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='classroom_form.php'">
                                    <i class="fa fa-plus-square"></i>
                                    <div>添加教室</div>
                                </div>
                            </div>
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='../../role_manage.php'">
                                    <i class="fa fa-user-cog"></i>
                                    <div>角色管理</div>
                                </div>
                            </div>
                            <div class="col-md-2 col-6">
                                <div class="quick-action" onclick="location.href='reports.php'">
                                    <i class="fa fa-chart-bar"></i>
                                    <div>统计报告</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近数据 -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-chalkboard-teacher"></i> 最新教师</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_teachers)): ?>
                            <p class="text-muted">暂无教师数据</p>
                        <?php else: ?>
                            <?php foreach ($recent_teachers as $teacher): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong><?php echo safe_html($teacher['name']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo safe_html($teacher['subject']); ?></small>
                                </div>
                                <small class="text-muted">
                                    <?php echo format_time_display($teacher['create_time']); ?>
                                </small>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <div class="text-center mt-3">
                            <a href="teachers.php" class="btn btn-sm btn-outline-primary">查看全部</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-user-graduate"></i> 最新学生</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_students)): ?>
                            <p class="text-muted">暂无学生数据</p>
                        <?php else: ?>
                            <?php foreach ($recent_students as $student): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong><?php echo safe_html($student['name']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo safe_html($student['grade']); ?></small>
                                </div>
                                <small class="text-muted">
                                    <?php echo format_time_display($student['create_time']); ?>
                                </small>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <div class="text-center mt-3">
                            <a href="students.php" class="btn btn-sm btn-outline-primary">查看全部</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-book"></i> 最新课程</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_courses)): ?>
                            <p class="text-muted">暂无课程数据</p>
                        <?php else: ?>
                            <?php foreach ($recent_courses as $course): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong><?php echo safe_html($course['name']); ?></strong>
                                    <br>
                                    <small class="text-muted"><?php echo safe_html($course['teacher_name']); ?></small>
                                </div>
                                <small class="text-muted">
                                    <?php echo format_time_display($course['create_time']); ?>
                                </small>
                            </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        <div class="text-center mt-3">
                            <a href="courses.php" class="btn btn-sm btn-outline-primary">查看全部</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>
