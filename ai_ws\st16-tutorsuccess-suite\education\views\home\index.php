<?php
/**
 * 首页视图
 * 修订日期：2025-01-22
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title ?? '特靠谱教培系统') ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        .logo h2 {
            color: #333;
            font-weight: bold;
            margin: 0;
        }
        .role-selection {
            margin-top: 30px;
        }
        .role-btn {
            width: 100%;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            background: white;
            transition: all 0.3s;
            text-decoration: none;
            color: #333;
            display: block;
        }
        .role-btn:hover {
            border-color: #667eea;
            background: #f8f9ff;
            color: #667eea;
            text-decoration: none;
        }
        .role-btn i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        .role-btn .role-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .role-btn .role-desc {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">
                <i class="fa fa-graduation-cap"></i>
                <h2>特靠谱教培系统</h2>
                <p class="text-muted">专业的教育培训管理平台</p>
            </div>

            <?php if (isset($user) && $user): ?>
                <!-- 已登录用户显示角色选择 -->
                <div class="text-center mb-3">
                    <h5>欢迎，<?= htmlspecialchars($user['name']) ?>！</h5>
                    <p class="text-muted">请选择您的身份</p>
                </div>

                <div class="role-selection">
                    <?php if ($user['role'] === 'admin' || $user['role'] === 'teacher'): ?>
                        <a href="/education.php/teacher/dashboard" class="role-btn">
                            <i class="fa fa-chalkboard-teacher text-success"></i>
                            <div class="role-title">教师端</div>
                            <div class="role-desc">课程管理、请假审批</div>
                        </a>
                    <?php endif; ?>

                    <?php if ($user['role'] === 'admin'): ?>
                        <a href="/education.php/admin/dashboard" class="role-btn">
                            <i class="fa fa-cogs text-primary"></i>
                            <div class="role-title">管理员</div>
                            <div class="role-desc">系统管理、用户管理</div>
                        </a>
                    <?php endif; ?>

                    <?php if ($user['role'] === 'student'): ?>
                        <a href="/education.php/student/dashboard" class="role-btn">
                            <i class="fa fa-user-graduate text-info"></i>
                            <div class="role-title">学生端</div>
                            <div class="role-desc">查看课表、请假申请</div>
                        </a>
                    <?php endif; ?>
                </div>

                <div class="text-center mt-3">
                    <a href="/education.php/user/logout" class="btn btn-outline-secondary">
                        <i class="fa fa-sign-out"></i> 退出登录
                    </a>
                </div>
            <?php else: ?>
                <!-- 未登录用户显示登录选项 -->
                <div class="text-center mb-3">
                    <h5>请登录系统</h5>
                    <p class="text-muted">选择登录方式</p>
                </div>

                <div class="role-selection">
                    <a href="/education.php/user/login" class="role-btn">
                        <i class="fa fa-sign-in text-primary"></i>
                        <div class="role-title">账号登录</div>
                        <div class="role-desc">使用邮箱密码登录</div>
                    </a>

                    <a href="/education.php/user/wxlogin" class="role-btn">
                        <i class="fa fa-wechat text-success"></i>
                        <div class="role-title">微信登录</div>
                        <div class="role-desc">使用微信快速登录</div>
                    </a>
                </div>

                <div class="text-center mt-3">
                    <p class="text-muted small">
                        没有账号？<a href="/education.php/user/register">立即注册</a>
                    </p>
                </div>
            <?php endif; ?>

            <!-- 演示入口 -->
            <div class="text-center mt-4 pt-3 border-top">
                <p class="text-muted small mb-2">快速体验</p>
                <div class="btn-group" role="group">
                    <a href="/education.php/teacher/dashboard" class="btn btn-sm btn-outline-success">教师演示</a>
                    <a href="/education.php/student/dashboard" class="btn btn-sm btn-outline-info">学生演示</a>
                    <a href="/education.php/admin/dashboard" class="btn btn-sm btn-outline-primary">管理员演示</a>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>

