---
type: "always_apply"
---

# 架构与编码原则

> Rule Type: `development`

## 基本原则

在进行架构分析、功能模块分析以及编码时，请遵循以下基本原则：

1. **第一性原理** - 分析问题和技术架构时，追溯到最基本的原理
2. **DRY原则** (Don't Repeat Yourself) - 避免代码重复
3. **KISS原则** (Keep It Simple, Stupid) - 保持设计和实现的简单性
4. **SOLID原则** - 单一职责、开闭原则、里氏替换、接口隔离、依赖倒置
5. **YAGNI原则** (You Aren't Gonna Need It) - 不添加当前不需要的功能

## 代码组织

- 如果单独的类、函数或代码文件超过500行，应进行识别分解和分离
- 在识别、分解、分离的过程中应遵循上述基本原则

## 安全设计

- 使用预处理语句防止SQL注入
- 命令文件动态加载防止未授权访问
- 对所有输入参数进行过滤处理

## 测试友好设计

- 提供完整的网页测试界面
- 支持模拟不同用户和群组
- 实时显示命令返回结果

## 数据库设计

- 使用`_lp.php`统一数据库操作
- 支持查询和执行两种操作模式
- 自动处理数据库连接

## 版本信息

- 版本: 1.0
- 最后更新: 2025.6.14

