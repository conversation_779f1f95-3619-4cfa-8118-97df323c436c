<?php
if ($_REQUEST['token'] != 'password') {
    header('HTTP/1.1 404 Not Found');
    exit;
}
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json");
$prompt = urldecode($_REQUEST['message']);
$modelvalue = $_REQUEST['model'];
$role = $_REQUEST['role'];
require_once ('admin/mysqlconn.php');
$row = $conn->get('model', '*', ['modelvalue' => $modelvalue, 'ORDER' => 'isonline']);
if (empty($row)) {
    echo '{"error":{"code":"model_not_exist","message":"Model ' . $modelvalue . ' does not exist"}}';
    exit;
}
$modelid = $row["id"];
$modeltype = $row["modeltype"];

function getkey()
{
    global $conn, $modelid, $OPENAI_API_KEY, $apiaddress, $apikeyid, $headers;
    $row = $conn->get('apikey', '*', ['isvalid' => true, 'keytype' => $modelid, 'ORDER' => ['lasttime', 'id']]);
    if (empty($row)) {
        echo 'data: {"error":{"code":"no_valid_apikey","message":""}}' . "\n\n";
        exit;
    }
    $OPENAI_API_KEY = $row["apikey"];
    $apiaddress = $row["apiaddress"];
    $apikeyid = $row["id"];
    $conn->update('apikey', ['lasttime' => date('Y-m-d H:i:s')], ['id' => $apikeyid]);

    $headers = [
        'Accept: application/json',
        'Content-Type: application/json',
        'Authorization: Bearer ' . $OPENAI_API_KEY
    ];
}

getkey();
$proxyaddress = $conn->get('main', 'proxyaddress', ['id' => 1]);

$url = 'http://' . $_SERVER['HTTP_HOST'] . '/websearchapi.php?message=' . $_REQUEST['message'];
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
    $url = str_replace('http://', 'https://', $url);
}

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
$responsedata = curl_exec($ch);
curl_close($ch);
$jsondata = json_decode($responsedata);
if ($jsondata->code == 200) {
    $prompt = $jsondata->online;
}

$context = json_decode($_REQUEST['context'] ?: "[]") ?: [];
$postdata = [
    "model" => $modelvalue,
    "temperature" => 0,
    "stream" => false,
    "messages" => [],
];

if (!empty($role)) {
    $postdata['messages'][] = ['role' => 'system', 'content' => $role];
}

if (!empty($context)) {
    $context = array_slice($context, -5);
    foreach ($context as $message) {
        $postdata['messages'][] = ['role' => 'user', 'content' => urldecode($message[0])];
        $postdata['messages'][] = ['role' => 'assistant', 'content' => urldecode($message[1])];
    }
}
$postdata['messages'][] = ['role' => 'user', 'content' => $prompt];
$postdata = json_encode($postdata);


function getbaiduaccesscode()
{
    global $conn, $apiaddress, $OPENAI_API_KEY, $apikeyid;
    $apikey = explode(",", $OPENAI_API_KEY);
    $newtoken = json_decode(file_get_contents($apiaddress . "/oauth/2.0/token?grant_type=client_credentials&client_id=" . $apikey[0] . "&client_secret=" . $apikey[1]))->access_token;
    $conn->update('apikey', ['apikey' => $apikey[0] . "," . $apikey[1] . "," . $newtoken], ['id' => $apikeyid]);
    return $newtoken;
}

if ($modeltype == "文心千帆") {
    $headers = [
        'Accept: application/json',
        'Content-Type: application/json'
    ];

    $postdatajson = json_decode($postdata);
    $postdatajson->temperature = null;
    $postdata = json_encode($postdatajson);
    $apikeyarray = explode(",", $OPENAI_API_KEY);
    if (count($apikeyarray) == 2) {
        $accesstoken = getbaiduaccesscode();
    } else {
        $accesstoken = $apikeyarray[2];
    }
}

function go()
{
    global $modeltype, $modelvalue, $apiaddress, $headers, $postdata, $proxyaddress, $accesstoken;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);

    if ($modeltype == "文心千帆") {
        if ($modelvalue == "ERNIE-Bot") {
            curl_setopt($ch, CURLOPT_URL, $apiaddress . '/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions?access_token=' . $accesstoken);
        } else if ($modelvalue == "ERNIE-Bot-turbo") {
            curl_setopt($ch, CURLOPT_URL, $apiaddress . '/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=' . $accesstoken);
        }
    } else {
        curl_setopt($ch, CURLOPT_URL, $apiaddress . '/v1/chat/completions');
        if (!empty($proxyaddress)) {
            curl_setopt($ch, CURLOPT_PROXY, $proxyaddress);
        }
    }
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300); // 设置连接超时时间为300秒
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3); // 设置最大重定向次数为3次
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // 允许自动重定向
    curl_setopt($ch, CURLOPT_AUTOREFERER, true); // 自动设置Referer
    $responsedata = curl_exec($ch);
    curl_close($ch);
    return $responsedata;
}
function retrygo()
{
    global $conn, $firsterror, $apikeyid, $accesstoken;
    $data = go();
    $complete = json_decode($data);
    if (isset($complete->error)) {
        $errcode = $complete->error->code;
        $errmsg = $complete->error->message;
        if (strpos($errmsg, "Rate limit reached") === 0) { //访问频率超限错误返回的code为空，特殊处理一下
            $errcode = "rate_limit_reached";
        }
        if (strpos($errmsg, "Your access was terminated") === 0) { //违规使用，被封禁，特殊处理一下
            $errcode = "access_terminated";
        }
        if (strpos($errmsg, "You didn't provide an API key") === 0) { //未提供API-KEY
            $errcode = "no_api_key";
        }
        if (strpos($errmsg, "You exceeded your current quota") === 0) { //API-KEY余额不足
            $errcode = "insufficient_quota";
        }
        if (strpos($errmsg, "That model is currently overloaded") === 0) { //OpenAI模型超负荷
            $errcode = "model_overloaded";
        }
        if (strpos($errmsg, "The server had an error") === 0) { //OpenAI服务器超负荷
            $errcode = "server_overloaded";
        }
        if (strpos($errmsg, "Your account is not active") === 0) { //账户被封禁
            $errcode = "account_deactivated";
        }
        if (($errcode == "access_terminated") || ($errcode == "insufficient_quota") || ($errcode == "invalid_api_key") || ($errcode == "account_deactivated")) {
            $conn->update('apikey', ['isvalid' => 0, 'lasttime' => date('Y-m-d H:i:s'), 'errmsg' => addslashes($errcode . "|" . $errmsg)], ['id' => $apikeyid]);
            getkey();
            retrygo();
        } else {
            echo '{"error":{"code":"' . $errcode . '","message":"' . $errmsg . '"}}';
        }
    } else if (isset($complete->error_code)) {
        $errcode = $complete->error_code;
        $errmsg = $complete->error_msg;
        if (($errcode == 110) && ($firsterror)) {
            $accesstoken = getbaiduaccesscode();
            $firsterror = false;
            echo go();
        }
    } else {
        echo $data;
    }
}
retrygo();

