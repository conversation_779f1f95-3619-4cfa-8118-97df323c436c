<?php

/**
 * 读取配置
 * @param string $name 配置项
 * @return mixed 配置值
 */
function config($name)
{
    static $config = null;
    if (!$config) {
        //$config = require P_ROOT. DS.'common/config.php';
        if(file_exists(P_ROOT. DS.'common/config.local.php')){
            $config = require P_ROOT. DS.'common/config.local.php';
        }else{
            $config = require P_ROOT. DS.'common/config.php';   
        }
    }

    return isset($config[$name]) ? $config[$name] : '';
}

/**
 * 接收输入的函数
 * @param array $method 输入的数组（可用字符串get、post来表示）
 * @param string $name 从数组中取出的变量名
 * @param string $type 表示类型的字符串
 * @param mixed $default 变量不存在时使用的默认值
 * @return mixed 返回的结果
 */
function input($method, $name, $type = 's', $default = '')
{
    switch ($method) {
        case 'get': $method = $_GET; break;
        case 'post': $method = $_POST; break;
        case 'cookie': $method = $_COOKIE; break;
        case 'file': $method = $_FILES; break;
    }
    $data = isset($method[$name]) ? $method[$name] : $default;
    switch ($type) {
        case 's': return is_string($data) ? $data : $default;
        case 'd': return (int) $data;
        case 'b': return (bool) $data;
        case 'a': return is_array($data) ? $data : [];
        case 'f': return (float) $data;
        default: trigger_error('不存在的过滤类型“' . $type . '”');
    }
}

/**
 * 重定向并停止脚本
 * @param string $url 目标URL
 */
function redirect($url)
{
    header("Location: $url");
    exit;
}

/**
 * 生成分页导航HTML
 * @param string $url 链接地址
 * @param int $total 总记录数
 * @param int $page 当前页码值
 * @param int $size 每页显示的条数
 * @param string $anchor 锚点
 * @return string 生成的HTML结果
 */
function page_html($url, $total, $page, $size, $anchor = '')
{
    // 计算总页数
    if(empty($size) || intval($size)<5){
        $size=5;
    }
    $maxpage = max(ceil($total / $size), 1);
    // 如果不足2页，则不显示分页导航
    if ($maxpage <= 1) {
        return '';
    }
    if ($page == 1) {
        $first = '<span>首页</span>';
        $prev = '<span>上一页</span>';
    } else {
        $first = "<a href=\"{$url}1$anchor\">首页</a>";
        $prev = '<a href="' . $url . ($page - 1) . $anchor.  '">上一页</a>';
    }
    if ($page == $maxpage) {
        $next = '<span>下一页</span>';
        $last = '<span>尾页</span>';
    } else {
        $next = '<a href="' . $url . ($page + 1) . $anchor . '">下一页</a>';
        $last = "<a href=\"{$url}{$maxpage}{$anchor}\">尾页</a>";
    }
    // 组合最终样式
    return "$first $prev $next $last";
}
/**
 * 获取SQL中的分页部分
 * @param int $page 当前页码值
 * @param int $size 每页显示的条数
 * @return string 拼接后的结果
 */
function page_sql($page, $size)
{
    if(empty($size) || intval($size)<1){
        $size=5;
    }
    return ($page - 1) * $size . ',' . $size;
}

/**
 * 生成缩略图（最大裁剪）
 * @param string $file 原图的路径
 * @param string $save 缩略图的保存路径
 * @param int $limit 缩略图的边长（像素）
 * @return bool 成功返回true，失败返回false
 */
function thumb($file, $save, $limit)
{
    $func = [
        'image/png' => function ($file, $img = null) {
            return $img ? imagepng($img, $file) : imagecreatefrompng($file);
        },
        'image/jpeg' => function ($file, $img = null) {
            return $img ? imagejpeg($img, $file, 100) : imagecreatefromjpeg($file);
        }
    ];
    $info = getimagesize($file);
    list($width, $height) = $info;
    $mime = $info['mime'];
    if (!in_array($mime, ['image/png', 'image/jpeg'])) {
        trigger_error('创建缩略图失败，不支持的图片类型。', E_USER_WARNING);
        return false;
    }
    $img = $func[$mime]($file);
    if ($width > $height) {
        $size = $height;
        $x = (int) (($width - $height) / 2);
        $y = 0;
    } else {
        $size = $width;
        $x = 0;
        $y = (int) (($height - $width) / 2);
    }
    $thumb = imagecreatetruecolor($limit, $limit);
    imagecopyresampled($thumb, $img, 0, 0, $x, $y, $limit, $limit, $size, $size);
    return $func[$mime]($save, $thumb);
}

/**
 * 密码加密
 * @param string $password 密码原文
 * @param string $salt 密钥
 * @return string 加密后的MD5值
 */
function password($password, $salt)
{
    return md5(md5($password) . $salt);
}

/**
 * 生成令牌
 * @return string 生成结果
 */
function token_get(){
    if(isset($_SESSION[SESSION_KEY]['token'])){
        $token = $_SESSION[SESSION_KEY]['token'];
    }else{
        $token = md5(microtime(true));
        $_SESSION[SESSION_KEY]['token'] = $token;
    }
    return $token;
}

/**
 * 验证令牌
 * @param string $token 待验证的令牌
 * @return bool 验证结果
 */
function token_check($token){
    return token_get() === $token;
}

/**
 * 将验证码保存到Session
 * @param string $code 待保存验证码
 */
function captcha_save($code)
{
    $_SESSION[SESSION_KEY]['captcha'] = $code;
}

/**
 * 对验证码进行验证
 * @param string $code 输入验证码
 * @return bool 验证结果
 */
function captcha_check($code)
{
    $captcha = isset($_SESSION[SESSION_KEY]['captcha']) ? $_SESSION[SESSION_KEY]['captcha'] : '';
    if (!empty($captcha)) {
        unset($_SESSION[SESSION_KEY]['captcha']);               // 清除验证码，防止重复验证
        return strtoupper($captcha) == strtoupper($code); // 不区分大小写
    }
    return false;
}

function input_check($field, $data, &$msg = '')
{
    switch ($field) {
        case 'user_name':
            $msg = '2~10位中文、字母、数字、下划线。';
            return (bool)preg_match('/^[\w\x{4E00}-\x{9FA5}]{2,10}$/u', $data);
        case 'user_password':
            $msg = '6~12位字母、数字、下划线。';
            return (bool)preg_match('/^\w{6,12}$/', $data);
        case 'user_email':
            return (bool)preg_match('/^(\w+(\_|\-|\.)*)+@(\w+(\-)?)+(\.\w{2,})+$/', $data);
        case 'post_video':
            $domain = parse_url($data);
            return isset($domain['host']) && in_array(strtolower($domain['host']), config('APP_VIDEO_ALLOW'));
        default:
            $msg = '4~32位中文、字母、数字、下划线。';
            return (bool)preg_match('/^[\w\x{4E00}-\x{9FA5}]{4,32}$/u', $data);
    }
}

function user($name, $user = null)
{
    static $data = null;
    if($user){
        $data = $user;
        return false;
    }

    return isset($data[$name]) ? $data[$name] : false ;
}

function autologin_cookie($data)
{
    $time = time();
    return $time . '|' . password($data, $time . config('AUTOLOGIN_SERCET'));
}

function autologin_check($cookie, $data)
{
    $arr = explode('|', $cookie, 2);
    return isset($arr[1]) && (time() - config('AUTOLOGIN_EXPIRES') < (int)$arr[0]) && (password($data, $arr[0] . config('AUTOLOGIN_SERCET')) == $arr[1]);
}

function category_list()
{
    static $data = [];
    return $data ?: ($data = Db::getInstance()->fetchAll('SELECT `id`,`name`,`cover`,`sort` FROM ks1_post_category ORDER BY `sort` ASC'));
}

function get_category_list()
{
    static $data = [];
    return $data ?: ($data = Db::getInstance()->fetchAll('SELECT `id`,`name`,`cover`,`sort` FROM __CATEGORY__ ORDER BY `sort` ASC'));
}

function get_venues_list()
{
    static $data = [];
    return $data ?: ($data = Db::getInstance()->fetchAll('SELECT `id`,`name`,`cover`,`sort` FROM ks1_venues ORDER BY `sort` ASC'));
}

// 确保缓存目录存在
function ensureCacheDirExists() {
    $cacheDir = CACHE_ROOT;
    if (!file_exists($cacheDir)) {
        // 尝试创建目录，权限设置为 0755
        if (!mkdir($cacheDir, 0755, true)) {
            // 如果创建失败，输出错误信息
            die('Unable to create cache directory: ' . $cacheDir);
        }
    }
}

function get_trpg_activity_suggestions_list()
{
    static $data = [];
    return $data ?: ($data = Db::getInstance()->fetchAll('SELECT * FROM trpg_activity_suggestions 
        WHERE date_time_start > NOW( ) 
        ORDER BY `date_time_start` ASC limit 30 '));
}

function get_his_trpg_activity_suggestions_list()
{
    static $data = [];
    return $data ?: ($data = Db::getInstance()->fetchAll('SELECT * FROM trpg_activity_suggestions 
        WHERE date_time_start <= NOW( ) 
        ORDER BY `date_time_start` DESC limit 30 '));
}

function get_trpg_activity_suggestions_list_max30($limit=1)
{
    static $data = [];
    if(empty($data)){
        if (empty($limit) ){
            $max=1;
        }else{
            $max=$limit;
        }
        if ($max>30){
            $max=30;
        }
        // 查询列表
        $sql = 'SELECT
        tas.id,
        tas.participant_id,
        tas.event_title,
        tas.activity_suggestion_content,
        tas.type_event,
        tas.date_time_start,
        tas.date_time_end,
        tas.about_event_hosting,
        tas.time_zone,
        tas.created_at,
        tas.status,
        tas.registered_participants_count,
        tas.category,
        tas.venues,
        pc.name,
        pc.email,
        pc.phone_number
        FROM
        trpg_activity_suggestions AS tas
        INNER JOIN trpg_participants AS pc ON tas.participant_id = pc.id
        WHERE
        tas.date_time_start > NOW( )
        ORDER BY
        tas.date_time_start ASC
        limit '.intval($max);
        $activity_list = get_data($sql);

        // 查询结果为空时，虚构一个返回邀请创建活动建议
        if (empty($activity_list)) {
            $activity_list = array(
                array(
                    'id' => '',
                    'participant_id' => '',
                    'event_title' => '虚构活动',
                    'activity_suggestion_content' => '虚构活动建议内容',
                    'type_event' => '虚构活动类型',
                    'date_time_start' => '2024-12-01 10:00:00',
                    'date_time_end' => '2024-12-01 12:00:00',
                    'about_event_hosting' => '虚构活动描述',
                    'time_zone' => 'Asia/Shanghai',
                    'created_at' => '2024-11-27 14:35:36',
                    'status' => 'pending',
                    'registered_participants_count' => '3',
                    'category' => '自由活动',
                    'venues' => '住宅小区',
                    'name' => '虚构参与者',
                    'email' => '<EMAIL>',
                    'phone_number' => '1234567890'
                )
            );
        }

    }

    $data=$activity_list;
    return $data;
}



function get_trpg_activity_suggestions_list_page($page, $limit_xp)
{
    if (empty($limit_xp) ){
        $max=1;
    }else{
        $max=$limit_xp;
    }
    if ($max>5){
        $max=5;
    }
    // 查询列表
    $sql = 'SELECT
    tas.id,
    tas.participant_id,
    tas.event_title,
    tas.activity_suggestion_content,
    tas.type_event,
    tas.date_time_start,
    tas.date_time_end,
    tas.about_event_hosting,
    tas.time_zone,
    tas.created_at,
    tas.status,
    tas.registered_participants_count,
    tas.category,
    tas.venues,
    pc.name,
    pc.email,
    pc.phone_number
    FROM
    trpg_activity_suggestions AS tas
    INNER JOIN trpg_participants AS pc ON tas.participant_id = pc.id
    WHERE
    tas.date_time_start > NOW( )
    ORDER BY
    tas.date_time_start ASC
    limit '.$limit_xp;
    $activity_list = get_data($sql); 


    $data=$activity_list;
    return $data;
}


function get_trpg_activity_suggestions_list_demo()
{
    // 查询结果为空时，虚构一个返回邀请创建活动建议

    $activity_list = array(
        array(
            'id' => '',
            'participant_id' => '',
            'event_title' => '虚构活动',
            'activity_suggestion_content' => '虚构活动建议内容',
            'type_event' => '虚构活动类型',
            'date_time_start' => '2024-12-01 10:00:00',
            'date_time_end' => '2024-12-01 12:00:00',
            'about_event_hosting' => '虚构活动描述',
            'time_zone' => 'Asia/Shanghai',
            'created_at' => '2024-11-27 14:35:36',
            'status' => 'pending',
            'registered_participants_count' => '3',
            'category' => '自由活动',
            'venues' => '住宅小区',
            'name' => '虚构参与者',
            'email' => '<EMAIL>',
            'phone_number' => '1234567890'
        )
    );
        
    $data=$activity_list;
    return $data;
}

function format_activity_dates($start_date, $end_date) {
    // 将时间转换为DateTime对象
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);

    // 检查是否是今年
    $now = new DateTime();
    $start_year = $start->format('Y');
    $end_year = $end->format('Y');
    $now_year = $now->format('Y');

    // 如果两个日期都是今年，移除年份
    if ($start_year == $now_year && $end_year == $now_year) {
        $start_date = $start->format('m-d');
        $end_date = $end->format('m-d');
    } else {
        // 否则，保留完整的日期格式
        $start_date = $start->format('Y-m-d');
        $end_date = $end->format('Y-m-d');
    }

    // 如果开始和结束日期是同一天
    if ($start_date == $end_date) {
        // 格式化为 yyyy-mm-dd 时:分1 到 时:分2
        $formatted_start = $start->format('H:i');
        $formatted_end = $end->format('H:i');
        return $start_date . ' ' . $formatted_start . ' 到 ' . $formatted_end;
    }

    // 否则，显示两个完整的日期
    return $start_date . ' - ' . $end_date;
}
/*
// 测试函数
$act_row = array(
    'date_time_start' => '2024-11-27 14:00:00',
    'date_time_end' => '2024-11-27 18:00:00'
);

echo format_activity_dates($act_row['date_time_start'], $act_row['date_time_end']);
*/


// 检查域名是否包含特定参数的函数
function check_domain($domain) {
    return stripos($_SERVER['HTTP_HOST'], $domain) !== false;
}

//ks1_api_keys

function sql_check_api_keys($id, $token) {
    $sql = "SELECT * FROM ks1_api_keys WHERE api_id = ?s";
    $ret = get_line(prepare($sql, array($id)));
    if($ret && isset($ret['api_token']) && !empty($ret['api_token']) ){
    	if( strtolower(trim($ret['api_token'])) ==strtolower(trim($token)) ) {
    		return true;
    	}
    }
    return false;
}

function validateParams($id, $token) {
    // 这里添加你的验证逻辑，例如检查数据库中是否存在对应的id和token
    // 以下是一个示例，你需要根据实际情况来实现验证逻辑
    // $isValid = checkDatabaseForIdAndToken($id, $token);
    // return $isValid;
    if(empty($id) || empty($token) ){
    	return false;
    }
    $id2=trim($id);
    $token2=trim($token);
    if(empty($id2) || empty($token2) ){
    	return false;
    }

    if(strlen($id2)<16 || strlen($token2)<32 ){
    	return false;
    }

	$isValid = sql_check_api_keys($id2, $token2);
	return $isValid;    
}