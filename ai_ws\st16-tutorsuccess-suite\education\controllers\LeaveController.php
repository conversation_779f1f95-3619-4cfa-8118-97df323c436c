<?php
/**
 * 请假控制器
 * 处理请假相关的请求
 * 修订日期：2025-01-22
 */
class LeaveController extends Controller {
    /**
     * 请假模型
     * @var LeaveModel
     */
    private $leaveModel;
    
    /**
     * 教师模型
     * @var TeacherModel
     */
    private $teacherModel;
    
    /**
     * 学生模型
     * @var StudentModel
     */
    private $studentModel;
    
    /**
     * 课程模型
     * @var CourseModel
     */
    private $courseModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        
        // 初始化模型
        $this->leaveModel = new LeaveModel();
        $this->teacherModel = new TeacherModel();
        $this->studentModel = new StudentModel();
        $this->courseModel = new CourseModel();
        
        // 检查登录状态
        $this->checkLogin();
    }
    
    /**
     * 检查登录状态
     */
    private function checkLogin() {
        if (!isset($_SESSION['user'])) {
            $this->redirect('user/login');
        }
    }
    
    /**
     * 请假列表页面
     */
    public function index() {
        // 获取当前页码
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $perPage = 10;
        
        // 获取过滤条件
        $filters = [];
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $filters['status'] = $_GET['status'];
        }
        
        // 根据用户角色设置过滤条件
        $user = $_SESSION['user'];
        if ($user['role'] === 'teacher') {
            // 教师只能看到自己课程的请假申请
            $teacher = $this->teacherModel->getTeacherByUserId($user['id']);
            if ($teacher) {
                $filters['teacher_id'] = $teacher['id'];
            }
        } elseif ($user['role'] === 'student') {
            // 学生只能看到自己的请假申请
            $student = $this->studentModel->getStudentByUserId($user['id']);
            if ($student) {
                $filters['student_id'] = $student['id'];
            }
        }
        
        // 获取请假列表
        $leaves = $this->leaveModel->getLeaves($page, $perPage, $filters);
        
        // 获取请假总数
        $total = $this->leaveModel->getLeaveCount($filters);
        
        // 计算总页数
        $totalPages = ceil($total / $perPage);
        
        // 渲染视图
        $this->render('index', [
            'leaves' => $leaves,
            'page' => $page,
            'perPage' => $perPage,
            'total' => $total,
            'totalPages' => $totalPages,
            'filters' => $filters
        ]);
    }
    
    /**
     * 添加请假申请页面
     */
    public function add() {
        // 只有学生可以添加请假申请
        if ($_SESSION['user']['role'] !== 'student') {
            $this->redirect('error/forbidden');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentByUserId($_SESSION['user']['id']);
        if (!$student) {
            $this->redirect('error/notFound');
        }
        
        // 获取学生的课程列表
        $courses = $this->studentModel->getStudentCourses($student['id']);
        
        // 渲染视图
        $this->render('add', [
            'student' => $student,
            'courses' => $courses
        ]);
    }
    
    /**
     * 处理添加请假申请请求
     */
    public function doAdd() {
        // 只有学生可以添加请假申请
        if ($_SESSION['user']['role'] !== 'student') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentByUserId($_SESSION['user']['id']);
        if (!$student) {
            $this->json(null, 404, '学生信息不存在');
        }
        
        // 获取表单数据
        $courseId = $this->post('course_id');
        $scheduleId = $this->post('schedule_id');
        $startDate = $this->post('start_date');
        $endDate = $this->post('end_date');
        $reason = $this->post('reason');
        
        // 验证数据
        if (empty($courseId) || empty($startDate) || empty($endDate) || empty($reason)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 验证日期
        if (strtotime($startDate) > strtotime($endDate)) {
            $this->json(null, 400, '开始日期不能晚于结束日期');
        }
        
        // 检查是否存在冲突的请假申请
        if ($this->leaveModel->hasConflictingLeave($student['id'], $courseId, $startDate, $endDate)) {
            $this->json(null, 400, '该时间段已有请假申请');
        }
        
        try {
            // 创建请假申请
            $leaveId = $this->leaveModel->createLeave([
                'student_id' => $student['id'],
                'course_id' => $courseId,
                'schedule_id' => $scheduleId ?: null,
                'reason' => $reason,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => 'pending'
            ]);
            
            // 返回成功响应
            $this->json(['id' => $leaveId], 200, '请假申请提交成功');
        } catch (Exception $e) {
            // 返回错误响应
            $this->json(null, 500, '提交请假申请失败：' . $e->getMessage());
        }
    }
    
    /**
     * 查看请假详情
     * @param int $id 请假ID
     */
    public function view($id) {
        // 获取请假信息
        $leave = $this->leaveModel->getLeaveById($id);
        
        // 检查请假是否存在
        if (!$leave) {
            $this->redirect('error/notFound');
        }
        
        // 检查权限
        $user = $_SESSION['user'];
        $hasPermission = false;
        
        if ($user['role'] === 'admin') {
            $hasPermission = true;
        } elseif ($user['role'] === 'teacher') {
            $teacher = $this->teacherModel->getTeacherByUserId($user['id']);
            if ($teacher && $leave['teacher_id'] == $teacher['id']) {
                $hasPermission = true;
            }
        } elseif ($user['role'] === 'student') {
            $student = $this->studentModel->getStudentByUserId($user['id']);
            if ($student && $leave['student_id'] == $student['id']) {
                $hasPermission = true;
            }
        }
        
        if (!$hasPermission) {
            $this->redirect('error/forbidden');
        }
        
        // 渲染视图
        $this->render('view', ['leave' => $leave]);
    }
    
    /**
     * 审批请假申请
     * @param int $id 请假ID
     */
    public function approve($id) {
        // 只有教师和管理员可以审批
        if (!in_array($_SESSION['user']['role'], ['admin', 'teacher'])) {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取请假信息
        $leave = $this->leaveModel->getLeaveById($id);
        if (!$leave) {
            $this->json(null, 404, '请假申请不存在');
        }
        
        // 检查状态
        if ($leave['status'] !== 'pending') {
            $this->json(null, 400, '该请假申请已被处理');
        }
        
        // 检查权限（教师只能审批自己课程的请假）
        if ($_SESSION['user']['role'] === 'teacher') {
            $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
            if (!$teacher || $leave['teacher_id'] != $teacher['id']) {
                $this->json(null, 403, '权限不足');
            }
        }
        
        // 获取审批意见
        $comment = $this->post('comment', '');
        
        try {
            // 审批请假申请
            $success = $this->leaveModel->approveLeave($id, 'approved', $_SESSION['user']['id'], $comment);
            
            if ($success) {
                $this->json(null, 200, '请假申请已批准');
            } else {
                $this->json(null, 500, '审批失败');
            }
        } catch (Exception $e) {
            $this->json(null, 500, '审批失败：' . $e->getMessage());
        }
    }
    
    /**
     * 拒绝请假申请
     * @param int $id 请假ID
     */
    public function reject($id) {
        // 只有教师和管理员可以审批
        if (!in_array($_SESSION['user']['role'], ['admin', 'teacher'])) {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取请假信息
        $leave = $this->leaveModel->getLeaveById($id);
        if (!$leave) {
            $this->json(null, 404, '请假申请不存在');
        }
        
        // 检查状态
        if ($leave['status'] !== 'pending') {
            $this->json(null, 400, '该请假申请已被处理');
        }
        
        // 检查权限（教师只能审批自己课程的请假）
        if ($_SESSION['user']['role'] === 'teacher') {
            $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
            if (!$teacher || $leave['teacher_id'] != $teacher['id']) {
                $this->json(null, 403, '权限不足');
            }
        }
        
        // 获取拒绝原因
        $comment = $this->post('comment', '');
        if (empty($comment)) {
            $this->json(null, 400, '请填写拒绝原因');
        }
        
        try {
            // 拒绝请假申请
            $success = $this->leaveModel->approveLeave($id, 'rejected', $_SESSION['user']['id'], $comment);
            
            if ($success) {
                $this->json(null, 200, '请假申请已拒绝');
            } else {
                $this->json(null, 500, '操作失败');
            }
        } catch (Exception $e) {
            $this->json(null, 500, '操作失败：' . $e->getMessage());
        }
    }
}
