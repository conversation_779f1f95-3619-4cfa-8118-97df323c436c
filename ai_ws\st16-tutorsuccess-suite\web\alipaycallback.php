<?php
require_once('admin/mysqlconn.php');
use Medoo\Medoo;
$alipayPublicKey = $conn->get('main','alipaypublickey',['id'=>1]);
$aliPay = new AlipayService($alipayPublicKey);
$result = $aliPay->rsaCheck($_POST, $_POST['sign_type']);
if ($result === true && $_POST['trade_status'] == 'TRADE_SUCCESS') {
    $row = $conn->get('alipaylist','*',['out_trade_no'=>$_POST["out_trade_no"]]);
    if (empty($row)) {
        echo 'error';
    } else {
        echo "success";
        if (!$row["isconfirmed"]) {
            $userid = $row["userid"];
            $alipaylistid = $row["id"];
            $row = $conn->get('cardtype','*',['id'=>$row["cardtype"]]);
            $quota = $row["quota"];
            $extenddays = $row["extenddays"];
            $conn->update('alipaylist',['isconfirmed'=>1,'confirmtime'=>date('Y-m-d H:i:s')],['out_trade_no'=>$_POST["out_trade_no"]]);
            $row = $conn->get('user','*',['userid'=>$userid]);
            if (strtotime($row["expiretime"]) < strtotime(date("Y-m-d H:i:s"))) {
                // ???
                // $sql = "UPDATE user SET quota = quota + " . $quota . ",expiretime = DATE_ADD(CURDATE(), INTERVAL " . $extenddays . " DAY) WHERE userid = '" . $userid . "'";
                $data = ['quota[+]' => $quota,'expiretime' => Medoo::raw("DATE_ADD(CURDATE(), INTERVAL {$extenddays} DAY)")];
            } else {
                // ???
                // $sql = "UPDATE user SET quota = quota + " . $quota . ",expiretime = DATE_ADD(expiretime, INTERVAL " . $extenddays . " DAY) WHERE userid = '" . $userid . "'";
                $data = ['quota[+]' => $quota,'expiretime' => Medoo::raw("DATE_ADD(expiretime, INTERVAL {$extenddays} DAY)")];
            }
            // ???
            // $result = $conn->query($sql);
            $conn->update('user',$data,['userid'=>$userid]);
            $conn->insert('rechargelog',['userid'=>$userid,'quota'=>$quota,'extenddays'=>$extenddays,'rechargetime'=>date('Y-m-d H:i:s'),'alipaylistid'=>$alipaylistid]);
        }
    }
    exit;
}
echo 'error';
exit();
class AlipayService
{
    //支付宝公钥
    protected $alipayPublicKey;
    protected $charset;

    public function __construct($alipayPublicKey)
    {
        $this->charset = 'utf8';
        $this->alipayPublicKey = $alipayPublicKey;
    }

    /**
     *  验证签名
     **/
    public function rsaCheck($params)
    {
        $sign = $params['sign'];
        $signType = $params['sign_type'];
        unset($params['sign_type']);
        unset($params['sign']);
        return $this->verify($this->getSignContent($params), $sign, $signType);
    }

    function verify($data, $sign, $signType = 'RSA')
    {
        $pubKey = $this->alipayPublicKey;
        $res = "-----BEGIN PUBLIC KEY-----\n" .
            wordwrap($pubKey, 64, "\n", true) .
            "\n-----END PUBLIC KEY-----";
        ($res) or die('支付宝RSA公钥错误。请检查公钥文件格式是否正确');

        //调用openssl内置方法验签，返回bool值
        if ("RSA2" == $signType) {
            $result = (bool)openssl_verify($data, base64_decode($sign), $res, version_compare(PHP_VERSION, '5.4.0', '<') ? SHA256 : OPENSSL_ALGO_SHA256);
        } else {
            $result = (bool)openssl_verify($data, base64_decode($sign), $res);
        }
        //        if(!$this->checkEmpty($this->alipayPublicKey)) {
        //            //释放资源
        //            openssl_free_key($res);
        //        }
        return $result;
    }

    /**
     * 校验$value是否非空
     *  if not set ,return true;
     *    if is null , return true;
     **/
    protected function checkEmpty($value)
    {
        if (!isset($value))
            return true;
        if ($value === null)
            return true;
        if (trim($value) === "")
            return true;
        return false;
    }

    public function getSignContent($params)
    {
        ksort($params);
        $stringToBeSigned = "";
        $i = 0;
        foreach ($params as $k => $v) {
            if (false === $this->checkEmpty($v) && "@" != substr($v, 0, 1)) {
                // 转换成目标字符集
                $v = $this->characet($v, $this->charset);
                if ($i == 0) {
                    $stringToBeSigned .= "$k" . "=" . "$v";
                } else {
                    $stringToBeSigned .= "&" . "$k" . "=" . "$v";
                }
                $i++;
            }
        }
        unset($k, $v);
        return $stringToBeSigned;
    }

    /**
     * 转换字符集编码
     * @param $data
     * @param $targetCharset
     * @return string
     */
    function characet($data, $targetCharset)
    {
        if (!empty($data)) {
            $fileType = $this->charset;
            if (strcasecmp($fileType, $targetCharset) != 0) {
                $data = mb_convert_encoding($data, $targetCharset, $fileType);
                //$data = iconv($fileType, $targetCharset.'//IGNORE', $data);
            }
        }
        return $data;
    }
}
