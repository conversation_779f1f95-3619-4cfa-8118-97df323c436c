---
type: "always_apply"
---

# 权限系统设计

> Rule Type: `security`

## 分层权限系统

系统采用三级权限结构，确保安全和灵活的访问控制：

### SA (超级管理员)

- 添加和管理所有用户 (admin/user)
- 分配群组可见性和管理权限
- 设置群组命令权限
- 查看所有统计报告

### Admin (管理员)

- 添加和管理 user 类型用户
- 在分配的群组内管理用户可见性
- 查看所管理群组的统计报告

### User (普通用户)

- 在可见群组内执行允许的命令
- 查看个人命令历史

## 细粒度权限控制

系统实现了多层次的细粒度权限控制机制：

1. **群组可见性控制**
   - SA 指定 admin 可查看/管理的群组
   - 确保信息隔离和安全管理

2. **命令权限控制**
   - SA 为每个群组指定可用命令
   - 限制用户可执行的操作范围

3. **用户管理范围**
   - Admin 只能管理自己添加的用户
   - 确保管理职责边界清晰

## 权限验证流程

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  用户请求   │ ──→ │ 身份验证    │ ──→ │ 权限检查    │
└─────────────┘      └─────────────┘      └─────────────┘
                                                 │
                                                 ↓
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  返回结果   │ ←── │ 执行操作    │ ←── │ 记录日志    │
└─────────────┘      └─────────────┘      └─────────────┘
```

## 权限检查表

| 操作类型 | SA | Admin | User |
|---------|-------|--------|------|
| 用户管理 | 所有用户 | 自己创建的用户 | 无权限 |
| 群组管理 | 所有群组 | 分配的群组 | 无权限 |
| 命令执行 | 所有命令 | 分配的命令 | 允许的命令 |
| 报表查看 | 所有报表 | 管理群组报表 | 个人报表 |

## 实现示例

```php
// 权限检查示例
function check_permission($user_id, $group_id, $command) {
    // 获取用户类型
    $user_type = get_user_type($user_id);
    
    // SA拥有所有权限
    if ($user_type === 'SA') {
        return true;
    }
    
    // 检查群组可见性
    if (!check_group_visibility($user_id, $group_id)) {
        return false;
    }
    
    // 检查命令权限
    if (!check_command_permission($user_id, $group_id, $command)) {
        return false;
    }
    
    return true;
}
```

## 版本信息

- 版本: 1.0
- 最后更新: 2025.6.14

