<?php
/**
 * 教培系统基础配置文件
 * 创建时间：2025-01-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

// 教培系统版本
define('EDU_VERSION', '1.0.0');

// 教培系统根目录
define('EDU_ROOT', dirname(__DIR__) . '/');

// 教培系统URL根目录
define('EDU_URL', '/ks1cck2/education/');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 系统配置
$edu_config = [
    // 系统基本信息
    'system' => [
        'name' => '特靠谱教培系统',
        'version' => EDU_VERSION,
        'description' => '课外补习班管理平台',
        'admin_email' => '<EMAIL>'
    ],
    
    // 课程配置
    'course' => [
        'max_students_default' => 20,  // 默认最大学生数
        'schedule_advance_days' => 7,  // 课程安排提前天数
        'leave_advance_hours' => 2     // 请假提前小时数
    ],
    
    // 时间配置
    'time' => [
        'class_periods' => [
            '08:00-09:30' => '第一节',
            '09:45-11:15' => '第二节',
            '13:30-15:00' => '第三节',
            '15:15-16:45' => '第四节',
            '19:00-20:30' => '晚课一',
            '20:45-22:15' => '晚课二'
        ],
        'week_days' => [
            1 => '周一',
            2 => '周二',
            3 => '周三',
            4 => '周四',
            5 => '周五',
            6 => '周六',
            7 => '周日'
        ]
    ],
    
    // 文件上传配置
    'upload' => [
        'max_size' => 5 * 1024 * 1024,  // 5MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'upload_path' => EDU_ROOT . 'uploads/'
    ],
    
    // 分页配置
    'pagination' => [
        'per_page' => 20,
        'per_page_options' => [10, 20, 50, 100]
    ]
];

/**
 * 获取配置值
 * @param string $key 配置键，支持点号分隔的多级键
 * @param mixed $default 默认值
 * @return mixed
 */
function edu_config($key, $default = null) {
    global $edu_config;
    
    $keys = explode('.', $key);
    $value = $edu_config;
    
    foreach ($keys as $k) {
        if (isset($value[$k])) {
            $value = $value[$k];
        } else {
            return $default;
        }
    }
    
    return $value;
}

/**
 * 获取当前学期
 * @return string
 */
function get_current_semester() {
    $year = date('Y');
    $month = date('n');
    
    if ($month >= 9 || $month <= 1) {
        // 秋季学期：9月-次年1月
        if ($month >= 9) {
            return $year . '年秋季学期';
        } else {
            return ($year - 1) . '年秋季学期';
        }
    } else {
        // 春季学期：2月-7月
        return $year . '年春季学期';
    }
}

/**
 * 格式化时间显示
 * @param string $time
 * @return string
 */
function format_time_display($time) {
    if (empty($time)) return '';
    
    $timestamp = is_numeric($time) ? $time : strtotime($time);
    $now = time();
    $diff = $now - $timestamp;
    
    if ($diff < 60) {
        return '刚刚';
    } elseif ($diff < 3600) {
        return floor($diff / 60) . '分钟前';
    } elseif ($diff < 86400) {
        return floor($diff / 3600) . '小时前';
    } elseif ($diff < 604800) {
        return floor($diff / 86400) . '天前';
    } else {
        return date('Y-m-d H:i', $timestamp);
    }
}

/**
 * 生成随机字符串
 * @param int $length
 * @return string
 */
function generate_random_string($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}

/**
 * 安全的HTML输出
 * @param string $string
 * @return string
 */
function safe_html($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 检查是否为AJAX请求
 * @return bool
 */
function is_ajax_request() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * JSON响应
 * @param array $data
 * @param int $code
 * @param string $message
 */
function json_response($data = [], $code = 200, $message = 'success') {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'code' => $code,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
