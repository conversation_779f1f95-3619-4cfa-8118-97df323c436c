<?php
date_default_timezone_set('Asia/Shanghai');
//session_set_cookie_params(0, null, null, null, true);
mb_internal_encoding('UTF-8');
require P_ROOT. 'common/function.php';
require P_ROOT. 'common/library/Db.php';
require P_ROOT. 'common/library/SessionDb.php';
require P_ROOT. 'common/comm.inc.php';
require P_ROOT. 'common/api_sql.php';
require P_ROOT. 'common/ks1_func.php';


if (DEBUG_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
	ini_set('display_errors', 'Off'); // 关闭错误显示
	ini_set('log_errors', 'On'); // 开启错误日志
	// 获取当前日期并格式化为 YYYYMMDD 的形式
	$today_date = date('Ymd');
	// 构建日志文件的完整路径，每天一个文件
	$logFile = ERROR_LOGS . 'error_' . $today_date . '.log';
	// 设置错误日志文件路径
	ini_set('error_log', $logFile);
}

// 你的代码
/*
2dac707e-cec7-4cad-955f-8a75ef370a3a
c5faf3a4-9a2a-48be-b6c6-1f58bb24e0f4
a0399ff0-8456-4107-872e-a2abe2f1b36f
*/
define('SESSION_KEY', 'ks'.crc32(__DIR__));

// 检查是否有表单提交
define('IS_POST', ($_POST || $_FILES));
// 检查用户登录

@session_start();

define('IS_LOGIN', isset($_SESSION[SESSION_KEY]['user']['id']));

// 生成和验证CSRF令牌
define('TOKEN', token_get());

if(!defined('IN_API') && !defined('IN_LOGIN')){

	if((IS_POST || isset($_GET['action'])) && !token_check(input('get', 'token', 's'))) {
		exit('操作失败：非法令牌。');
	}

}

// 如果用户已经登录，取出用户信息
IS_LOGIN && user(null, [
    'user_id' => $_SESSION[SESSION_KEY]['user']['id'] ?? null,
    'user_name' => $_SESSION[SESSION_KEY]['user']['name'] ?? null,
    'user_email' => $_SESSION[SESSION_KEY]['user']['email'] ?? null,
    'group_id' => $_SESSION[SESSION_KEY]['user']['group_id'] ?? null,
    'group' => $_SESSION[SESSION_KEY]['user']['group'] ?? null
]);
// 当前用户是否为管理员
define('IS_ADMIN', IS_LOGIN && user('group') == 'admin');

$category = [];
$web_hot_link=[];


// 调用函数确保缓存目录存在
ensureCacheDirExists();

