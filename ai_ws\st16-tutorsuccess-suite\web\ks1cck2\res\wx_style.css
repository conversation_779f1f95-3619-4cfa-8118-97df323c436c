/* 基础样式 */
body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    font-size: 16px; /* 调整字体大小，更适合移动端 */
}

/* 标题样式 */
h1, h2 {
    font-size: 20px; /* 调整标题字体大小 */
    text-align: center;
    color: #333;
    margin: 10px 0;
}

/* 表单样式 */
form {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px auto;
    width: 90%; /* 调整表单宽度，使其更适合移动端 */
}

input[type="text"], button {
    padding: 12px; /* 增加内边距，提高触感 */
    margin: 10px 0;
    width: 100%; /* 调整宽度为100%，适应表单宽度 */
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px; /* 调整字体大小 */
}

button {
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
    width: 100%; /* 调整按钮宽度为100%，适应表单宽度 */
}

button:hover {
    background-color: #0056b3;
}

/* 列表样式 */
ul {
    list-style: none;
    padding: 0;
    margin: 0 auto;
    width: 90%; /* 调整列表宽度，使其更适合移动端 */
}

li {
    background-color: white;
    padding: 15px; /* 增加内边距，提高触感 */
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* 链接样式 */
a {
    color: #007bff;
    text-decoration: none;
    display: inline-block; /* 使链接可以设置内边距 */
    padding: 5px 10px; /* 增加内边距，提高触感 */
}

a:hover {
    text-decoration: underline;
}

/* 增加媒体查询，优化小屏幕设备 */
@media (max-width: 768px) {
    body {
        font-size: 14px; /* 在小屏幕上进一步调整字体大小 */
    }

    h1, h2 {
        font-size: 18px; /* 在小屏幕上进一步调整标题字体大小 */
    }

    input[type="text"], button {
        padding: 10px; /* 在小屏幕上调整内边距 */
        font-size: 14px; /* 在小屏幕上调整字体大小 */
    }

    li {
        padding: 10px; /* 在小屏幕上调整内边距 */
    }

    a {
        padding: 3px 6px; /* 在小屏幕上调整内边距 */
    }
}


.red-txt {
    color: #f00;
}

.view-details-layer {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.view-details-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    width: 80%;
    max-height: 80%;
    overflow-y: auto;
}

.view-details-close {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
}