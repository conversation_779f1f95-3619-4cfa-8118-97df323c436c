<?php
header("X-Accel-Buffering: no");
set_time_limit(0);
require_once('admin/mysqlconn.php');
$row = $conn->get('asr','*',['id'=>1]);
$asrmodel = $row["asrmodel"];
$openaiapikey = $row["openaiapikey"];
$openaiapiaddress = $row["openaiapiaddress"];

if ($_FILES['file']) {
    $filename = $_FILES['file']['tmp_name'];
    $model = 'whisper-1';

    $ch = curl_init();

    $file_to_upload = new CURLFile($filename, 'audio/wav');

    $data = array('file' => $file_to_upload, 'model' => $model, 'language' => 'zh');

    curl_setopt($ch, CURLOPT_URL, $openaiapiaddress);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

    $headers = array();
    $headers[] = 'Authorization: Bearer ' . $openaiapikey;
    $headers[] = 'Content-Type: multipart/form-data';
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        echo curl_error($ch);
    } else {
        require_once('simpleChineseTranscoder.php');
        $transcoder = new simpleChineseTranscoder;
        echo $transcoder->big5_gb($result);
    }
    curl_close($ch);
} else {
    echo "No file uploaded.";
}
