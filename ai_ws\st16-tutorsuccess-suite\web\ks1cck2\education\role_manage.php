<?php
/**
 * 用户角色管理页面
 * 创建时间：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../_ks1.php';
require_once '../mysqlconn.php';
require_once 'init.php';

// 检查管理员权限
require_permission(ROLE_ADMIN, '../login.php');

$message = '';
$error = '';

// 处理角色分配
if ($_POST) {
    $user_id = intval($_POST['user_id'] ?? 0);
    $role_type = intval($_POST['role_type'] ?? 0);
    $action = $_POST['action'] ?? '';
    
    if ($user_id > 0 && in_array($role_type, [ROLE_ADMIN, ROLE_TEACHER, ROLE_STUDENT])) {
        if ($action === 'add') {
            // 添加角色
            $sql = "INSERT IGNORE INTO " . TABLE_USER_ROLE . " (user_id, role_type) VALUES (?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ii', $user_id, $role_type);
            
            if ($stmt->execute()) {
                $message = '角色分配成功！';
                log_operation('role_assign', "为用户ID {$user_id} 分配角色 " . get_role_name($role_type));
            } else {
                $error = '角色分配失败：' . $conn->error;
            }
        } elseif ($action === 'remove') {
            // 移除角色
            $sql = "DELETE FROM " . TABLE_USER_ROLE . " WHERE user_id = ? AND role_type = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ii', $user_id, $role_type);
            
            if ($stmt->execute()) {
                $message = '角色移除成功！';
                log_operation('role_remove', "移除用户ID {$user_id} 的角色 " . get_role_name($role_type));
            } else {
                $error = '角色移除失败：' . $conn->error;
            }
        }
    } else {
        $error = '参数错误！';
    }
}

// 获取所有用户及其角色
$sql = "SELECT u.userid, u.username, u.email, 
        GROUP_CONCAT(ur.role_type) as roles
        FROM user u 
        LEFT JOIN " . TABLE_USER_ROLE . " ur ON u.userid = ur.user_id 
        WHERE u.isforbidden = 0
        GROUP BY u.userid 
        ORDER BY u.userid";
$result = $conn->query($sql);
$users = [];
if ($result) {
    while ($row = $result->fetch_assoc()) {
        $row['role_list'] = $row['roles'] ? explode(',', $row['roles']) : [];
        $users[] = $row;
    }
}

$page_title = '用户角色管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 20px 0;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
        .role-badge {
            margin: 2px;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="container main-container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fa fa-users"></i> <?php echo $page_title; ?>
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <?php echo safe_html($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <?php echo safe_html($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>用户ID</th>
                                        <th>用户名</th>
                                        <th>邮箱</th>
                                        <th>当前角色</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['userid']; ?></td>
                                        <td><?php echo safe_html($user['username']); ?></td>
                                        <td><?php echo safe_html($user['email']); ?></td>
                                        <td>
                                            <?php if (empty($user['role_list'])): ?>
                                                <span class="badge bg-secondary">无角色</span>
                                            <?php else: ?>
                                                <?php foreach ($user['role_list'] as $role): ?>
                                                    <span class="badge bg-primary role-badge">
                                                        <?php echo get_role_name($role); ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <!-- 管理员角色 -->
                                                <?php if (!in_array(ROLE_ADMIN, $user['role_list'])): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_ADMIN; ?>">
                                                    <input type="hidden" name="action" value="add">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                            onclick="return confirm('确定要分配管理员角色吗？')">
                                                        +管理员
                                                    </button>
                                                </form>
                                                <?php else: ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_ADMIN; ?>">
                                                    <input type="hidden" name="action" value="remove">
                                                    <button type="submit" class="btn btn-danger btn-sm" 
                                                            onclick="return confirm('确定要移除管理员角色吗？')">
                                                        -管理员
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                                
                                                <!-- 教师角色 -->
                                                <?php if (!in_array(ROLE_TEACHER, $user['role_list'])): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_TEACHER; ?>">
                                                    <input type="hidden" name="action" value="add">
                                                    <button type="submit" class="btn btn-outline-success btn-sm">
                                                        +教师
                                                    </button>
                                                </form>
                                                <?php else: ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_TEACHER; ?>">
                                                    <input type="hidden" name="action" value="remove">
                                                    <button type="submit" class="btn btn-success btn-sm">
                                                        -教师
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                                
                                                <!-- 学生角色 -->
                                                <?php if (!in_array(ROLE_STUDENT, $user['role_list'])): ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_STUDENT; ?>">
                                                    <input type="hidden" name="action" value="add">
                                                    <button type="submit" class="btn btn-outline-info btn-sm">
                                                        +学生
                                                    </button>
                                                </form>
                                                <?php else: ?>
                                                <form method="post" style="display: inline;">
                                                    <input type="hidden" name="user_id" value="<?php echo $user['userid']; ?>">
                                                    <input type="hidden" name="role_type" value="<?php echo ROLE_STUDENT; ?>">
                                                    <input type="hidden" name="action" value="remove">
                                                    <button type="submit" class="btn btn-info btn-sm">
                                                        -学生
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="mt-3">
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> 返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>
