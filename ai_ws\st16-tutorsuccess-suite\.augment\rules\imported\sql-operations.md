---
type: "always_apply"
---

# SQL操作规范

> Rule Type: `database`

## 目录
- [基本原则](mdc:#基本原则)
- [表名处理规则](mdc:#表名处理规则)
- [SQL参数绑定规则](mdc:#sql参数绑定规则)
- [SQL执行规则](mdc:#sql执行规则)
- [SQL执行流程](mdc:#sql执行流程)
- [SQL语句格式规则](mdc:#sql语句格式规则)
- [防SQL注入规则](mdc:#防sql注入规则)
- [错误处理规则](mdc:#错误处理规则)
- [性能优化指南](mdc:#性能优化指南)
- [测试环境规则](mdc:#测试环境规则)

## 基本原则

- Web目录下的.PHP文件中，引用`_lp.php`获得数据库定义和操作函数
- 保持SQL语句的简洁性和可读性
- 所有数据库操作必须通过统一接口进行，避免直接使用原生SQL
- 具体数据库定义在 web\php\common\config.php 文件中，测试也使用这个开发环境地址，禁止编造
```
    // 数据库连接配置
    'DB_CONNECT' => [
        'host' => 'localhost',
        'user' => 'pubhelper4dev',
        'pass' => 'pubhelper4dev',
        'dbname' => 'pubhelper4dev',
        'port' => '3306'
    ],
```

## 表名处理规则

1. 使用`get_table_name()`函数获取表名，避免硬编码表名.
get_table_name函数定义在 web\php\func.inc.php 中
2. 表名参数使用单引号，如: `get_table_name('users')`
3. 在SQL语句中使用变量形式的表名: `$table_name`或更具描述性的`$users_table`

### 示例

```php
// 正确示例
$users_table = get_table_name('users');
$sql = "SELECT * FROM $users_table WHERE id = ?i";

// 错误示例
$sql = "SELECT * FROM users WHERE id = ?i"; // 硬编码表名
```

## SQL参数绑定规则

1. 使用`prepare()`函数处理所有SQL语句和参数
2. 参数类型标记：
   - `?s` - 字符串类型
   - `?i` - 整数类型
   - `?d` - 浮点数类型
   - `?a` - 数组类型（自动转换为逗号分隔列表）
   - `?u` - 无转义字符串（仅用于特殊情况）

### 示例

```php
// 正确的参数绑定
$sql = "SELECT * FROM $users_table WHERE username = ?s AND status = ?i";
$sql = prepare($sql, [$username, $status]);

// 多参数绑定
$sql = "INSERT INTO $logs_table (user_id, action, timestamp) VALUES (?i, ?s, ?i)";
$sql = prepare($sql, [$user_id, $action, time()]);
```

## SQL执行规则

1. 使用`get_data()`函数获取多行数据
2. 使用`get_line()`函数获取单行数据
3. 使用`get_var()`函数获取单个值
4. 使用`run_sql()`函数执行写操作

### 示例

```php
// 获取多行数据
$users = get_data("SELECT * FROM $users_table WHERE group_id = ?i", [$group_id]);

// 获取单行数据
$user = get_line("SELECT * FROM $users_table WHERE id = ?i", [$user_id]);

// 获取单个值
$count = get_var("SELECT COUNT(*) FROM $users_table WHERE status = ?i", [1]);

// 执行写操作
$result = run_sql("UPDATE $users_table SET status = ?i WHERE id = ?i", [1, $user_id]);
```

## SQL执行流程

1. 获取表名: `$table_name = get_table_name('表名');`
2. 编写SQL语句: `$sql = "SELECT * FROM $table_name WHERE condition = ?s";`
3. 绑定参数：`$sql = prepare($sql, [$param1, $param2]);`
4. 执行查询：`$result = get_data($sql);` 或 `$result = get_line($sql);` 或 `$result = get_var($sql);` 或 `$result = run_sql($sql);`

## SQL语句格式规则

1. 对于简单查询，SQL可以写在一行
2. 对于复杂查询，使用多行格式并适当缩进
3. JOIN语句或子查询应当保持良好缩进以提高可读性

### 示例

```php
// 简单查询 - 单行
$sql = "SELECT id, username FROM $users_table WHERE status = ?i";

// 复杂查询 - 多行
$sql = "SELECT u.id, u.username, g.name AS group_name 
        FROM $users_table AS u
        JOIN $groups_table AS g ON u.group_id = g.id
        WHERE u.status = ?i 
        AND g.active = ?i
        ORDER BY u.username ASC";
```

## 防SQL注入规则

1. 所有用户输入必须通过`prepare()`函数处理
2. 不得在SQL语句中直接拼接变量
3. 表名虽通过`get_table_name()`获取，但仍需注意安全

### 示例

```php
// 安全的方式
$sql = "SELECT * FROM $users_table WHERE username = ?s";
$sql = prepare($sql, [$_POST['username']]);

// 不安全的方式 - 禁止使用
$sql = "SELECT * FROM $users_table WHERE username = '" . $_POST['username'] . "'";
```

## 错误处理规则

1. 所有数据库操作应包含在try-catch块中
2. 记录数据库错误但不向用户展示详细错误信息
3. 返回用户友好的错误消息

### 示例

```php
try {
    $result = run_sql("UPDATE $users_table SET status = ?i WHERE id = ?i", [1, $user_id]);
    if (!$result) {
        throw new Exception("更新用户状态失败");
    }
    return ['status' => 'success'];
} catch (Exception $e) {
    error_log("数据库错误: " . $e->getMessage());
    return ['status' => 'error', 'message' => '操作失败，请稍后再试'];
}
```

## 性能优化指南

1. 仅查询需要的列，避免`SELECT *`
2. 为频繁查询的列添加索引
3. 使用EXPLAIN分析查询性能
4. 对大结果集使用分页
5. 考虑使用缓存减少数据库负载

### 示例

```php
// 优化前
$users = get_data("SELECT * FROM $users_table");

// 优化后 - 只选择需要的列
$users = get_data("SELECT id, username, email FROM $users_table");

// 分页示例
$page = max(1, intval($_GET['page']));
$limit = 20;
$offset = ($page - 1) * $limit;
$users = get_data("SELECT id, username FROM $users_table LIMIT ?i OFFSET ?i", [$limit, $offset]);
```

## 测试环境规则

- 所有测试目的的.php脚本，应该存在`test`目录下
- 测试脚本应复用`test_lp.php`中的数据库定义和操作函数
- 测试数据库应与生产数据库分离

## 版本信息

- 版本: 1.0
- 最后更新: 2025.6.14
