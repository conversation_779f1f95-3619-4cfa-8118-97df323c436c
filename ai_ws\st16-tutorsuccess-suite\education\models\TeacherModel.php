<?php
/**
 * 教师模型
 * 处理教师数据的操作
 */
class TeacherModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'teachers';
    
    /**
     * 根据ID获取教师信息
     * @param int $id 教师ID
     * @return array|null 教师信息
     */
    public function getTeacherById($id) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 根据用户ID获取教师信息
     * @param int $userId 用户ID
     * @return array|null 教师信息
     */
    public function getTeacherByUserId($userId) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.user_id = ?";
        return $this->db->selectOne($sql, [$userId]);
    }
    
    /**
     * 获取教师列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教师列表
     */
    public function getTeachers($page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                ORDER BY t.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql);
    }
    
    /**
     * 获取教师总数
     * @return int 教师总数
     */
    public function getTeacherCount() {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table}");
    }
    
    /**
     * 创建教师
     * @param array $data 教师数据
     * @return int 新教师的ID
     */
    public function createTeacher($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新教师信息
     * @param int $id 教师ID
     * @param array $data 教师数据
     * @return bool 是否成功
     */
    public function updateTeacher($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除教师
     * @param int $id 教师ID
     * @return bool 是否成功
     */
    public function deleteTeacher($id) {
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 根据科目获取教师列表
     * @param string $subject 科目
     * @return array 教师列表
     */
    public function getTeachersBySubject($subject) {
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE t.subject = ? 
                ORDER BY t.id DESC";
        return $this->db->select($sql, [$subject]);
    }
    
    /**
     * 搜索教师
     * @param string $keyword 关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 教师列表
     */
    public function searchTeachers($keyword, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $keyword = "%{$keyword}%";
        $sql = "SELECT t.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} t 
                JOIN users u ON t.user_id = u.id 
                WHERE u.name LIKE ? OR t.subject LIKE ? OR t.title LIKE ? 
                ORDER BY t.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取搜索结果总数
     * @param string $keyword 关键词
     * @return int 结果总数
     */
    public function getSearchCount($keyword) {
        $keyword = "%{$keyword}%";
        $sql = "SELECT COUNT(*) FROM {$this->table} t
                JOIN users u ON t.user_id = u.id
                WHERE u.name LIKE ? OR t.subject LIKE ? OR t.title LIKE ?";
        return $this->db->count($sql, [$keyword, $keyword, $keyword]);
    }

    /**
     * 获取教师的课表
     * @param int $teacherId 教师ID
     * @param string $date 日期 (Y-m-d)，为空则获取本周
     * @return array 课表列表
     */
    public function getTeacherSchedule($teacherId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        // 获取本周的开始和结束日期
        $weekStart = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $weekEnd = date('Y-m-d', strtotime('sunday this week', strtotime($date)));

        $sql = "SELECT s.*, c.name as course_name, c.description as course_description,
                       cr.name as classroom_name, cr.location as classroom_location,
                       COUNT(cs.student_id) as student_count
                FROM schedules s
                JOIN courses c ON s.course_id = c.id
                JOIN teachers t ON c.teacher_id = t.id
                LEFT JOIN classrooms cr ON s.classroom_id = cr.id
                LEFT JOIN course_student cs ON c.id = cs.course_id AND cs.status = 'active'
                WHERE t.id = ? AND c.status = 'active'
                AND c.start_date <= ? AND (c.end_date IS NULL OR c.end_date >= ?)
                GROUP BY s.id
                ORDER BY s.day_of_week, s.start_time";
        return $this->db->select($sql, [$teacherId, $weekEnd, $weekStart]);
    }

    /**
     * 获取教师今日课程
     * @param int $teacherId 教师ID
     * @param string $date 日期 (Y-m-d)
     * @return array 今日课程列表
     */
    public function getTodaySchedule($teacherId, $date = null) {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $dayOfWeek = date('N', strtotime($date)); // 1-7 (Monday-Sunday)

        $sql = "SELECT s.*, c.name as course_name, c.description as course_description,
                       cr.name as classroom_name, cr.location as classroom_location,
                       COUNT(cs.student_id) as student_count
                FROM schedules s
                JOIN courses c ON s.course_id = c.id
                JOIN teachers t ON c.teacher_id = t.id
                LEFT JOIN classrooms cr ON s.classroom_id = cr.id
                LEFT JOIN course_student cs ON c.id = cs.course_id AND cs.status = 'active'
                WHERE t.id = ? AND s.day_of_week = ? AND c.status = 'active'
                AND c.start_date <= ? AND (c.end_date IS NULL OR c.end_date >= ?)
                GROUP BY s.id
                ORDER BY s.start_time";
        return $this->db->select($sql, [$teacherId, $dayOfWeek, $date, $date]);
    }

    /**
     * 获取教师统计数据
     * @param int $teacherId 教师ID
     * @return array 统计数据
     */
    public function getTeacherStats($teacherId) {
        $stats = [];

        // 本周课程数
        $weekStart = date('Y-m-d', strtotime('monday this week'));
        $weekEnd = date('Y-m-d', strtotime('sunday this week'));

        $sql = "SELECT COUNT(DISTINCT s.id) as week_courses
                FROM schedules s
                JOIN courses c ON s.course_id = c.id
                JOIN teachers t ON c.teacher_id = t.id
                WHERE t.id = ? AND c.status = 'active'
                AND c.start_date <= ? AND (c.end_date IS NULL OR c.end_date >= ?)";
        $result = $this->db->selectOne($sql, [$teacherId, $weekEnd, $weekStart]);
        $stats['week_courses'] = $result['week_courses'] ?? 0;

        // 学生总数
        $sql = "SELECT COUNT(DISTINCT cs.student_id) as total_students
                FROM course_student cs
                JOIN courses c ON cs.course_id = c.id
                JOIN teachers t ON c.teacher_id = t.id
                WHERE t.id = ? AND cs.status = 'active' AND c.status = 'active'";
        $result = $this->db->selectOne($sql, [$teacherId]);
        $stats['total_students'] = $result['total_students'] ?? 0;

        // 待审批请假数
        $sql = "SELECT COUNT(*) as pending_leaves
                FROM leaves l
                JOIN courses c ON l.course_id = c.id
                JOIN teachers t ON c.teacher_id = t.id
                WHERE t.id = ? AND l.status = 'pending'";
        $result = $this->db->selectOne($sql, [$teacherId]);
        $stats['pending_leaves'] = $result['pending_leaves'] ?? 0;

        // 平均出勤率（简化计算，这里可以根据实际需求调整）
        $stats['attendance_rate'] = 92; // 暂时使用固定值，实际应该根据出勤记录计算

        return $stats;
    }

    /**
     * 获取教师的课程列表
     * @param int $teacherId 教师ID
     * @param string $status 课程状态
     * @return array 课程列表
     */
    public function getTeacherCourses($teacherId, $status = 'active') {
        $sql = "SELECT c.*, COUNT(cs.student_id) as student_count
                FROM courses c
                LEFT JOIN course_student cs ON c.id = cs.course_id AND cs.status = 'active'
                WHERE c.teacher_id = ?";

        $params = [$teacherId];

        if ($status) {
            $sql .= " AND c.status = ?";
            $params[] = $status;
        }

        $sql .= " GROUP BY c.id ORDER BY c.created_at DESC";

        return $this->db->select($sql, $params);
    }

    /**
     * 获取教师的学生列表
     * @param int $teacherId 教师ID
     * @return array 学生列表
     */
    public function getTeacherStudents($teacherId) {
        $sql = "SELECT DISTINCT s.*, u.name, u.email, u.phone, u.avatar,
                       GROUP_CONCAT(c.name SEPARATOR ', ') as course_names
                FROM students s
                JOIN users u ON s.user_id = u.id
                JOIN course_student cs ON s.id = cs.student_id
                JOIN courses c ON cs.course_id = c.id
                WHERE c.teacher_id = ? AND cs.status = 'active' AND c.status = 'active'
                GROUP BY s.id
                ORDER BY u.name";
        return $this->db->select($sql, [$teacherId]);
    }
}