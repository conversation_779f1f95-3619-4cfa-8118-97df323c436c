<?php
set_time_limit(0);
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);
require_once('check_admin.php');
require_once('mysqlconn.php');
$row = $conn->get('model', '*', ['ORDER' => ['sequenceid', 'id']]);
if (empty($result)) {
    $lastsequenceid = 0;
} else {
    $lastsequenceid = $row["sequenceid"];
    if (empty($lastsequenceid)) {
        $lastsequenceid = $row["id"];
    }
}
if ((isset($_POST["action"])) && ($_POST["action"] == "addnew")) {
    $conn->insert('model', ['modeltype' => $_POST["modeltype"], 'sequenceid' => $_POST["sequenceid"], 'modelname' => $_POST["modelname"], 'modelvalue' => $_POST["modelvalue"], 'modelprice' => $_POST["modelprice"], 'isvalid' => $_POST["isvalid"], 'isimage' => $_POST["isimage"], 'istranslate' => $_POST["istranslate"], 'isonline' => $_POST["isonline"], 'createtime' => date('Y-m-d H:i:s'), 'memo' => $_POST["memo"]]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('添加成功！');parent.location.href='modelconfig.php';</script>";
    exit;
}
if ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    if ($conn->has('model', ['id' => $_GET['id']])) {
        $conn->delete('model', ['id' => $_GET['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='modelconfig.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('模型不存在！');parent.location.reload();</script>";
    }
    exit;
}
if ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    if ($conn->has('model', ['id' => $_POST['id']])) {
        $conn->update('model', ['modeltype' => $_POST['modeltype'], 'modelname' => $_POST['modelname'], 'modelvalue' => $_POST['modelvalue'], 'sequenceid' => $_POST['sequenceid'], 'modelprice' => $_POST['modelprice'], 'isvalid' => $_POST['isvalid'], 'isimage' => $_POST['isimage'], 'istranslate' => $_POST['istranslate'], 'isonline' => $_POST['isonline'], 'memo' => $_POST['memo']], ['id' => $_POST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("模型不存在！");parent.location.reload();</script>';
    }
    exit;
}
?>

<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>模型配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
            padding: 1px 4px;
            height: 23px;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }

        div.mycollapse {
            overflow: hidden;
            white-space: normal;
            text-overflow: ellipsis;
            width: 100%;
            height: 40px;
        }

        div.myexpand {
            white-space: normal;
            width: 100%;
            height: auto;
        }

        table th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) { }
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">模型配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <div class="row">
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div class="mycollapse" id="info"
                        style="border-radius: 10px;padding:10px 20px;line-height:20px;margin-bottom:20px;border:1px solid silver;">
                        <button onclick="toggleCollapse(this)"
                            style="float:right;font-weight:bold;padding:0 10px;margin-top:-3px;">点击展开模型配置说明</button>
                        <p>模型类型为“OpenAI官方”指的是官方接口及兼容的接口规范。如果使用类似<a target="_blank"
                                href="https://github.com/songquanpeng/one-api">one-api</a>之类的聚合转发接口生成的APIKEY，则所有模型都选择OpenAI官方类型即可。
                        </p>
                        <p>模型类型为“免费国外接口”用的是免费的第三方模型。注册和使用方式请参考<a target="_blank"
                                href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=106">论坛相关教程</a>。</p>
                        <p>模型类型为MidJourney和StableDiffusion时，模型参数值不能修改，模型名称可以随意写。环境配置请参考<a target="_blank"
                                href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=81">论坛相关教程</a></p>
                        <p>模型参数值代表了每个模型调用接口时使用的模型名，区分大小写，注意不要写错。模型名称可以随意设置，不影响使用。</p>
                        <p>目前已确认支持的模型参数值如下。（OpenAI、Claude账号可以在交流群<a target="_blank"
                                href="https://ad.ipfei.cn">商户平台</a>购买，国内大模型申请地址请查看<a target="_blank"
                                href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=116">论坛相关教程</a>）<br>
                            <b>国外模型：</b><br>
                            OpenAI官方：gpt-3.5-turbo-0125、gpt-3.5-turbo-instruct（擅长执行具体指令，无上下文）、gpt-4-turbo-preview（最具性价比）、gpt-4-0125-preview等、dall-e-3（画图）、gpt-4-vision-preview（识图）、dall-e-2（改图）；<br>
                            由于使用官方APIKEY成本和门槛都很高，大多数用户都是购买第三方接口的额度来调用gpt-4等模型，并且第三方接口可以支持某些官方没有的模型，如gpt-4-all，gpts上的应用gpt-4-gizmo-xxx等。推荐一个<a
                                target="_blank"
                                href="https://mvtw4457uos.feishu.cn/docx/HxB6dqzPDozjE8xribScGUtGnBh">群友运营的店铺</a>。<br>
                            Claude官方：claude-3-opus-20240229、claude-3-sonnet-20240229、claude-2.1、claude-2.0、claude-instant-1.2（Claude的所有模型均包含文本对话及识图能力）；<br>
                            <a target="_blank"
                                href="https://ai.google.dev/?hl=zh-cn">谷歌Bard</a>：gemini-1.5-flash、gemini-1.5-pro、gemini-1.5-flash（识图）；<br>
                            <b>国内模型：</b><br>
                            <a target="_blank"
                                href="https://cloud.baidu.com/survey/qianfan.html">文心千帆</a>：ERNIE-4.0-8K、ERNIE-3.5-8K、ERNIE-Speed-128K（免费）、ERNIE-Lite-8K（免费）等文本大模型，每个模型使用前需要先<a
                                target="_blank"
                                href="https://console.bce.baidu.com/qianfan/chargemanage/list">点击这里开通权限</a>、fuyu_8b（识图）、Stable-Diffusion-XL（画图）；<br>
                            <a target="_blank"
                                href="https://dashscope.console.aliyun.com/model">通义千问</a>：qwen-turbo、qwen-vl-plus（识图）、wanx-v1（画图）、stable-diffusion-xl（画图）、stable-diffusion-v1.5（画图）；<br>
                            <a target="_blank"
                                href="https://cloud.tencent.com/product/hunyuan">腾讯混元</a>：hunyuan-pro、hunyuan-standard、hunyuan-lite、hunyuan-code（写代码）、hunyuan-role、hunyuan-vision（识图）、TextToImageLite（画图））；<br>
                            <a target="_blank" href="https://open.bigmodel.cn/overview">清华智谱</a>：glm-4-0520、glm-4
                            、glm-4-air、glm-4-airx、glm-4-long、 glm-4-flash、codegeex-4（写代码）、glm-4v（识图）、cogview-3（画图）；<br>
                            <a target="_blank" href="https://xinghuo.xfyun.cn/">讯飞星火</a>：Spark Lite、Spark Pro、Spark
                            Max、图片理解、图片生成，参数值随便写，靠API地址区分；<br>
                            <a target="_blank"
                                href="https://ai.360.cn/open">360智脑</a>：360gpt2-pro、360gpt-turbo-responsibility-8k、360gpt-pro、360gpt-turbo、360CV_S0_V5（画图）；<br>
                            <a target="_blank"
                                href="https://platform.baichuan-ai.com/console/authentication">百川智能</a>：Baichuan4、Baichuan3-Turbo、Baichuan3-Turbo-128k、Baichuan2-Turbo、Baichuan2-Turbo-192k；<br>
                            <a target="_blank"
                                href="https://platform.moonshot.cn/">月之暗面</a>：moonshot-v1-8k、moonshot-v1-32k、moonshot-v1-128k；<br>
                            <a target="_blank"
                                href="https://www.volcengine.com/product/doubao">火山方舟（豆包）</a>：Doubao-lite-4k、Doubao-lite-32k、Doubao-lite-128k、Doubao-pro-4k、Doubao-pro-32k、Doubao-pro-128k；<br>
                            <a target="_blank"
                                href="https://platform.minimaxi.com/">MiniMax</a>：abab6.5s、abab5.5s、abab5.5；<br>
                            <a target="_blank"
                                href="https://platform.lingyiwanwu.com/">零一万物</a>：yi-large、yi-large-turbo、yi-medium、yi-spark、yi-vision（识图）；<br>
                            <a target="_blank"
                                href="https://www.deepseek.com/">DeepSeek</a>：deepseek-chat、deepseek-coder；<br>
                            <a target="_blank"
                                href="https://cloud.infini-ai.com/platform/ai">无问芯穹</a>：mt-infini-3b、baichuan2-13b-chat、qwen2-72b-chat、glm-4-9b-chat、yi-34b-chat；<br>
                            <a target="_blank" href="https://siliconflow.cn/zh-cn/">硅基流动</a>：超多免费和收费的第三方模型，<a
                                target="_blank"
                                href="https://siliconflow.cn/zh-cn/maaspricing#siliconcloud">点击这里查看</a>。模型参数值样例：deepseek-ai/DeepSeek-V2-Chat<br>
                            <a target="_blank"
                                href="https://docs.link-ai.tech">LinkAI</a>：这是一个第三方知识库网站，请将app_code写到模型参数后面，如gpt-3.5-turbo,cJslRJSo；<br>
                            本地知识库：模型参数值填写知识库ID即可，如：asst_eduOuMQnER4nFKyDdCJo7Wur
                        </p>
                        <p>模型开启联网搜索后，每次请求该模型前会将搜索引擎（目前只支持duckduckgo）返回的前3条结果作为上下文提问，建议采用tokens较大的模型开启联网功能。</p>
                        <p>模型开启提问翻译后，每次请求该模型前会先调用百度翻译将提问翻译成英文，多用于画图模型。此选项生效的前提是“提问翻译配置”已经配置好并开启。</p>
                    </div>
                    <button class=" btn btn-sm btn-info" style="padding:2px 10px;margin-bottom:10px;"
                        onclick="$('#addnew').show();">添加模型</button>
                    <div style="margin:20px 0;display:none;" id="addnew">
                        <form method=post name=addtype target="temp" onsubmit="return checkform();">
                            <input name="action" value="addnew" type=hidden>
                            <table>
                                <tr>
                                    <td style="text-align:right">
                                        <p>是否前端展示：</p>
                                    </td>
                                    <td>
                                        <p><input type=checkbox id="isvalidcheck" checked
                                                onchange='var checkbox=document.getElementById("isvalid");if (this.checked) {checkbox.value = "1";} else {checkbox.value = "0";}'><input
                                                id="isvalid" value=1 type=hidden name=isvalid></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>画图模型配置：</p>
                                    </td>
                                    <td>
                                        <p>
                                            <select id="isimage" name=isimage>
                                                <option value=0 selected>文本模型</option>
                                                <option value=1>仅画图</option>
                                                <option value=2>仅改图</option>
                                                <option value=4>仅识图</option>
                                                <option value=3>画图+改图</option>
                                                <option value=5>画图+识图</option>
                                                <option value=6>改图+识图</option>
                                                <option value=7>画图+改图+识图</option>
                                            </select>
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>排序数值：</p>
                                    </td>
                                    <td>
                                        <p><input name="sequenceid" class="bg-focus" autoComplete="off"
                                                value="<?php echo ($lastsequenceid + 1) ?>"></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>模型类型：</p>
                                    </td>
                                    <td>
                                        <p><select name="modeltype" class="bg-focus"
                                                onchange="changemodelvalue(this.value);">
                                                <option value="openai">OpenAI官方</option>
                                                <option value="claude">Claude官方</option>
                                                <option value="bard">谷歌Bard</option>
                                                <option value="azure">微软Azure</option>
                                                <option value="chimeragpt">免费国外接口</option>
                                                <option value="文心千帆">文心千帆</option>
                                                <option value="通义千问">通义千问</option>
                                                <option value="腾讯混元">腾讯混元</option>
                                                <option value="清华智谱">清华智谱</option>
                                                <option value="讯飞星火">讯飞星火</option>
                                                <option value="360智脑">360智脑</option>
                                                <option value="百川智能">百川智能</option>
                                                <option value="月之暗面">月之暗面</option>
                                                <option value="火山方舟">火山方舟</option>
                                                <option value="MiniMax">MiniMax</option>
                                                <option value="零一万物">零一万物</option>
                                                <option value="DeepSeek">DeepSeek</option>
                                                <option value="无问芯穹">无问芯穹</option>
                                                <option value="硅基流动">硅基流动</option>
                                                <option value="midjourney">MidJourney</option>
                                                <option value="stablediffusion">StableDiffusion</option>
                                                <option value="LinkAI">LinkAI</option>
                                                <option value="本地知识库">本地知识库</option>
                                            </select></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>模型名称：</p>
                                    </td>
                                    <td>
                                        <p><input id="modelname" name="modelname" class="bg-focus" autoComplete="off">
                                        </p>

                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>模型参数值：</p>
                                    </td>
                                    <td>
                                        <p><input name="modelvalue" class="bg-focus" autoComplete="off"
                                                value="gpt-4o-mini"></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>消耗次数：</p>
                                    </td>
                                    <td>
                                        <p><input name="modelprice" size=2 class="bg-focus" autoComplete="off"></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>是否开启联网搜索：</p>
                                    </td>
                                    <td>
                                        <p><input type=checkbox id="isonlinecheck"
                                                onchange='var checkbox2=document.getElementById("isonline");if (this.checked) {checkbox2.value = "1";} else {checkbox2.value = "0";}'><input
                                                id="isonline" value=0 type=hidden name=isonline></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>是否开启提问翻译：</p>
                                    </td>
                                    <td>
                                        <p><input type=checkbox id="istranslatecheck"
                                                onchange='var checkbox2=document.getElementById("istranslate");if (this.checked) {checkbox2.value = "1";} else {checkbox2.value = "0";}'><input
                                                id="istranslate" value=0 type=hidden name=istranslate></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:right">
                                        <p>备注：</p>
                                    </td>
                                    <td>
                                        <p><input name=memo class="bg-focus" autoComplete="off" size=30></p>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan=2 style="text-align:center">
                                        <button type=submit class="btn-primary" style="width:80px;">确认添加</button>
                                    </td>
                                </tr>
                            </table>

                        </form>
                    </div>
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th class="text-center" style="width:60px;">顺序</th>
                                <th class="text-center" style="width:70px;">前端展示</th>
                                <th class="text-center" style="width:100px;">画图模型</th>
                                <th class="text-center" style="width:70px;">调整顺序</th>
                                <th class="text-center" style="width:160px;">模型类型</th>
                                <th class="text-center" style="width:200px;">模型名称</th>
                                <th class="text-center" style="width:200px;">模型参数值</th>
                                <th class="text-center" style="width:70px;">消耗次数</th>
                                <th class="text-center" style="width:70px;">联网搜索</th>
                                <th class="text-center" style="width:70px;">提问翻译</th>
                                <!--<th class="text-center" style="width:160px;">创建时间</th>-->
                                <th class="text-center">备注</th>
                                <th class="text-center" style="width:100px;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "select * from model order by isvalid desc,isimage,sequenceid,id";
                            $sqlcount = "select count(t.id) from (" . $sql . ") t";
                            $result = $conn->query($sqlcount);
                            $row = $result->fetch();
                            $totalnumber = $row[0];
                            if ($totalnumber == 0) {
                                echo "<tr><td colspan=10>暂未设置任何模型。</td></tr>";
                            } else {
                                $count = 0;
                                $result = $conn->query($sql);
                                while ($row = $result->fetch()) {
                                    $count++;
                                    ?>
                                    <tr>
                                        <form id="form<?php echo $row["id"] ?>" method=post target="temp"
                                            action="modelconfig.php" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden
                                                name=action value=update>
                                            <td class="text-center"><?php echo $count ?></td>
                                            <td class="text-center"><select name=isvalid
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');" <?php echo ($row["isvalid"]) ? "" : "style='background:pink;'" ?>>
                                                    <option value=1 <?php echo ($row["isvalid"]) ? "selected" : "" ?>>是</option>
                                                    <option value=0 <?php echo ($row["isvalid"]) ? "" : "selected" ?>>否</option>
                                                </select></td>
                                            <td class="text-center"><select name=isimage
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value=0 <?php echo ($row["isimage"] == 0) ? "selected" : "" ?>>文本模型
                                                    </option>
                                                    <option value=1 <?php echo ($row["isimage"] == 1) ? "selected" : "" ?>>仅画图
                                                    </option>
                                                    <option value=2 <?php echo ($row["isimage"] == 2) ? "selected" : "" ?>>仅改图
                                                    </option>
                                                    <option value=4 <?php echo ($row["isimage"] == 4) ? "selected" : "" ?>>仅识图
                                                    </option>
                                                    <option value=3 <?php echo ($row["isimage"] == 3) ? "selected" : "" ?>>画图+改图
                                                    </option>
                                                    <option value=5 <?php echo ($row["isimage"] == 5) ? "selected" : "" ?>>画图+识图
                                                    </option>
                                                    <option value=6 <?php echo ($row["isimage"] == 6) ? "selected" : "" ?>>改图+识图
                                                    </option>
                                                    <option value=7 <?php echo ($row["isimage"] == 7) ? "selected" : "" ?>>
                                                        画图+改图+识图</option>
                                                </select></td>
                                            <td class="text-center"><input name=sequenceid style="width:100%;"
                                                    value="<?php echo (empty($row["sequenceid"])) ? 0 : $row["sequenceid"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class=" text-center">
                                                <select name=modeltype onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value="openai" <?php echo ($row["modeltype"] == "openai") ? "selected" : "" ?>>OpenAI官方</option>
                                                    <option value="claude" <?php echo ($row["modeltype"] == "claude") ? "selected" : "" ?>>Claude官方</option>
                                                    <option value="bard" <?php echo ($row["modeltype"] == "bard") ? "selected" : "" ?>>谷歌Bard</option>
                                                    <option value="azure" <?php echo ($row["modeltype"] == "azure") ? "selected" : "" ?>>微软Azure</option>
                                                    <option value="chimeragpt" <?php echo ($row["modeltype"] == "chimeragpt") ? "selected" : "" ?>>免费国外接口</option>
                                                    <option value="文心千帆" <?php echo ($row["modeltype"] == "文心千帆") ? "selected" : "" ?>>文心千帆</option>
                                                    <option value="通义千问" <?php echo ($row["modeltype"] == "通义千问") ? "selected" : "" ?>>通义千问</option>
                                                    <option value="腾讯混元" <?php echo ($row["modeltype"] == "腾讯混元") ? "selected" : "" ?>>腾讯混元</option>
                                                    <option value="清华智谱" <?php echo ($row["modeltype"] == "清华智谱") ? "selected" : "" ?>>清华智谱</option>
                                                    <option value="讯飞星火" <?php echo ($row["modeltype"] == "讯飞星火") ? "selected" : "" ?>>讯飞星火</option>
                                                    <option value="360智脑" <?php echo ($row["modeltype"] == "360智脑") ? "selected" : "" ?>>360智脑</option>
                                                    <option value="百川智能" <?php echo ($row["modeltype"] == "百川智能") ? "selected" : "" ?>>百川智能</option>
                                                    <option value="月之暗面" <?php echo ($row["modeltype"] == "月之暗面") ? "selected" : "" ?>>月之暗面</option>
                                                    <option value="火山方舟" <?php echo ($row["modeltype"] == "火山方舟") ? "selected" : "" ?>>火山方舟</option>
                                                    <option value="MiniMax" <?php echo ($row["modeltype"] == "MiniMax") ? "selected" : "" ?>>MiniMax</option>
                                                    <option value="零一万物" <?php echo ($row["modeltype"] == "零一万物") ? "selected" : "" ?>>零一万物</option>
                                                    <option value="DeepSeek" <?php echo ($row["modeltype"] == "DeepSeek") ? "selected" : "" ?>>DeepSeek</option>
                                                    <option value="无问芯穹" <?php echo ($row["modeltype"] == "无问芯穹") ? "selected" : "" ?>>无问芯穹</option>
                                                    <option value="硅基流动" <?php echo ($row["modeltype"] == "硅基流动") ? "selected" : "" ?>>硅基流动</option>
                                                    <option value="midjourney" <?php echo ($row["modeltype"] == "midjourney") ? "selected" : "" ?>>MidJourney</option>
                                                    <option value="stablediffusion" <?php echo ($row["modeltype"] == "stablediffusion") ? "selected" : "" ?>>
                                                        StableDiffusion</option>
                                                    <option value="alistablediffusion" <?php echo ($row["modeltype"] == "alistablediffusion") ? "selected" : "" ?>>
                                                        阿里StableDiffusion</option>
                                                    <option value="LinkAI" <?php echo ($row["modeltype"] == "LinkAI") ? "selected" : "" ?>>LinkAI</option>
                                                    <option value="本地知识库" <?php echo ($row["modeltype"] == "本地知识库") ? "selected" : "" ?>>本地知识库</option>
                                                </select>
                                            </td>
                                            <td class="text-center"><input name=modelname style="width:100%;"
                                                    value="<?php echo $row["modelname"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input name=modelvalue style="width:100%;"
                                                    value="<?php echo $row["modelvalue"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input name=modelprice style="width:100%;"
                                                    value="<?php echo $row["modelprice"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><select name=isonline
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value=1 <?php echo ($row["isonline"]) ? "selected" : "" ?>>是
                                                    </option>
                                                    <option value=0 <?php echo ($row["isonline"]) ? "" : "selected" ?>>否
                                                    </option>
                                                </select></td>
                                            <td class="text-center"><select name=istranslate
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');">
                                                    <option value=1 <?php echo ($row["istranslate"]) ? "selected" : "" ?>>是
                                                    </option>
                                                    <option value=0 <?php echo ($row["istranslate"]) ? "" : "selected" ?>>否
                                                    </option>
                                                </select></td>
                                            <!--<td class="text-center"><?php echo $row["createtime"] ?></td>-->
                                            <td class="text-center"><input name=memo style="width:100%;"
                                                    value="<?php echo $row["memo"] ?>"
                                                    onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                            <td class="text-center"><input type=submit value="更新"
                                                    style="width:35px;">&nbsp;&nbsp;<input type=button style="width:35px;"
                                                    onclick="deleteid('<?php echo $row["id"] ?>');" value="删除"></td>
                                        </form>
                                    </tr>

                                    <?php
                                }
                            }

                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function deleteid(id) {
            if (confirm("确认删除该模型吗？")) {
                document.getElementById("result").src = "modelconfig.php?action=delete&id=" + id;
            }
        }

        function checkform() {
            if ((window.addtype.sequenceid.value == "") || (window.addtype.modeltype.value == "") || (window.addtype.modelname.value == "") || (window.addtype.modelvalue.value == "") || (window.addtype.modelprice.value == "")) {
                alert("排序数值、模型类型、名称、参数值、消耗次数不能为空！");
                return false;
            } else {
                return true;
            }
        }

        function changemodelvalue(modeltype) {
            if (modeltype == "midjourney") {
                $("#isimagecheck").prop("checked", true);
                $("#isimage").val(7);
                $("#istranslatecheck").prop("checked", true);
                $("#istranslate").val(1);
                window.addtype.modelvalue.value = "midjourney_image";
            } else if (modeltype == "stablediffusion") {
                $("#isimagecheck").prop("checked", true);
                $("#isimage").val(3);
                $("#istranslatecheck").prop("checked", true);
                $("#istranslate").val(1);
                window.addtype.modelvalue.value = "stablediffusion_image";
            } else if (modeltype == "alistablediffusion") {
                $("#isimagecheck").prop("checked", true);
                $("#isimage").val(1);
                $("#istranslatecheck").prop("checked", true);
                $("#istranslate").val(1);
                window.addtype.modelvalue.value = "stable-diffusion-v1.5";
            } else if (modeltype == "openai") {
                window.addtype.modelvalue.value = "gpt-4o-mini";
            } else if (modeltype == "claude") {
                window.addtype.modelvalue.value = "claude-3-sonnet";
            } else if (modeltype == "bard") {
                window.addtype.modelvalue.value = "gemini-1.5-flash";
            } else if (modeltype == "文心千帆") {
                window.addtype.modelvalue.value = "ERNIE-Bot-turbo";
            } else if (modeltype == "通义千问") {
                window.addtype.modelvalue.value = "qwen-turbo";
            } else if (modeltype == "腾讯混元") {
                window.addtype.modelvalue.value = "hunyuan-pro";
            } else if (modeltype == "清华智谱") {
                window.addtype.modelvalue.value = "glm-4";
            } else if (modeltype == "讯飞星火") {
                window.addtype.modelvalue.value = "Spark Pro";
            } else if (modeltype == "360智脑") {
                window.addtype.modelvalue.value = "360gpt2-pro";
            } else if (modeltype == "百川智能") {
                window.addtype.modelvalue.value = "Baichuan4";
            } else if (modeltype == "月之暗面") {
                window.addtype.modelvalue.value = "moonshot-v1-8k";
            } else if (modeltype == "火山方舟") {
                window.addtype.modelvalue.value = "ep-20240702083321-xxxxx";
            } else if (modeltype == "MiniMax") {
                window.addtype.modelvalue.value = "abab6.5s";
            } else if (modeltype == "零一万物") {
                window.addtype.modelvalue.value = "yi-large";
            } else if (modeltype == "DeepSeek") {
                window.addtype.modelvalue.value = "deepseek-chat";
            } else if (modeltype == "无问芯穹") {
                window.addtype.modelvalue.value = "mt-infini-3b";
            } else if (modeltype == "硅基流动") {
                window.addtype.modelvalue.value = "internlm/internlm2_5-7b-chat";
            } else if (modeltype == "LinkAI") {
                window.addtype.modelvalue.value = "gpt-3.5-turbo,cJslRJSo";
            } else if (modeltype == "本地知识库") {
                window.addtype.modelvalue.value = "asst_eduOuMQnER4nFKyDdCJo7Wur";
            }
        }

        function toggleCollapse(elem) {
            var myDiv = document.getElementById("info");
            if (myDiv.className == "mycollapse") {
                myDiv.className = "myexpand";
                elem.innerText = "点击收起模型配置说明";
            } else {
                myDiv.className = "mycollapse";
                elem.innerText = "点击展开模型配置说明";
            }
        }
    </script>
    <iframe style="display:none;" id=result name=temp></iframe>
</body>

</html>