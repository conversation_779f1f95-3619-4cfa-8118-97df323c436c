@keyframes slide-in {
    0% {
        opacity: 0;
        transform: translateY(20px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

@keyframes slide-in-from-top {
    0% {
        opacity: 0;
        transform: translateY(-20px)
    }

    to {
        opacity: 1;
        transform: translateY(0)
    }
}

.window-header {
    padding: 14px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.window-header-title {
    max-width: calc(100% - 100px);
    overflow: hidden
}

.window-header-title .window-header-main-title {
    font-size: 20px;
    font-weight: bolder;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: 50vw
}

.window-header-title .window-header-sub-title {
    font-size: 14px;
    margin-top: 5px
}

.window-actions {
    display: inline-flex
}

.window-action-button {
    margin-left: 10px
}

.light {
    --theme: light;
    --silver: #ccc;
    --white: #fff;
    --black: #303030;
    --gray: #fafafa;
    --primary: #1d93ab;
    --second: #e7f8ff;
    --hover-color: #f3f3f3;
    --bar-color: rgba(0, 0, 0, .1);
    --theme-color: var(--gray);
    --shadow: 50px 50px 100px 10px rgba(0, 0, 0, .1);
    --card-shadow: 0px 2px 4px 0px rgba(0, 0, 0, .05);
    --border-in-light: 1px solid #dedede
}

.dark {
    --theme: dark;
    --silver: #666;
    --white: #1e1e1e;
    --black: #bbb;
    --gray: #151515;
    --primary: #1d93ab;
    --second: #1b262a;
    --hover-color: #323232;
    --bar-color: hsla(0, 0%, 100%, .1);
    --border-in-light: 1px solid hsla(0, 0%, 100%, .192);
    --theme-color: var(--gray)
}

.dark div:not(.no-dark)>svg {
    filter: invert(.5)
}

.mask {
    filter: invert(.8)
}

:root {
    --theme: light;
    --white: #fff;
    --black: #303030;
    --gray: #fafafa;
    --primary: #1d93ab;
    --second: #e7f8ff;
    --hover-color: #f3f3f3;
    --bar-color: rgba(0, 0, 0, .1);
    --theme-color: var(--gray);
    --shadow: 50px 50px 100px 10px rgba(0, 0, 0, .1);
    --card-shadow: 0px 2px 4px 0px rgba(0, 0, 0, .05);
    --border-in-light: 1px solid #dedede;
    --window-width: 90vw;
    --window-height: 90vh;
    --sidebar-width: 300px;
    --window-content-width: calc(100% - var(--sidebar-width));
    --message-max-width: 80%;
    --full-height: 100%
}

@media only screen and (max-width:600px) {
    :root {
        --window-width: 100vw;
        --window-height: var(--full-height);
        --sidebar-width: 100vw;
        --window-content-width: var(--window-width);
        --message-max-width: 100%
    }

    .no-mobile {
        display: none
    }
}

@media(prefers-color-scheme:dark) {
    :root {
        --theme: dark;
        --white: #1e1e1e;
        --black: #bbb;
        --gray: #151515;
        --primary: #1d93ab;
        --second: #1b262a;
        --hover-color: #323232;
        --bar-color: hsla(0, 0%, 100%, .1);
        --border-in-light: 1px solid hsla(0, 0%, 100%, .192);
        --theme-color: var(--gray)
    }

    :root div:not(.no-dark)>svg {
        filter: invert(.5)
    }
}

html {
    font-family: Noto Sans SC, SF Pro SC, SF Pro Text, SF Pro Icons, PingFang SC, Helvetica Neue, Helvetica, Arial, sans-serif
}

body,
html {
    height: var(--full-height)
}

body {
    background-color: var(--gray);
    color: var(--black);
    margin: 0;
    padding: 0;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    touch-action: pan-x pan-y
}

@media only screen and (max-width:600px) {
    body {
        background-color: var(--second)
    }
}

::-webkit-scrollbar {
    --bar-width: 5px;
    width: var(--bar-width);
    height: var(--bar-width)
}

::-webkit-scrollbar-track {
    background-color: transparent
}

::-webkit-scrollbar-thumb {
    background-color: var(--bar-color);
    border-radius: 20px;
    background-clip: content-box;
    border: 1px solid transparent
}

select {
    border: var(--border-in-light);
    padding: 10px;
    border-radius: 10px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--white);
    color: var(--black);
    text-align: center
}

label,
select {
    cursor: pointer
}

/* 自定义提示框样式 */
body .custom_alert1 {
    background-color: #fff;
    border-radius: 20px;
    text-align: center;
}

body .custom_alert1 .layui-layer-content {
    padding: 6px;
    font-size: 16px;
}

/* 影响修改会话名称的弹框了 */
input {
    text-align: center;
    font-family: inherit
}

input[type=checkbox] {
    cursor: pointer;
    background-color: var(--white);
    color: var(--black);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: var(--border-in-light);
    border-radius: 5px;
    height: 16px;
    width: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center
}

input[type=checkbox]:checked:after {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--primary);
    content: " ";
    border-radius: 2px
}

input[type=range] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: var(--white);
    color: var(--black)
}

input[type=range]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    width: 20px;
    background-color: var(--primary);
    border-radius: 10px;
    cursor: pointer;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    margin-left: 5px;
    border: none
}

input[type=range]::-moz-range-thumb {
    -moz-appearance: none;
    appearance: none;
    height: 8px;
    width: 20px;
    background-color: var(--primary);
    border-radius: 10px;
    cursor: pointer;
    -moz-transition: all .3s ease;
    transition: all .3s ease;
    margin-left: 5px;
    border: none
}

input[type=range]::-ms-thumb {
    appearance: none;
    height: 8px;
    width: 20px;
    background-color: var(--primary);
    border-radius: 10px;
    cursor: pointer;
    -ms-transition: all .3s ease;
    transition: all .3s ease;
    margin-left: 5px;
    border: none
}

input[type=range]::-webkit-slider-thumb:hover {
    transform: scaleY(1.2);
    width: 24px
}

input[type=range]::-moz-range-thumb:hover {
    transform: scaleY(1.2);
    width: 24px
}

input[type=range]::-ms-thumb:hover {
    transform: scaleY(1.2);
    width: 24px
}

/* 影响修改会话名称的弹框了 */
/* input[type=number],
input[type=password],
input[type=text] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 10px;
    border: var(--border-in-light);
    min-height: 36px;
    box-sizing: border-box;
    background: var(--white);
    color: var(--black);
    padding: 0 10px;
    max-width: 50%;
    font-family: inherit
} */

div.math {
    overflow-x: auto
}

.modal-mask {
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    height: var(--full-height);
    width: 100vw;
    background-color: rgba(0, 0, 0, .5);
    display: flex;
    align-items: center;
    justify-content: center
}

@media screen and (max-width:600px) {
    .modal-mask {
        align-items: flex-end
    }
}

.link {
    font-size: 12px;
    color: var(--primary);
    text-decoration: none
}

.link:hover {
    text-decoration: underline
}

pre {
    position: relative
}

pre:hover .copy-code-button {
    pointer-events: all;
    transform: translateX(0);
    opacity: .5
}

pre .copy-code-button {
    position: absolute;
    right: 10px;
    top: 1em;
    cursor: pointer;
    padding: 0 5px;
    background-color: var(--black);
    color: var(--white);
    border: var(--border-in-light);
    border-radius: 10px;
    transform: translateX(10px);
    pointer-events: none;
    opacity: 0;
    transition: all .3s ease
}

pre .copy-code-button:after {
    content: "copy"
}

pre .copy-code-button:hover {
    opacity: 1
}

.clickable {
    cursor: pointer
}

.clickable:hover {
    filter: brightness(.9)
}

.error {
    width: 80%;
    border-radius: 20px;
    border: var(--border-in-light);
    box-shadow: var(--card-shadow);
    padding: 20px;
    background-color: var(--white);
    color: var(--black)
}

.error,
.error pre {
    overflow: auto
}

.password-input-container {
    max-width: 50%;
    display: flex;
    justify-content: flex-end
}

.password-input-container .password-eye {
    margin-right: 4px
}

.password-input-container .password-input {
    min-width: 80%
}

.user-avatar {
    height: 30px;
    min-height: 30px;
    width: 30px;
    min-width: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: var(--border-in-light);
    box-shadow: var(--card-shadow);
    border-radius: 10px
}

.one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.markdown-body {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    margin: 0;
    color: var(--color-fg-default);
    background-color: var(--color-canvas-default);
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word
}

.light {
    color-scheme: light;
    --color-prettylights-syntax-comment: #6e7781;
    --color-prettylights-syntax-constant: #0550ae;
    --color-prettylights-syntax-entity: #8250df;
    --color-prettylights-syntax-storage-modifier-import: #24292f;
    --color-prettylights-syntax-entity-tag: #116329;
    --color-prettylights-syntax-keyword: #cf222e;
    --color-prettylights-syntax-string: #0a3069;
    --color-prettylights-syntax-variable: #953800;
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
    --color-prettylights-syntax-invalid-illegal-bg: #82071e;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa;
    --color-prettylights-syntax-carriage-return-bg: #cf222e;
    --color-prettylights-syntax-string-regexp: #116329;
    --color-prettylights-syntax-markup-list: #3b2300;
    --color-prettylights-syntax-markup-heading: #0550ae;
    --color-prettylights-syntax-markup-italic: #24292f;
    --color-prettylights-syntax-markup-bold: #24292f;
    --color-prettylights-syntax-markup-deleted-text: #82071e;
    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
    --color-prettylights-syntax-markup-inserted-text: #116329;
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
    --color-prettylights-syntax-markup-changed-text: #953800;
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
    --color-prettylights-syntax-markup-ignored-text: #eaeef2;
    --color-prettylights-syntax-markup-ignored-bg: #0550ae;
    --color-prettylights-syntax-meta-diff-range: #8250df;
    --color-prettylights-syntax-brackethighlighter-angle: #57606a;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
    --color-prettylights-syntax-constant-other-reference-link: #0a3069;
    --color-fg-default: #24292f;
    --color-fg-muted: #57606a;
    --color-fg-subtle: #6e7781;
    --color-canvas-default: transparent;
    --color-canvas-subtle: #f6f8fa;
    --color-border-default: #d0d7de;
    --color-border-muted: #d8dee4;
    --color-neutral-muted: rgba(175, 184, 193, .2);
    --color-accent-fg: #0969da;
    --color-accent-emphasis: #0969da;
    --color-attention-subtle: #fff8c5;
    --color-danger-fg: #cf222e
}

.dark {
    color-scheme: dark;
    --color-prettylights-syntax-comment: #8b949e;
    --color-prettylights-syntax-constant: #79c0ff;
    --color-prettylights-syntax-entity: #d2a8ff;
    --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
    --color-prettylights-syntax-entity-tag: #7ee787;
    --color-prettylights-syntax-keyword: #ff7b72;
    --color-prettylights-syntax-string: #a5d6ff;
    --color-prettylights-syntax-variable: #ffa657;
    --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
    --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
    --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
    --color-prettylights-syntax-carriage-return-text: #f0f6fc;
    --color-prettylights-syntax-carriage-return-bg: #b62324;
    --color-prettylights-syntax-string-regexp: #7ee787;
    --color-prettylights-syntax-markup-list: #f2cc60;
    --color-prettylights-syntax-markup-heading: #1f6feb;
    --color-prettylights-syntax-markup-italic: #c9d1d9;
    --color-prettylights-syntax-markup-bold: #c9d1d9;
    --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
    --color-prettylights-syntax-markup-deleted-bg: #67060c;
    --color-prettylights-syntax-markup-inserted-text: #aff5b4;
    --color-prettylights-syntax-markup-inserted-bg: #033a16;
    --color-prettylights-syntax-markup-changed-text: #ffdfb6;
    --color-prettylights-syntax-markup-changed-bg: #5a1e02;
    --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
    --color-prettylights-syntax-markup-ignored-bg: #1158c7;
    --color-prettylights-syntax-meta-diff-range: #d2a8ff;
    --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
    --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
    --color-fg-default: #c9d1d9;
    --color-fg-muted: #8b949e;
    --color-fg-subtle: #6e7681;
    --color-canvas-default: transparent;
    --color-canvas-subtle: #161b22;
    --color-border-default: #30363d;
    --color-border-muted: #21262d;
    --color-neutral-muted: hsla(215, 8%, 47%, .4);
    --color-accent-fg: #58a6ff;
    --color-accent-emphasis: #1f6feb;
    --color-attention-subtle: rgba(187, 128, 9, .15);
    --color-danger-fg: #f85149
}

:root {
    color-scheme: light;
    --color-prettylights-syntax-comment: #6e7781;
    --color-prettylights-syntax-constant: #0550ae;
    --color-prettylights-syntax-entity: #8250df;
    --color-prettylights-syntax-storage-modifier-import: #24292f;
    --color-prettylights-syntax-entity-tag: #116329;
    --color-prettylights-syntax-keyword: #cf222e;
    --color-prettylights-syntax-string: #0a3069;
    --color-prettylights-syntax-variable: #953800;
    --color-prettylights-syntax-brackethighlighter-unmatched: #82071e;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
    --color-prettylights-syntax-invalid-illegal-bg: #82071e;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa;
    --color-prettylights-syntax-carriage-return-bg: #cf222e;
    --color-prettylights-syntax-string-regexp: #116329;
    --color-prettylights-syntax-markup-list: #3b2300;
    --color-prettylights-syntax-markup-heading: #0550ae;
    --color-prettylights-syntax-markup-italic: #24292f;
    --color-prettylights-syntax-markup-bold: #24292f;
    --color-prettylights-syntax-markup-deleted-text: #82071e;
    --color-prettylights-syntax-markup-deleted-bg: #ffebe9;
    --color-prettylights-syntax-markup-inserted-text: #116329;
    --color-prettylights-syntax-markup-inserted-bg: #dafbe1;
    --color-prettylights-syntax-markup-changed-text: #953800;
    --color-prettylights-syntax-markup-changed-bg: #ffd8b5;
    --color-prettylights-syntax-markup-ignored-text: #eaeef2;
    --color-prettylights-syntax-markup-ignored-bg: #0550ae;
    --color-prettylights-syntax-meta-diff-range: #8250df;
    --color-prettylights-syntax-brackethighlighter-angle: #57606a;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #8c959f;
    --color-prettylights-syntax-constant-other-reference-link: #0a3069;
    --color-fg-default: #24292f;
    --color-fg-muted: #57606a;
    --color-fg-subtle: #6e7781;
    --color-canvas-default: transparent;
    --color-canvas-subtle: #f6f8fa;
    --color-border-default: #d0d7de;
    --color-border-muted: #d8dee4;
    --color-neutral-muted: rgba(175, 184, 193, .2);
    --color-accent-fg: #0969da;
    --color-accent-emphasis: #0969da;
    --color-attention-subtle: #fff8c5;
    --color-danger-fg: #cf222e
}

@media(prefers-color-scheme:dark) {
    :root {
        color-scheme: dark;
        --color-prettylights-syntax-comment: #8b949e;
        --color-prettylights-syntax-constant: #79c0ff;
        --color-prettylights-syntax-entity: #d2a8ff;
        --color-prettylights-syntax-storage-modifier-import: #c9d1d9;
        --color-prettylights-syntax-entity-tag: #7ee787;
        --color-prettylights-syntax-keyword: #ff7b72;
        --color-prettylights-syntax-string: #a5d6ff;
        --color-prettylights-syntax-variable: #ffa657;
        --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
        --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
        --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
        --color-prettylights-syntax-carriage-return-text: #f0f6fc;
        --color-prettylights-syntax-carriage-return-bg: #b62324;
        --color-prettylights-syntax-string-regexp: #7ee787;
        --color-prettylights-syntax-markup-list: #f2cc60;
        --color-prettylights-syntax-markup-heading: #1f6feb;
        --color-prettylights-syntax-markup-italic: #c9d1d9;
        --color-prettylights-syntax-markup-bold: #c9d1d9;
        --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
        --color-prettylights-syntax-markup-deleted-bg: #67060c;
        --color-prettylights-syntax-markup-inserted-text: #aff5b4;
        --color-prettylights-syntax-markup-inserted-bg: #033a16;
        --color-prettylights-syntax-markup-changed-text: #ffdfb6;
        --color-prettylights-syntax-markup-changed-bg: #5a1e02;
        --color-prettylights-syntax-markup-ignored-text: #c9d1d9;
        --color-prettylights-syntax-markup-ignored-bg: #1158c7;
        --color-prettylights-syntax-meta-diff-range: #d2a8ff;
        --color-prettylights-syntax-brackethighlighter-angle: #8b949e;
        --color-prettylights-syntax-sublimelinter-gutter-mark: #484f58;
        --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
        --color-fg-default: #c9d1d9;
        --color-fg-muted: #8b949e;
        --color-fg-subtle: #6e7681;
        --color-canvas-default: transparent;
        --color-canvas-subtle: #161b22;
        --color-border-default: #30363d;
        --color-border-muted: #21262d;
        --color-neutral-muted: hsla(215, 8%, 47%, .4);
        --color-accent-fg: #58a6ff;
        --color-accent-emphasis: #1f6feb;
        --color-attention-subtle: rgba(187, 128, 9, .15);
        --color-danger-fg: #f85149
    }
}

.markdown-body h1:hover .anchor .octicon-link:before,
.markdown-body h2:hover .anchor .octicon-link:before,
.markdown-body h3:hover .anchor .octicon-link:before,
.markdown-body h4:hover .anchor .octicon-link:before,
.markdown-body h5:hover .anchor .octicon-link:before,
.markdown-body h6:hover .anchor .octicon-link:before {
    width: 16px;
    height: 16px;
    content: " ";
    display: inline-block;
    background-color: currentColor;
    -webkit-mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>");
    mask-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' version='1.1' aria-hidden='true'><path fill-rule='evenodd' d='M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z'></path></svg>")
}

.markdown-body details,
.markdown-body figcaption,
.markdown-body figure {
    display: block
}

.markdown-body summary {
    display: list-item
}

.markdown-body [hidden] {
    display: none !important
}

.markdown-body a {
    background-color: transparent;
    color: var(--color-accent-fg);
    text-decoration: none
}

.markdown-body abbr[title] {
    border-bottom: none;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

.markdown-body b,
.markdown-body strong {
    font-weight: var(--base-text-weight-semibold, 600)
}

.markdown-body dfn {
    font-style: italic
}

.markdown-body h1 {
    margin: .67em 0;
    font-weight: var(--base-text-weight-semibold, 600);
    padding-bottom: .3em;
    font-size: 2em;
    border-bottom: 1px solid var(--color-border-muted)
}

.markdown-body mark {
    background-color: var(--color-attention-subtle);
    color: var(--color-fg-default)
}

.markdown-body small {
    font-size: 90%
}

.markdown-body sub,
.markdown-body sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

.markdown-body sub {
    bottom: -.25em
}

.markdown-body sup {
    top: -.5em
}

.markdown-body img {
    border-style: none;
    max-width: 100%;
    box-sizing: content-box;
    background-color: var(--color-canvas-default)
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre,
.markdown-body samp {
    font-family: monospace;
    font-size: 1em
}

.markdown-body figure {
    margin: 1em 40px
}

.markdown-body hr {
    box-sizing: content-box;
    overflow: hidden;
    background: transparent;
    height: .25em;
    padding: 0;
    margin: 24px 0;
    background-color: var(--color-border-default);
    border: 0
}

.markdown-body input {
    font: inherit;
    margin: 0;
    overflow: visible;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

.markdown-body [type=button],
.markdown-body [type=reset],
.markdown-body [type=submit] {
    -webkit-appearance: button
}

.markdown-body [type=checkbox],
.markdown-body [type=radio] {
    box-sizing: border-box;
    padding: 0
}

.markdown-body [type=number]::-webkit-inner-spin-button,
.markdown-body [type=number]::-webkit-outer-spin-button {
    height: auto
}

.markdown-body [type=search]::-webkit-search-cancel-button,
.markdown-body [type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

.markdown-body ::-webkit-input-placeholder {
    color: inherit;
    opacity: .54
}

.markdown-body ::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

.markdown-body a:hover {
    text-decoration: underline
}

.markdown-body ::placeholder {
    color: var(--color-fg-subtle);
    opacity: 1
}

.markdown-body hr:after,
.markdown-body hr:before {
    display: table;
    content: ""
}

.markdown-body hr:after {
    clear: both
}

.markdown-body table {
    border-spacing: 0;
    border-collapse: collapse;
    display: block;
    width: max-content;
    max-width: 100%;
    overflow: auto
}

.markdown-body td,
.markdown-body th {
    padding: 0
}

.markdown-body details summary {
    cursor: pointer
}

.markdown-body details:not([open])>:not(summary) {
    display: none !important
}

.markdown-body [role=button]:focus,
.markdown-body a:focus,
.markdown-body input[type=checkbox]:focus,
.markdown-body input[type=radio]:focus {
    outline: 2px solid var(--color-accent-fg);
    outline-offset: -2px;
    box-shadow: none
}

.markdown-body [role=button]:focus:not(:focus-visible),
.markdown-body a:focus:not(:focus-visible),
.markdown-body input[type=checkbox]:focus:not(:focus-visible),
.markdown-body input[type=radio]:focus:not(:focus-visible) {
    outline: 1px solid transparent
}

.markdown-body [role=button]:focus-visible,
.markdown-body a:focus-visible,
.markdown-body input[type=checkbox]:focus-visible,
.markdown-body input[type=radio]:focus-visible {
    outline: 2px solid var(--color-accent-fg);
    outline-offset: -2px;
    box-shadow: none
}

.markdown-body a:not([class]):focus,
.markdown-body a:not([class]):focus-visible,
.markdown-body input[type=checkbox]:focus,
.markdown-body input[type=checkbox]:focus-visible,
.markdown-body input[type=radio]:focus,
.markdown-body input[type=radio]:focus-visible {
    outline-offset: 0
}

.markdown-body kbd {
    display: inline-block;
    padding: 3px 5px;
    font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
    line-height: 10px;
    color: var(--color-fg-default);
    vertical-align: middle;
    background-color: var(--color-canvas-subtle);
    border-bottom-color: var(--color-neutral-muted);
    border: 1px solid var(--color-neutral-muted);
    border-radius: 6px;
    box-shadow: inset 0 -1px 0 var(--color-neutral-muted)
}

.markdown-body h1,
.markdown-body h2,
.markdown-body h3,
.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: var(--base-text-weight-semibold, 600);
    line-height: 1.25
}

.markdown-body h2 {
    padding-bottom: .3em;
    font-size: 1.5em;
    border-bottom: 1px solid var(--color-border-muted)
}

.markdown-body h2,
.markdown-body h3 {
    font-weight: var(--base-text-weight-semibold, 600)
}

.markdown-body h3 {
    font-size: 1.25em
}

.markdown-body h4 {
    font-size: 1em
}

.markdown-body h4,
.markdown-body h5 {
    font-weight: var(--base-text-weight-semibold, 600)
}

.markdown-body h5 {
    font-size: .875em
}

.markdown-body h6 {
    font-weight: var(--base-text-weight-semibold, 600);
    font-size: .85em;
    color: var(--color-fg-muted)
}

.markdown-body p {
    margin-top: 0;
    margin-bottom: 10px
}

.markdown-body blockquote {
    margin: 0;
    padding: 0 1em;
    color: var(--color-fg-muted);
    border-left: .25em solid var(--color-border-default)
}

.markdown-body ol,
.markdown-body ul {
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 2em
}

.markdown-body ol ol,
.markdown-body ul ol {
    list-style-type: lower-roman
}

.markdown-body ol ol ol,
.markdown-body ol ul ol,
.markdown-body ul ol ol,
.markdown-body ul ul ol {
    list-style-type: lower-alpha
}

.markdown-body dd {
    margin-left: 0
}

.markdown-body code,
.markdown-body pre,
.markdown-body samp,
.markdown-body tt {
    font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;
    font-size: 12px
}

.markdown-body pre {
    margin-top: 0;
    margin-bottom: 0;
    word-wrap: normal
}

.markdown-body .octicon {
    display: inline-block;
    overflow: visible !important;
    vertical-align: text-bottom;
    fill: currentColor
}

.markdown-body input::-webkit-inner-spin-button,
.markdown-body input::-webkit-outer-spin-button {
    margin: 0;
    -webkit-appearance: none;
    appearance: none
}

.markdown-body:after,
.markdown-body:before {
    display: table;
    content: ""
}

.markdown-body:after {
    clear: both
}

.markdown-body>:first-child {
    margin-top: 0 !important
}

.markdown-body>:last-child {
    margin-bottom: 0 !important
}

.markdown-body a:not([href]) {
    color: inherit;
    text-decoration: none
}

.markdown-body .absent {
    color: var(--color-danger-fg)
}

.markdown-body .anchor {
    float: left;
    padding-right: 4px;
    margin-left: -20px;
    line-height: 1
}

.markdown-body .anchor:focus {
    outline: none
}

.markdown-body blockquote,
.markdown-body details,
.markdown-body dl,
.markdown-body ol,
.markdown-body p,
.markdown-body pre,
.markdown-body table,
.markdown-body ul {
    margin-top: 0;
    margin-bottom: 16px
}

.markdown-body blockquote>:first-child {
    margin-top: 0
}

.markdown-body blockquote>:last-child {
    margin-bottom: 0
}

.markdown-body h1 .octicon-link,
.markdown-body h2 .octicon-link,
.markdown-body h3 .octicon-link,
.markdown-body h4 .octicon-link,
.markdown-body h5 .octicon-link,
.markdown-body h6 .octicon-link {
    color: var(--color-fg-default);
    vertical-align: middle;
    visibility: hidden
}

.markdown-body h1:hover .anchor,
.markdown-body h2:hover .anchor,
.markdown-body h3:hover .anchor,
.markdown-body h4:hover .anchor,
.markdown-body h5:hover .anchor,
.markdown-body h6:hover .anchor {
    text-decoration: none
}

.markdown-body h1:hover .anchor .octicon-link,
.markdown-body h2:hover .anchor .octicon-link,
.markdown-body h3:hover .anchor .octicon-link,
.markdown-body h4:hover .anchor .octicon-link,
.markdown-body h5:hover .anchor .octicon-link,
.markdown-body h6:hover .anchor .octicon-link {
    visibility: visible
}

.markdown-body h1 code,
.markdown-body h1 tt,
.markdown-body h2 code,
.markdown-body h2 tt,
.markdown-body h3 code,
.markdown-body h3 tt,
.markdown-body h4 code,
.markdown-body h4 tt,
.markdown-body h5 code,
.markdown-body h5 tt,
.markdown-body h6 code,
.markdown-body h6 tt {
    padding: 0 .2em;
    font-size: inherit
}

.markdown-body summary h1,
.markdown-body summary h2,
.markdown-body summary h3,
.markdown-body summary h4,
.markdown-body summary h5,
.markdown-body summary h6 {
    display: inline-block
}

.markdown-body summary h1 .anchor,
.markdown-body summary h2 .anchor,
.markdown-body summary h3 .anchor,
.markdown-body summary h4 .anchor,
.markdown-body summary h5 .anchor,
.markdown-body summary h6 .anchor {
    margin-left: -40px
}

.markdown-body summary h1,
.markdown-body summary h2 {
    padding-bottom: 0;
    border-bottom: 0
}

.markdown-body ol.no-list,
.markdown-body ul.no-list {
    padding: 0;
    list-style-type: none
}

.markdown-body ol[type=a] {
    list-style-type: lower-alpha
}

.markdown-body ol[type=A] {
    list-style-type: upper-alpha
}

.markdown-body ol[type=i] {
    list-style-type: lower-roman
}

.markdown-body ol[type=I] {
    list-style-type: upper-roman
}

.markdown-body div>ol:not([type]),
.markdown-body ol[type="1"] {
    list-style-type: decimal
}

.markdown-body ol ol,
.markdown-body ol ul,
.markdown-body ul ol,
.markdown-body ul ul {
    margin-top: 0;
    margin-bottom: 0
}

.markdown-body li>p {
    margin-top: 16px
}

.markdown-body li+li {
    margin-top: .25em
}

.markdown-body dl {
    padding: 0
}

.markdown-body dl dt {
    padding: 0;
    margin-top: 16px;
    font-size: 1em;
    font-style: italic;
    font-weight: var(--base-text-weight-semibold, 600)
}

.markdown-body dl dd {
    padding: 0 16px;
    margin-bottom: 16px
}

.markdown-body table th {
    font-weight: var(--base-text-weight-semibold, 600)
}

.markdown-body table td,
.markdown-body table th {
    padding: 6px 13px;
    border: 1px solid var(--color-border-default)
}

.markdown-body table tr {
    background-color: var(--color-canvas-default);
    border-top: 1px solid var(--color-border-muted)
}

.markdown-body table tr:nth-child(2n) {
    background-color: var(--color-canvas-subtle)
}

.markdown-body table img {
    background-color: transparent
}

.markdown-body img[align=right] {
    padding-left: 20px
}

.markdown-body img[align=left] {
    padding-right: 20px
}

.markdown-body .emoji {
    max-width: none;
    vertical-align: text-top;
    background-color: transparent
}

.markdown-body span.frame {
    display: block;
    overflow: hidden
}

.markdown-body span.frame>span {
    display: block;
    float: left;
    width: auto;
    padding: 7px;
    margin: 13px 0 0;
    overflow: hidden;
    border: 1px solid var(--color-border-default)
}

.markdown-body span.frame span img {
    display: block;
    float: left
}

.markdown-body span.frame span span {
    display: block;
    padding: 5px 0 0;
    clear: both;
    color: var(--color-fg-default)
}

.markdown-body span.align-center {
    display: block;
    overflow: hidden;
    clear: both
}

.markdown-body span.align-center>span {
    display: block;
    margin: 13px auto 0;
    overflow: hidden;
    text-align: center
}

.markdown-body span.align-center span img {
    margin: 0 auto;
    text-align: center
}

.markdown-body span.align-right {
    display: block;
    overflow: hidden;
    clear: both
}

.markdown-body span.align-right>span {
    display: block;
    margin: 13px 0 0;
    overflow: hidden;
    text-align: right
}

.markdown-body span.align-right span img {
    margin: 0;
    text-align: right
}

.markdown-body span.float-left {
    display: block;
    float: left;
    margin-right: 13px;
    overflow: hidden
}

.markdown-body span.float-left span {
    margin: 13px 0 0
}

.markdown-body span.float-right {
    display: block;
    float: right;
    margin-left: 13px;
    overflow: hidden
}

.markdown-body span.float-right>span {
    display: block;
    margin: 13px auto 0;
    overflow: hidden;
    text-align: right
}

.markdown-body code,
.markdown-body tt {
    padding: .2em .4em;
    margin: 0;
    font-size: 85%;
    white-space: break-spaces;
    background-color: var(--color-neutral-muted);
    border-radius: 6px
}

.markdown-body code br,
.markdown-body tt br {
    display: none
}

.markdown-body del code {
    text-decoration: inherit
}

.markdown-body samp {
    font-size: 85%
}

.markdown-body pre code {
    font-size: 100%
}

.markdown-body pre>code {
    padding: 0;
    margin: 0;
    word-break: normal;
    white-space: pre;
    background: transparent;
    border: 0
}

.markdown-body .highlight {
    margin-bottom: 16px
}

.markdown-body .highlight pre {
    margin-bottom: 0;
    word-break: normal
}

.markdown-body .highlight pre,
.markdown-body pre {
    padding: 16px 16px 8px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    border-radius: 6px
}

.markdown-body pre code,
.markdown-body pre tt {
    display: inline-block;
    max-width: 100%;
    padding: 0;
    margin: 0;
    overflow-x: scroll;
    line-height: inherit;
    word-wrap: normal;
    background-color: transparent;
    border: 0
}

.markdown-body .csv-data td,
.markdown-body .csv-data th {
    padding: 5px;
    overflow: hidden;
    font-size: 12px;
    line-height: 1;
    text-align: left;
    white-space: nowrap
}

.markdown-body .csv-data .blob-num {
    padding: 10px 8px 9px;
    text-align: right;
    background: var(--color-canvas-default);
    border: 0
}

.markdown-body .csv-data tr {
    border-top: 0
}

.markdown-body .csv-data th {
    font-weight: var(--base-text-weight-semibold, 600);
    background: var(--color-canvas-subtle);
    border-top: 0
}

.markdown-body [data-footnote-ref]:before {
    content: "["
}

.markdown-body [data-footnote-ref]:after {
    content: "]"
}

.markdown-body .footnotes {
    font-size: 12px;
    color: var(--color-fg-muted);
    border-top: 1px solid var(--color-border-default)
}

.markdown-body .footnotes ol {
    padding-left: 16px
}

.markdown-body .footnotes ol ul {
    display: inline-block;
    padding-left: 16px;
    margin-top: 16px
}

.markdown-body .footnotes li {
    position: relative
}

.markdown-body .footnotes li:target:before {
    position: absolute;
    top: -8px;
    right: -8px;
    bottom: -8px;
    left: -24px;
    pointer-events: none;
    content: "";
    border: 2px solid var(--color-accent-emphasis);
    border-radius: 6px
}

.markdown-body .footnotes li:target {
    color: var(--color-fg-default)
}

.markdown-body .footnotes .data-footnote-backref g-emoji {
    font-family: monospace
}

.markdown-body .pl-c {
    color: var(--color-prettylights-syntax-comment)
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
    color: var(--color-prettylights-syntax-constant)
}

.markdown-body .pl-e,
.markdown-body .pl-en {
    color: var(--color-prettylights-syntax-entity)
}

.markdown-body .pl-s .pl-s1,
.markdown-body .pl-smi {
    color: var(--color-prettylights-syntax-storage-modifier-import)
}

.markdown-body .pl-ent {
    color: var(--color-prettylights-syntax-entity-tag)
}

.markdown-body .pl-k {
    color: var(--color-prettylights-syntax-keyword)
}

.markdown-body .pl-pds,
.markdown-body .pl-s,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sra,
.markdown-body .pl-sr .pl-sre {
    color: var(--color-prettylights-syntax-string)
}

.markdown-body .pl-smw,
.markdown-body .pl-v {
    color: var(--color-prettylights-syntax-variable)
}

.markdown-body .pl-bu {
    color: var(--color-prettylights-syntax-brackethighlighter-unmatched)
}

.markdown-body .pl-ii {
    color: var(--color-prettylights-syntax-invalid-illegal-text);
    background-color: var(--color-prettylights-syntax-invalid-illegal-bg)
}

.markdown-body .pl-c2 {
    color: var(--color-prettylights-syntax-carriage-return-text);
    background-color: var(--color-prettylights-syntax-carriage-return-bg)
}

.markdown-body .pl-sr .pl-cce {
    font-weight: 700;
    color: var(--color-prettylights-syntax-string-regexp)
}

.markdown-body .pl-ml {
    color: var(--color-prettylights-syntax-markup-list)
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
    font-weight: 700;
    color: var(--color-prettylights-syntax-markup-heading)
}

.markdown-body .pl-mi {
    font-style: italic;
    color: var(--color-prettylights-syntax-markup-italic)
}

.markdown-body .pl-mb {
    font-weight: 700;
    color: var(--color-prettylights-syntax-markup-bold)
}

.markdown-body .pl-md {
    color: var(--color-prettylights-syntax-markup-deleted-text);
    background-color: var(--color-prettylights-syntax-markup-deleted-bg)
}

.markdown-body .pl-mi1 {
    color: var(--color-prettylights-syntax-markup-inserted-text);
    background-color: var(--color-prettylights-syntax-markup-inserted-bg)
}

.markdown-body .pl-mc {
    color: var(--color-prettylights-syntax-markup-changed-text);
    background-color: var(--color-prettylights-syntax-markup-changed-bg)
}

.markdown-body .pl-mi2 {
    color: var(--color-prettylights-syntax-markup-ignored-text);
    background-color: var(--color-prettylights-syntax-markup-ignored-bg)
}

.markdown-body .pl-mdr {
    font-weight: 700;
    color: var(--color-prettylights-syntax-meta-diff-range)
}

.markdown-body .pl-ba {
    color: var(--color-prettylights-syntax-brackethighlighter-angle)
}

.markdown-body .pl-sg {
    color: var(--color-prettylights-syntax-sublimelinter-gutter-mark)
}

.markdown-body .pl-corl {
    text-decoration: underline;
    color: var(--color-prettylights-syntax-constant-other-reference-link)
}

.markdown-body g-emoji {
    display: inline-block;
    min-width: 1ch;
    font-family: Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
    font-size: 1em;
    font-style: normal !important;
    font-weight: var(--base-text-weight-normal, 400);
    line-height: 1;
    vertical-align: -.075em
}

.markdown-body g-emoji img {
    width: 1em;
    height: 1em
}

.markdown-body .task-list-item {
    list-style-type: none
}

.markdown-body .task-list-item label {
    font-weight: var(--base-text-weight-normal, 400)
}

.markdown-body .task-list-item.enabled label {
    cursor: pointer
}

.markdown-body .task-list-item+.task-list-item {
    margin-top: 4px
}

.markdown-body .task-list-item .handle {
    display: none
}

.markdown-body .task-list-item-checkbox {
    margin: 0 .2em .25em -1.4em;
    vertical-align: middle
}

.markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {
    margin: 0 -1.6em .25em .2em
}

.markdown-body .contains-task-list {
    position: relative
}

.markdown-body .contains-task-list:focus-within .task-list-item-convert-container,
.markdown-body .contains-task-list:hover .task-list-item-convert-container {
    display: block;
    width: auto;
    height: 24px;
    overflow: visible;
    clip: auto
}

.markdown-body ::-webkit-calendar-picker-indicator {
    filter: invert(50%)
}

.markdown-body {
    /*!
  Theme: Tokyo-night-Dark
  origin: https://github.com/enkia/tokyo-night-vscode-theme
  Description: Original highlight.js style
  Author: (c) Henri Vandersleyen <<EMAIL>>
  License: see project LICENSE
  Touched: 2022
*/
}

.markdown-body pre {
    padding: 0
}

.markdown-body code,
.markdown-body pre {
    font-family: Consolas, Monaco, Andale Mono, Ubuntu Mono, monospace
}

.markdown-body pre code {
    display: block;
    overflow-x: auto;
    padding: 1em
}

.markdown-body code {
    padding: 3px 5px
}

.markdown-body .hljs,
.markdown-body pre {
    background: #1a1b26;
    color: #cbd2ea
}

.markdown-body .hljs-comment,
.markdown-body .hljs-meta {
    color: #565f89
}

.markdown-body .hljs-deletion,
.markdown-body .hljs-doctag,
.markdown-body .hljs-regexp,
.markdown-body .hljs-selector-attr,
.markdown-body .hljs-selector-class,
.markdown-body .hljs-selector-id,
.markdown-body .hljs-selector-pseudo,
.markdown-body .hljs-tag,
.markdown-body .hljs-template-tag,
.markdown-body .hljs-variable.language_ {
    color: #f7768e
}

.markdown-body .hljs-link,
.markdown-body .hljs-literal,
.markdown-body .hljs-number,
.markdown-body .hljs-params,
.markdown-body .hljs-template-variable,
.markdown-body .hljs-type,
.markdown-body .hljs-variable {
    color: #ff9e64
}

.markdown-body .hljs-attribute,
.markdown-body .hljs-built_in {
    color: #e0af68
}

.markdown-body .hljs-keyword,
.markdown-body .hljs-property,
.markdown-body .hljs-subst,
.markdown-body .hljs-title,
.markdown-body .hljs-title.class_,
.markdown-body .hljs-title.class_.inherited__,
.markdown-body .hljs-title.function_ {
    color: #7dcfff
}

.markdown-body .hljs-selector-tag {
    color: #73daca
}

.markdown-body .hljs-addition,
.markdown-body .hljs-bullet,
.markdown-body .hljs-quote,
.markdown-body .hljs-string,
.markdown-body .hljs-symbol {
    color: #9ece6a
}

.markdown-body .hljs-code,
.markdown-body .hljs-formula,
.markdown-body .hljs-section {
    color: #7aa2f7
}

.markdown-body .hljs-attr,
.markdown-body .hljs-char.escape_,
.markdown-body .hljs-keyword,
.markdown-body .hljs-name,
.markdown-body .hljs-operator {
    color: #bb9af7
}

.markdown-body .hljs-punctuation {
    color: #c0caf5
}

.markdown-body .hljs-emphasis {
    font-style: italic
}

.markdown-body .hljs-strong {
    font-weight: 700
}

/* 右侧对话部分样式 */
.chat_chat-input-actions {
    display: flex;
    flex-wrap: wrap
}

.chat_chat-input-actions .chat_chat-input-action {
    display: inline-flex;
    border-radius: 20px;
    font-size: 12px;
    background-color: var(--white);
    color: var(--black);
    border: var(--border-in-light);
    padding: 4px 10px;
    animation: chat_slide-in__jGTU7 .3s ease;
    box-shadow: var(--card-shadow);
    transition: all .3s ease;
    margin-bottom: 10px;
    align-items: center
}

.chat_chat-input-actions .chat_chat-input-action:not(:last-child) {
    margin-right: 5px
}

.chat_prompt-toast {
    position: absolute;
    bottom: -50px;
    z-index: 999;
    display: flex;
    justify-content: center;
    width: calc(100% - 40px)
}

.chat_prompt-toast .chat_prompt-toast-inner {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    background-color: var(--white);
    color: var(--black);
    border: var(--border-in-light);
    box-shadow: var(--card-shadow);
    padding: 10px 20px;
    border-radius: 100px;
    animation: chat_slide-in-from-top__QIZOS .3s ease
}

.chat_prompt-toast .chat_prompt-toast-inner .chat_prompt-toast-content {
    margin-left: 10px
}

.dropdown-background,
.dropdown-background ul {
    background-color: var(--white);
}