<?php
error_reporting(E_ERROR);
require_once('admin/mysqlconn.php');
$row = $conn->get('user', '*', ['rndstr' => $_POST['user']]);
if (empty($row)) {
    echo '{"success":false,"message":"当前用户登录状态已过期，请刷新页面重新登录"}';
    exit;
} else {
    $userid = $row["userid"];
}
$result = $conn->select("role", "*", ["isvalid" => true, "ORDER" => 'id']);
$roleArray = array();
foreach ($result as $row) {
    $roleArray[] = array('rolevalue' => $row['rolevalue'], 'rolecode' => $row['rolecode']);
}
$prompt = urldecode($_POST['message']);
$row = $conn->get('model', '*', ['id' => $_POST["model"]]);
$modelvalue = $row["modelvalue"];
$modeltype = $row["modeltype"];
$istranslate = $row["istranslate"];
$isonline = $row["isonline"];
$isimage = $_POST["isimage"];
$imageurl = $_POST["imageurl"];
if ($isonline) {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . '/websearchapi.php?message=' . $_POST['message'];
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $url = str_replace('http://', 'https://', $url);
    }

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    $responsedata = curl_exec($ch);
    curl_close($ch);
    $jsondata = json_decode($responsedata);
    if ($jsondata->code == 200) {
        $prompt = $jsondata->online;
    }
}
$context = json_decode($_POST['context'] ?: "[]") ?: [];
$row = $conn->get('main', '*', ['id' => 1]);
$maxcontexttokens = $row["maxcontexttokens"];
function calculateTokens($content)
{
    $tokens = 0;

    // 匹配中文字符
    preg_match_all('/[\x{4e00}-\x{9fa5}]/u', $content, $chineseMatches);
    $tokens += count($chineseMatches[0]) * 2; // 每个汉字算作 2 个 token

    // 匹配英文单词
    preg_match_all('/\b\w+\b/', $content, $englishMatches);
    $tokens += count($englishMatches[0]); // 每个英文单词算作 1 个 token

    // 匹配中文标点符号
    preg_match_all('/[，。！？；：“”‘’]/u', $content, $chinesePunctuationMatches);
    $tokens += count($chinesePunctuationMatches[0]) * 2; // 每个中文标点算作 2 个 token

    // 匹配英文标点符号
    preg_match_all('/[.,!?;:\'\"()]/', $content, $englishPunctuationMatches);
    $tokens += count($englishPunctuationMatches[0]); // 每个英文标点算作 1 个 token

    return $tokens;
}
if (($row["isdrawtranslate"]) && ($istranslate)) {
    $url = 'http://' . $_SERVER['HTTP_HOST'] . '/baidutranslate.php';
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $url = str_replace('http://', 'https://', $url);
    }
    $data = array(
        'text' => $prompt
    );
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => http_build_query($data),
        ],
        "ssl" => [
            "verify_peer" => false,
            "verify_peer_name" => false,
        ],
    ]);
    // $prompt = file_get_contents($url, false, $context)??$prompt;
    $prompt = file_get_contents($url, false, $context);
}
// print_r($prompt);die;
if ($isimage == "4") { //识图模型
    if (empty($prompt))
        $prompt = "图上是什么？";
    if ($modeltype == "通义千问") {
        $postdata = [
            "model" => $modelvalue,
            "input" => [
                "messages" => [
                    [
                        "role" => "user",
                        "content" => [
                            [
                                "image" => $imageurl
                            ],
                            [
                                "text" => $prompt
                            ]
                        ]
                    ]
                ]
            ]
        ];
    } else if ($modeltype == "腾讯混元") {
        $postdata = [
            "Model" => $modelvalue,
            "Messages" => [
                [
                    "Role" => "user",
                    "Contents" => [
                        [
                            "Type" => "text",
                            "Text" => $prompt
                        ],
                        [
                            "Type" => "image_url",
                            "ImageUrl" => [
                                "Url" => $imageurl
                            ]
                        ]
                    ]
                ]
            ],
            "Stream" => true
        ];
    } else if ($modeltype == "讯飞星火") {
        $postdata = [
            "header" => [
                "app_id" => ""
            ],
            "parameter" => [
                "chat" => [
                    "domain" => "general"
                ]
            ],
            "payload" => [
                "message" => [
                    "text" => [
                        [
                            "role" => "user",
                            "content" => "base64",
                            "content_type" => "image"
                        ],
                        [
                            "role" => "user",
                            "content" => $prompt,
                            "content_type" => "text"
                        ]
                    ]
                ]
            ],
            "imageurl" => $imageurl
        ];
    } else if (($modelvalue == "gpt-4-vision-preview") || ($modelvalue == "gpt-4o") || ($modelvalue == "glm-4v")) { //glm-4v是清华智谱的识图模型，和OpenAI接口兼容
        $postdata = [
            "model" => $modelvalue,
            "temperature" => 0,
            "stream" => true,
            "messages" => [
                [
                    "role" => "user",
                    "content" => [
                        [
                            "type" => "text",
                            "text" => $prompt
                        ],
                        [
                            "type" => "image_url",
                            "image_url" => [
                                "url" => $imageurl,
                                "detail" => "high"
                            ]
                        ]
                    ]
                ]
            ],
            "max_tokens" => 2000,
        ];
    } else if ($modeltype == "midjourney") {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . '/plugins/mj/callback_describe.php';
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $url = str_replace('http://', 'https://', $url);
        }
        $postdata = [
            "base64" => "",
            "notifyHook" => $url,
            "state" => $userid,
            "imageurl" => $imageurl
        ];
    } else if ($modeltype == "bard") {
        $postdata = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "text" => $prompt
                        ],
                        [
                            "inline_data" => [
                                "mime_type" => "image/jpeg",
                                "data" => ""
                            ]
                        ]
                    ]
                ]
            ],
            "imageurl" => $imageurl
        ];
    } else if ($modeltype == "claude") {
        $postdata = [
            "model" => $modelvalue,
            "max_tokens" => 2000,
            "stream" => true,
            "messages" => [
                [
                    "role" => "user",
                    "content" => [
                        [
                            "type" => "text",
                            "text" => $prompt
                        ],
                        [
                            "type" => "image",
                            "source" => [
                                "type" => "base64",
                                "media_type" => "image/jpeg",
                                "data" => ""
                            ]
                        ]
                    ],
                    "imageurl" => $imageurl
                ]
            ]
        ];
    } else if ($modelvalue == "fuyu_8b") {
        $postdata = [
            "prompt" => $prompt,
            "image" => "",
            "imageurl" => $imageurl
        ];
    }
} else if ($isimage == "2") { //改图模型
    if ($modeltype == "midjourney") {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . '/plugins/mj/callback.php';
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $url = str_replace('http://', 'https://', $url);
        }
        if ((isset($_POST["midjourneyaction"])) && (!empty($_POST["midjourneyaction"]))) {
            $mjstr = explode(".", $_POST["midjourneyaction"]);
            $postdata = [
                "customId" => $mjstr[0] . "::" . $mjstr[1] . "::" . $mjstr[3],
                "notifyHook" => $url,
                "state" => $userid,
                "taskId" => $mjstr[2],
                "prompt" => $prompt
            ];
        } else if ((isset($_POST["blendimageurl"])) && (!empty($_POST["blendimageurl"]))) {
            $postdata = [
                "base64Array" => [],
                "dimensions" => "SQUARE",
                "notifyHook" => $url,
                "state" => $userid,
                "imageurl" => $imageurl,
                "blendimageurl" => $_POST["blendimageurl"],
                "prompt" => "![IMG](" . $imageurl . ")![IMG](" . $_POST["blendimageurl"] . ")混合图片"
            ];
        } else {
            $postdata = [
                "base64" => "",
                "notifyHook" => $url,
                "prompt" => $imageurl . " " . $prompt,
                "state" => $userid
            ];
        }
    } else {
        $postdata = [
            "model" => $modelvalue,
            "n" => 1,
            "size" => "1024x1024",
            "url" => $imageurl,
            "prompt" => $prompt
        ];
    }
} else { //文本或画图模型
    if (($modelvalue == "openai_image") || ($modeltype == "stablediffusion") || (($modeltype == "openai") && ($isimage))) {
        $postdata = [
            "prompt" => $prompt,
            "model" => $modelvalue,
            "n" => 1,
            "size" => "1024x1024"
        ];
    } else if (($modeltype == "清华智谱") && ($modelvalue == "cogview-3") && ($isimage)) {
        $postdata = [
            "prompt" => $prompt,
            "model" => $modelvalue
        ];
    } else if (($modeltype == "chimeragpt") && ($isimage)) {
        $postdata = [
            "prompt" => $prompt,
            "model" => $modelvalue,
            "n" => 1,
            "size" => "1024x1024",
            "response_format" => "url"
        ];
    } else if (($modeltype == "腾讯混元") && ($modelvalue == "TextToImageLite") && ($isimage)) {
        $postdata = [
            "Prompt" => $prompt,
            "Resolution" => "1024:1024",
            "RspImgType" => "url"
        ];
    } else if (($modeltype == "讯飞星火") && ($isimage)) {
        $postdata = [
            "header" => [
                "app_id" => ""
            ],
            "parameter" => [
                "chat" => [
                    "domain" => "general",
                    "width" => 1024,
                    "height" => 1024
                ]
            ],
            "payload" => [
                "message" => [
                    "text" => [
                        [
                            "role" => "user",
                            "content" => $prompt
                        ]
                    ]
                ]
            ]
        ];
    } else if (($modeltype == "360智脑") && ($isimage)) {
        $postdata = [
            "model" => $modelvalue,
            "style" => "realistic", //这里选择的是“写实”风格，还有papercut（剪纸）、cartoon（卡通）、CG（二次元）风格可选
            "prompt" => $prompt,
            "height" => 1024,
            "width" => 1024
        ];
    } else if (($modeltype == "通义千问") && ($isimage)) {
        $postdata = [
            "model" => $modelvalue,
            "input" => ["prompt" => $prompt],
            "parameters" => ["size" => "1024*1024", "n" => 1]
        ];
    } else if (($modeltype == "文心千帆") && ($modelvalue == "Stable-Diffusion-XL") && ($isimage)) {
        $postdata = [
            "prompt" => $prompt,
            "steps" => 50,
            "sampler_index" => "DPM++ SDE Karras"
        ];
    } else if ($modeltype == "midjourney") {
        $url = 'http://' . $_SERVER['HTTP_HOST'] . '/plugins/mj/callback.php';
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
            $url = str_replace('http://', 'https://', $url);
        }
        if (strpos($prompt, " --v 6.0") === false) {
            $prompt .= " --v 6.0";
        }
        $postdata = [
            "base64" => "",
            "notifyHook" => $url,
            "prompt" => $prompt,
            "state" => $userid
        ];
    } else { //文本模型采用同样的postdata，不同模型交给stream.php去处理
        if ($modeltype == "LinkAI") {
            $postdata = [
                "app_code" => explode(",", $modelvalue)[1],
                "model" => explode(",", $modelvalue)[0],
                "temperature" => 0,
                "stream" => true,
                "messages" => [],
            ];
        } else {
            $postdata = [
                "model" => $modelvalue,
                "temperature" => 0,
                "stream" => true,
                "messages" => [],
            ];
        }
        if (!empty($_POST["role"])) {
            foreach ($roleArray as $role) {
                if ($_POST["role"] == $role['rolevalue']) {
                    eval ($role['rolecode']);
                }
            }
        }

        if (!empty($row["fakeprompt"])) {
            $fakeprompt = explode("\n", $row["fakeprompt"]);
            $postdata['messages'][] = ['role' => 'user', 'content' => $fakeprompt[0]];
            $postdata['messages'][] = ['role' => 'assistant', 'content' => $fakeprompt[1]];
        }
        if (!empty($context)) {
            $context = array_reverse($context);
            $accumulatedContent = '';
            $reversedPostdata = [];
            foreach ($context as $message) {
                $userContent = urldecode($message[0]);
                $assistantContent = urldecode($message[1]);
                $combinedContent = $userContent . $assistantContent;
                if (calculateTokens($accumulatedContent . $combinedContent) > $maxcontexttokens) {
                    break;
                }
                $accumulatedContent .= $combinedContent;
                array_unshift($reversedPostdata, ['role' => 'assistant', 'content' => $assistantContent]);
                array_unshift($reversedPostdata, ['role' => 'user', 'content' => $userContent]);
            }
            $postdata['messages'] = array_merge($reversedPostdata, $postdata['messages'] ?? []);
        }
        $postdata['messages'][] = ['role' => 'user', 'content' => $prompt];
    }
}
$postdata = json_encode($postdata);
// $postdata = addslashes($postdata);

$conn->update('user', ['lastquestion' => $_POST['conversationid'] . "," . $postdata, 'lastmodelid' => $_POST["model"]], ['rndstr' => $_POST['user']]);
echo '{"success":true}';
