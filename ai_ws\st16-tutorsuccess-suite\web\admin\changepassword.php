<?php
require_once('check_admin.php');
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "change")) {
    require_once('mysqlconn.php');
    $table_name = ($_SESSION['usertype'] == '3')?'judge':'officer';
    $row = $conn->get($table_name,'*',['id'=>$_SESSION['operatorid']]);

    if ($row['password'] == md5(md5(md5(md5($_REQUEST["oldpass"]) . "Libra")) . "chatgpt@2023")) {
        $conn->update($table_name,['password'=>md5(md5($_REQUEST["password"]) . "chatgpt@2023")],['id'=>$_SESSION['operatorid']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('旧密码不匹配，请重试。');</script>";
    }
    exit;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>修改密码</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">修改密码</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" target=temp onsubmit="return checkform();">
                            <input type=hidden name="action" value="change">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">旧密码：</label>

                                <div class="col-lg-4">
                                    <input type="text" value="admin" style="position: absolute;z-index: -1;" disabled autocomplete="off" /><!-- 这个username会被浏览器记住，我随便用个admin-->
                                    <input type="password" value=" " style="position: absolute;z-index: -1;" disabled autocomplete="off" />
                                    <input type="password" id="oldpass" name="oldpass" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">新密码：</label>

                                <div class="col-lg-4">
                                    <input type="password" onchange="document.getElementById('password').value=md5(md5(this.value)+'Libra');" id="password1" name="password1" class="bg-focus form-control" autoComplete="off">
                                    <input type=hidden id=password name=password>
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">重复输入：</label>

                                <div class="col-lg-4">
                                    <input type="password" id="password2" name="password2" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                                <div class="col-lg-6 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认修改</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
    <script>
        function checkform() {
            if ((document.getElementById("password1").value == document.getElementById("password2").value) && (document.getElementById("password1").value.length > 5)) {
                return true;
            } else {
                alert("两次密码输入不一致或长度太短！\n请重新输入。");
                return false;
            }
        }
    </script>
</body>

</html>