<?php
set_time_limit(0);

require_once('mysqlconn.php');
echo "<html><head><meta charset=utf-8></head><body style='font-size:18px;margin:30px;'><p>开始更新</p><hr>";
if (!extension_loaded('zip')) {
    echo "<p style='color:red'>缺少zip相关组件，请安装并启用zip扩展</p><p style='color:red'>更新失败！</p>";
    exit;
}
$row = $conn->get('main','*',['id'=>1]);
$updateurl = $row["updateurl"];
$license = $row["license"];
$version = $row["version"];

if (empty($license)) {
    exit;
}
$url = str_replace("checkupdate.php", "downloadupdatefile.php", $updateurl);
echo "<p>正在申请下载更新包：" . $url . "</p>";
flush();
$postData = array('license' => $license, 'version' => $version);
$savePath = $_SERVER['DOCUMENT_ROOT'] . '/update.zip';

$ch = curl_init();
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($ch, CURLOPT_HEADER, 0);
$fileContent = curl_exec($ch);
curl_close($ch);
if (!empty($fileContent)) {
    file_put_contents($savePath, $fileContent);
    echo "<p>更新包已下载完成</p><p>正在更新文件</p>";
} else {
    echo "<p style='color:red'>更新包下载失败</p>";
    exit;
}
flush();

$zip = new ZipArchive;
if ($zip->open($_SERVER['DOCUMENT_ROOT'] . '/update.zip') === TRUE) {
    $zip->extractTo($_SERVER['DOCUMENT_ROOT']);
    $zip->close();
    echo "<p>文件更新成功</p>";
} else {
    echo "<p style='color:red'>文件更新失败</p>";
    exit;
}
flush();
$filename = $_SERVER['DOCUMENT_ROOT'] . '/update.zip';
if (file_exists($filename)) {
    unlink($filename);
}

$filename = $_SERVER['DOCUMENT_ROOT'] . '/install.php';
if (file_exists($filename)) {
    echo "<p>正在执行安装脚本</p><iframe style='border:0;width:100%;height:50%;' src='/install.php'></iframe>";
} else {
    echo "<hr><p style='color:darkgreen'>已完成全部更新动作，更新成功！</p>";
}
