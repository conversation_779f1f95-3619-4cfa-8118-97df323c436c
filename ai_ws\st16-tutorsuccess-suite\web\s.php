<?php
require_once('admin/mysqlconn.php');
$row = $conn->get('main','*',['id'=>1]);
$weixinaddress = $row["weixinaddress"];
$newweixinaddress = $row["newweixinaddress"];

if (!empty($newweixinaddress)) {
    if (isset($_GET["m"])) {
        header("Location: " . $newweixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Ffrom%3Dwx%26new%3D1%26userrndstr%3D" . $_GET["s"]);
    } else {
        header("Location: " . $newweixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Fnew%3D1%26userrndstr%3D" . $_GET["s"]);
    }
} else {
    if (isset($_GET["m"])) {
        header("Location: " . $weixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Ffrom%3Dwx%26userrndstr%3D" . $_GET["s"]);
    } else {
        header("Location: " . $weixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Fuserrndstr%3D" . $_GET["s"]);
    }
}
