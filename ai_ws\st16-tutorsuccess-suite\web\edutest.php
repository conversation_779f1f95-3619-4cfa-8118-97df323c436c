<?php
/**
 * 教育系统测试文件
 */

echo "<h1>教育系统测试页面</h1>";
echo "<p>当前时间：" . date('Y-m-d H:i:s') . "</p>";
echo "<p>PHP版本：" . phpversion() . "</p>";
echo "<p>REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "</p>";

// 测试目录结构
$appPath = dirname(__DIR__) . '/education';
echo "<p>APP_PATH: $appPath</p>";
echo "<p>APP_PATH存在: " . (is_dir($appPath) ? '是' : '否') . "</p>";

$configFile = $appPath . '/config/config.php';
echo "<p>配置文件: $configFile</p>";
echo "<p>配置文件存在: " . (file_exists($configFile) ? '是' : '否') . "</p>";

$coreDir = $appPath . '/core';
echo "<p>核心目录: $coreDir</p>";
echo "<p>核心目录存在: " . (is_dir($coreDir) ? '是' : '否') . "</p>";

if (is_dir($coreDir)) {
    $files = scandir($coreDir);
    echo "<p>核心文件: " . implode(', ', array_filter($files, function($f) { return $f !== '.' && $f !== '..'; })) . "</p>";
}

// 测试直接访问教师主页原型
echo "<h2>原型页面测试</h2>";
$prototypeFile = dirname(__DIR__) . '/原型/teacher/dashboard.html';
echo "<p>原型文件: $prototypeFile</p>";
echo "<p>原型文件存在: " . (file_exists($prototypeFile) ? '是' : '否') . "</p>";

echo '<p><a href="../原型/teacher/dashboard.html" target="_blank">访问教师主页原型</a></p>';
echo '<p><a href="education.php" target="_blank">访问教育系统入口</a></p>';
?>
