<?php
/**
 * 教培系统Web入口文件
 * 修订日期：2025-01-22
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 启动会话
session_start();

// 定义应用根目录
define('APP_PATH', dirname(__DIR__) . '/education');
define('WEB_PATH', __DIR__);

// 自动加载函数
spl_autoload_register(function ($class) {
    // 处理命名空间
    $class = str_replace('\\', '/', $class);
    
    // 核心类
    if (strpos($class, 'Core/') === 0) {
        $file = APP_PATH . '/core/' . substr($class, 5) . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
    
    // 控制器类
    if (strpos($class, 'Controller') !== false) {
        $file = APP_PATH . '/controllers/' . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
    
    // 模型类
    if (strpos($class, 'Model') !== false) {
        $file = APP_PATH . '/models/' . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// 加载配置文件
require_once APP_PATH . '/config/config.php';

try {
    // 调试信息
    echo "<!-- DEBUG: APP_PATH: " . APP_PATH . " -->\n";
    echo "<!-- DEBUG: REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . " -->\n";

    // 加载路由
    require_once APP_PATH . '/config/routes.php';

    // 启动应用
    $app = new \Core\Application();
    $app->run();
} catch (Exception $e) {
    // 错误处理
    echo '<h1>系统错误</h1>';
    echo '<p>错误信息：' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p>错误文件：' . htmlspecialchars($e->getFile()) . '</p>';
    echo '<p>错误行号：' . $e->getLine() . '</p>';

    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    }
}
