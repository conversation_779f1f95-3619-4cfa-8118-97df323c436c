<?php
/**
 * 验证码生成类
 */
class VerifyCode {
    private $width = 120;      // 验证码图片宽度
    private $height = 40;      // 验证码图片高度
    private $codeLength = 4;   // 验证码长度
    private $fontSize = 20;    // 字体大小
    private $sessionKey = 'verify_code'; // 存储验证码的session键名
    
    /**
     * 构造函数，可自定义验证码参数
     * 
     * @param array $options 自定义参数
     */
    public function __construct(array $options = []) {
        if (!empty($options)) {
            foreach ($options as $key => $value) {
                if (property_exists($this, $key)) {
                    $this->$key = $value;
                }
            }
        }
    }
    
    /**
     * 生成验证码并输出图像
     * 
     * @return void
     */
    public function generate() {
        // 创建图像资源
        $image = imagecreatetruecolor($this->width, $this->height);
        
        // 设置背景色
        $bgColor = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bgColor);
        
        // 生成随机验证码
        $code = $this->generateRandomCode();
        
        // 保存验证码到session
        $_SESSION[$this->sessionKey] = strtolower($code);
        
        // 绘制干扰线
        $this->drawDisturbLines($image);
        
        // 绘制干扰点
        $this->drawDisturbDots($image);
        
        // 绘制验证码字符
        $this->drawCode($image, $code);
        
        // 输出图像
        header('Content-Type: image/png');
        imagepng($image);
        
        // 释放资源
        imagedestroy($image);
    }
    
    /**
     * 生成随机验证码
     * 
     * @return string 生成的验证码
     */
    private function generateRandomCode() {
        // 验证码字符集，不包含容易混淆的字符
        $chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        $code = '';
        
        for ($i = 0; $i < $this->codeLength; $i++) {
            $code .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        
        return $code;
    }
    
    /**
     * 绘制干扰线
     * 
     * @param resource $image 图像资源
     * @return void
     */
    private function drawDisturbLines($image) {
        for ($i = 0; $i < 6; $i++) {
            $lineColor = imagecolorallocate(
                $image, 
                mt_rand(100, 200), 
                mt_rand(100, 200), 
                mt_rand(100, 200)
            );
            
            imageline(
                $image,
                mt_rand(0, $this->width), 
                mt_rand(0, $this->height), 
                mt_rand(0, $this->width), 
                mt_rand(0, $this->height), 
                $lineColor
            );
        }
    }
    
    /**
     * 绘制干扰点
     * 
     * @param resource $image 图像资源
     * @return void
     */
    private function drawDisturbDots($image) {
        for ($i = 0; $i < 100; $i++) {
            $dotColor = imagecolorallocate(
                $image, 
                mt_rand(100, 200), 
                mt_rand(100, 200), 
                mt_rand(100, 200)
            );
            
            imagesetpixel(
                $image, 
                mt_rand(0, $this->width), 
                mt_rand(0, $this->height), 
                $dotColor
            );
        }
    }
    
    /**
     * 绘制验证码字符
     * 
     * @param resource $image 图像资源
     * @param string $code 验证码字符串
     * @return void
     */
    private function drawCode($image, $code) {
        // 计算每个字符的宽度
        $codeWidth = $this->width / $this->codeLength;
        
        for ($i = 0; $i < $this->codeLength; $i++) {
            $textColor = imagecolorallocate(
                $image, 
                mt_rand(20, 100), 
                mt_rand(20, 100), 
                mt_rand(20, 100)
            );
            
            // 随机倾斜角度
            $angle = mt_rand(-15, 15);
            
            // 计算字符位置
            $x = $codeWidth * $i + ($codeWidth - $this->fontSize) / 2;
            $y = $this->height / 2 + $this->fontSize / 2;
            
            // 绘制字符
            imagettftext(
                $image, 
                $this->fontSize, 
                $angle, 
                $x, 
                $y, 
                $textColor, 
                __DIR__ . '/../public/fonts/arial.ttf', // 字体文件路径
                $code[$i]
            );
        }
    }
    
    /**
     * 验证用户输入的验证码
     * 
     * @param string $userInput 用户输入的验证码
     * @param bool $caseSensitive 是否区分大小写
     * @return bool 验证结果
     */
    public static function verify($userInput, $caseSensitive = false) {
        if (empty($_SESSION['verify_code'])) {
            return false;
        }
        
        $savedCode = $_SESSION['verify_code'];
        
        if (!$caseSensitive) {
            $userInput = strtolower($userInput);
        }
        
        return $userInput === $savedCode;
    }
    
    /**
     * 清除验证码session
     * 
     * @return void
     */
    public static function clear() {
        if (isset($_SESSION['verify_code'])) {
            unset($_SESSION['verify_code']);
        }
    }
}