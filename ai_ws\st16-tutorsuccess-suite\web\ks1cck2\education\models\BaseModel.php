<?php
/**
 * 基础数据模型类
 * 创建时间：2025-01-22
 */

class BaseModel {
    protected $conn;
    protected $table;
    protected $primary_key = 'id';
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * 查找单条记录
     * @param int $id
     * @return array|null
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primary_key} = ? LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('i', $id);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
    
    /**
     * 查找所有记录
     * @param array $conditions 查询条件
     * @param string $order_by 排序
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function findAll($conditions = [], $order_by = '', $limit = 0, $offset = 0) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        $types = '';
        
        if (!empty($conditions)) {
            $where_clauses = [];
            foreach ($conditions as $field => $value) {
                if (is_array($value)) {
                    // IN 查询
                    $placeholders = str_repeat('?,', count($value) - 1) . '?';
                    $where_clauses[] = "{$field} IN ({$placeholders})";
                    foreach ($value as $v) {
                        $params[] = $v;
                        $types .= is_int($v) ? 'i' : 's';
                    }
                } else {
                    $where_clauses[] = "{$field} = ?";
                    $params[] = $value;
                    $types .= is_int($value) ? 'i' : 's';
                }
            }
            $sql .= " WHERE " . implode(' AND ', $where_clauses);
        }
        
        if ($order_by) {
            $sql .= " ORDER BY {$order_by}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        
        $records = [];
        while ($row = $result->fetch_assoc()) {
            $records[] = $row;
        }
        
        return $records;
    }
    
    /**
     * 创建记录
     * @param array $data
     * @return int|false 返回插入的ID或false
     */
    public function create($data) {
        $fields = array_keys($data);
        $placeholders = str_repeat('?,', count($fields) - 1) . '?';
        
        $sql = "INSERT INTO {$this->table} (" . implode(',', $fields) . ") VALUES ({$placeholders})";
        $stmt = $this->conn->prepare($sql);
        
        $types = '';
        $values = [];
        foreach ($data as $value) {
            $values[] = $value;
            $types .= is_int($value) ? 'i' : 's';
        }
        
        $stmt->bind_param($types, ...$values);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * 更新记录
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function update($id, $data) {
        $set_clauses = [];
        $types = '';
        $values = [];
        
        foreach ($data as $field => $value) {
            $set_clauses[] = "{$field} = ?";
            $values[] = $value;
            $types .= is_int($value) ? 'i' : 's';
        }
        
        $values[] = $id;
        $types .= 'i';
        
        $sql = "UPDATE {$this->table} SET " . implode(',', $set_clauses) . " WHERE {$this->primary_key} = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$values);
        
        return $stmt->execute();
    }
    
    /**
     * 删除记录
     * @param int $id
     * @return bool
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primary_key} = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('i', $id);
        return $stmt->execute();
    }
    
    /**
     * 软删除（更新状态）
     * @param int $id
     * @return bool
     */
    public function softDelete($id) {
        return $this->update($id, ['status' => STATUS_INACTIVE]);
    }
    
    /**
     * 统计记录数
     * @param array $conditions
     * @return int
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) as total FROM {$this->table}";
        $params = [];
        $types = '';
        
        if (!empty($conditions)) {
            $where_clauses = [];
            foreach ($conditions as $field => $value) {
                $where_clauses[] = "{$field} = ?";
                $params[] = $value;
                $types .= is_int($value) ? 'i' : 's';
            }
            $sql .= " WHERE " . implode(' AND ', $where_clauses);
        }
        
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return intval($row['total']);
    }
    
    /**
     * 分页查询
     * @param int $page 页码
     * @param int $per_page 每页数量
     * @param array $conditions 查询条件
     * @param string $order_by 排序
     * @return array
     */
    public function paginate($page = 1, $per_page = 20, $conditions = [], $order_by = '') {
        $total = $this->count($conditions);
        $total_pages = ceil($total / $per_page);
        $offset = ($page - 1) * $per_page;
        
        $records = $this->findAll($conditions, $order_by, $per_page, $offset);
        
        return [
            'data' => $records,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $per_page,
                'total' => $total,
                'total_pages' => $total_pages,
                'has_prev' => $page > 1,
                'has_next' => $page < $total_pages
            ]
        ];
    }
    
    /**
     * 执行原生SQL查询
     * @param string $sql
     * @param array $params
     * @return array
     */
    public function query($sql, $params = []) {
        $stmt = $this->conn->prepare($sql);
        
        if (!empty($params)) {
            $types = '';
            foreach ($params as $param) {
                $types .= is_int($param) ? 'i' : 's';
            }
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        $result = $stmt->get_result();
        
        $records = [];
        while ($row = $result->fetch_assoc()) {
            $records[] = $row;
        }
        
        return $records;
    }
    
    /**
     * 获取最后插入的ID
     * @return int
     */
    public function getLastInsertId() {
        return $this->conn->insert_id;
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        $this->conn->autocommit(false);
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        $this->conn->commit();
        $this->conn->autocommit(true);
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        $this->conn->rollback();
        $this->conn->autocommit(true);
    }
}
