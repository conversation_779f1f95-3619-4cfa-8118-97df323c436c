<?php
session_start();
function generateCaptcha()
{
    $captcha = '';
    $chars = '23456789abcdefghijkmnpqrstuvwxyzABCDEFGHIJKLMNPQRSTUVWXYZ';
    for ($i = 0; $i < 4; $i++) {
        $captcha .= $chars[rand(0, strlen($chars) - 1)];
    }
    $_SESSION['captcha'] = strtolower($captcha);
    $image = imagecreate(55, 30);
    $bg_color = imagecolorallocate($image, 200, 200, 200);
    $text_color = imagecolorallocate($image, 0, 0, 0);
    imagestring($image, 5, 10, 8, $captcha, $text_color);
    header('Content-Type: image/png');
    imagepng($image);
    imagedestroy($image);
}

generateCaptcha();
