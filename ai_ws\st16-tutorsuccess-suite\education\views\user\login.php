<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt"></i> 用户登录</h4>
            </div>
            <div class="card-body">
                <form id="loginForm" method="post" action="/login">
                    <div class="mb-3">
                        <label for="email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="verifycode" class="form-label">验证码</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="verifycode" name="verifycode" required>
                            <span class="input-group-text p-0">
                                <img src="/verifycode" alt="验证码" id="verifycodeImg" style="height: 38px; cursor: pointer;" onclick="refreshVerifyCode()">
                            </span>
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">记住我</label>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">登录</button>
                    </div>
                </form>
                
                <div class="mt-3 text-center">
                    <p>还没有账号？<a href="/register">立即注册</a></p>
                </div>
                
                <div class="mt-4">
                    <div class="text-center mb-3">或使用以下方式登录</div>
                    <div class="d-grid gap-2">
                        <a href="/wxlogin" class="btn btn-success">
                            <i class="fab fa-weixin"></i> 微信一键登录
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshVerifyCode() {
    document.getElementById('verifycodeImg').src = '/verifycode?' + Math.random();
}

document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/login', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect || '/';
        } else {
            alert(data.message);
            refreshVerifyCode();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('登录失败，请稍后再试');
        refreshVerifyCode();
    });
});
</script>