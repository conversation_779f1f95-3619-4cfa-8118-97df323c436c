<?php
use Medoo\Medoo;
if (isset($_GET["from"])) {
    $ismobile = 1;
} else {
    $ismobile = 0;
}
if (isset($_GET["openid"])) {
    if (trim($_GET["openid"]) == "") {
        echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录失败</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>未获取到用户信息</h1><p>请再试一次</p>';
        if ($ismobile) {
            echo '<script>setTimeout(function(){window.location.href="http://' . $_SERVER['HTTP_HOST'] . '"},2e3)</script>';
        }
        exit;
    }
    require_once('admin/mysqlconn.php');
    $row = $conn->get('main','*',['id'=>1]);
    $freetry = $row["freetry"];
    $freedays = $row["freedays"];
    $weixinaddress = $row["weixinaddress"];
    $weixinredirecturl = $row["weixinredirecturl"];
    if (isset($_GET["new"])) { //更换过的新服务号
        $row = $conn->get('user','*',['newopenid'=>$_GET["openid"]]);
        if (empty($row)) { //第一次用新服务号登录的用户，准备跳转
            if (isset($_GET["m"])) {
                header("Location: " . $weixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Ffrom%3Dwx%26newopenid%3D" . $_GET["openid"] . "%26userrndstr%3D" . $_GET["userrndstr"]);
            } else {
                header("Location: " . $weixinaddress . "?url=http%3A%2F%2F" . $_SERVER['HTTP_HOST'] . "%2Fwxuserlogin.php%3Fnewopenid%3D" . $_GET["openid"] . "%26userrndstr%3D" . $_GET["userrndstr"]);
            }
            exit(0);
        } else { //老用户
            if ($row["isforbidden"]) {
                echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录失败</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>对不起</h1><p>您的账号已被封禁</p>';
            } else {
                $conn->update('user',['rndstr'=>$_GET['userrndstr'],'ismobile'=>$ismobile,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
                if ($ismobile) {
                    header("Location: http://" . $_SERVER['HTTP_HOST']);
                } else {
                    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>欢迎回来</h1><p>您已完成登录</p>';
                    if (!empty($weixinredirecturl)) {
                        echo '<script>setTimeout(function(){window.location.href="' . $weixinredirecturl . '"},2e3)</script>';
                    }
                    echo '</body></html>';
                }
            }
        }
    } else if (isset($_GET["newopenid"])) { //第一次用新服务号登录的用户，拿到旧服务号的openid回来了
        $row = $conn->get('user','*',['openid'=>$_GET["openid"]]);
        if (empty($row)) { //第一次用新服务号登录的用户，没关联到旧openid（纯新用户）
            $conn->insert('user',['openid'=>$_GET["openid"],'newopenid'=>$_GET["newopenid"],'rndstr'=>$_GET["userrndstr"],'quota'=>$freetry,'expiretime'=>date('Y-m-d H:i:s', strtotime('+' . $freedays . ' day')),'registertime'=>date('Y-m-d H:i:s'),'loginip'=>$_SERVER["REMOTE_ADDR"],'logintime'=>date('Y-m-d H:i:s'),'ismobile'=>$ismobile]);
            if (!$conn->error) {
                $conn->update('user',['userid'=>Medoo::raw('id+1001')],['rndstr'=>$_GET["userrndstr"]]);
                if ($ismobile) {
                    header("Location: http://" . $_SERVER['HTTP_HOST']);
                } else {
                    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>欢迎新用户</h1><p>您已完成登录</p>';
                    if (!empty($weixinredirecturl)) {
                        echo '<script>setTimeout(function(){window.location.href="' . $weixinredirecturl . '"},2e3)</script>';
                    }
                    echo '</body></html>';
                }
            } else {
                echo $conn->error;
            }
        } else { //第一次用新服务号登录的用户，关联到了旧openid（老用户）
            if ($row["isforbidden"]) {
                echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录失败</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>对不起</h1><p>您的账号已被封禁</p>';
            } else {
                $conn->update('user',['rndstr'=>$_GET['userrndstr'],'newopenid'=>$_GET['newopenid'],'ismobile'=>$ismobile,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
                if ($ismobile) {
                    header("Location: http://" . $_SERVER['HTTP_HOST']);
                } else {
                    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>欢迎回来</h1><p>您已完成登录</p>';
                    if (!empty($weixinredirecturl)) {
                        echo '<script>setTimeout(function(){window.location.href="' . $weixinredirecturl . '"},2e3)</script>';
                    }
                    echo '</body></html>';
                }
            }
        }
    } else {
        $row = $conn->get('user','*',['openid'=>$_GET["openid"]]);
        if (empty($row)) {
            $conn->insert('user',['openid'=>$_GET["openid"],'rndstr'=>$_GET["userrndstr"],'quota'=>$freetry,'expiretime'=>date('Y-m-d H:i:s', strtotime('+' . $freedays . ' day')),'registertime'=>date('Y-m-d H:i:s'),'loginip'=>$_SERVER["REMOTE_ADDR"],'logintime'=>date('Y-m-d H:i:s'),'ismobile'=>$ismobile]);
            if (!$conn->error) {
                $conn->update('user',['userid'=>Medoo::raw('id+1001')],['rndstr'=>$_GET["userrndstr"]]);
                if ($ismobile) {
                    header("Location: http://" . $_SERVER['HTTP_HOST']);
                } else {
                    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>欢迎新用户</h1><p>您已完成登录</p>';
                    if (!empty($weixinredirecturl)) {
                        echo '<script>setTimeout(function(){window.location.href="' . $weixinredirecturl . '"},2e3)</script>';
                    }
                    echo '</body></html>';
                }
            } else {
                echo $conn->error;
            }
        } else {
            if ($row["isforbidden"]) {
                echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录失败</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>对不起</h1><p>您的账号已被封禁</p>';
            } else {
                $conn->update('user',['rndstr'=>$_GET['userrndstr'],'ismobile'=>$ismobile,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
                if ($ismobile) {
                    header("Location: http://" . $_SERVER['HTTP_HOST']);
                } else {
                    echo '<!DOCTYPE html><html><head><meta charset="UTF-8"><title>登录成功</title><style>body{background-color:#f2f2f2;font-family:Arial,sans-serif;font-size:18px;color:#333;text-align:center;padding-top:50px}h1{font-size:48px;margin-bottom:20px}p{font-size:36px;margin-bottom:40px}</style></head><body><h1>欢迎回来</h1><p>您已完成登录</p>';
                    if (!empty($weixinredirecturl)) {
                        echo '<script>setTimeout(function(){window.location.href="' . $weixinredirecturl . '"},2e3)</script>';
                    }
                    echo '</body></html>';
                }
            }
        }
    }
}
