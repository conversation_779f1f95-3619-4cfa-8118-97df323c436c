<?php
/**
 * 教师请假审批页面
 * 修订日期：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../../../_ks1.php';
require_once '../../../mysqlconn.php';
require_once '../../init.php';

// 检查是否已登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: ../../login.php');
    exit;
}

$current_user = get_current_login_user();
$user_id = $current_user['id'];
$user_roles = get_user_roles($user_id);

// 检查是否有教师权限
if (!in_array(ROLE_TEACHER, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
    header('Location: ../index.php');
    exit;
}

// 模拟请假申请数据
$leave_requests = [
    [
        'id' => 1,
        'student_name' => '张同学',
        'student_phone' => '138****5678',
        'course_name' => '数学 - 代数基础',
        'course_time' => '2025-01-22 09:00-10:30',
        'classroom' => '教室A101',
        'apply_time' => '2025-01-21 10:30',
        'start_date' => '2025-01-22',
        'end_date' => '2025-01-22',
        'reason' => '身体不适，需要看医生',
        'status' => 'pending',
        'priority' => 'normal'
    ],
    [
        'id' => 2,
        'student_name' => '王同学',
        'student_phone' => '139****1234',
        'course_name' => '数学 - 几何基础',
        'course_time' => '2025-01-22 14:00-15:30',
        'classroom' => '教室A102',
        'apply_time' => '2025-01-21 09:15',
        'start_date' => '2025-01-22',
        'end_date' => '2025-01-22',
        'reason' => '家庭事务，需要陪同家长办事',
        'status' => 'pending',
        'priority' => 'normal'
    ],
    [
        'id' => 3,
        'student_name' => '李同学',
        'student_phone' => '137****9876',
        'course_name' => '数学 - 综合练习',
        'course_time' => '2025-01-23 16:00-17:30',
        'classroom' => '教室A101',
        'apply_time' => '2025-01-21 16:45',
        'start_date' => '2025-01-23',
        'end_date' => '2025-01-25',
        'reason' => '参加学校活动，连续三天无法上课',
        'status' => 'pending',
        'priority' => 'urgent'
    ],
    [
        'id' => 4,
        'student_name' => '赵同学',
        'student_phone' => '136****5432',
        'course_name' => '数学 - 代数基础',
        'course_time' => '2025-01-20 09:00-10:30',
        'classroom' => '教室A101',
        'apply_time' => '2025-01-19 14:20',
        'start_date' => '2025-01-20',
        'end_date' => '2025-01-20',
        'reason' => '突发疾病，无法参加课程',
        'status' => 'approved',
        'priority' => 'urgent',
        'approved_by' => '李老师',
        'approved_at' => '2025-01-19 15:30',
        'comment' => '同意请假，注意身体健康'
    ]
];

// 获取过滤条件
$status_filter = $_GET['status'] ?? 'all';
$priority_filter = $_GET['priority'] ?? 'all';

// 过滤数据
$filtered_requests = array_filter($leave_requests, function($request) use ($status_filter, $priority_filter) {
    $status_match = ($status_filter === 'all') || ($request['status'] === $status_filter);
    $priority_match = ($priority_filter === 'all') || ($request['priority'] === $priority_filter);
    return $status_match && $priority_match;
});

$page_title = '请假审批 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .leave-request-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
        }
        .leave-request-card:hover {
            border-color: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: bold;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-approved {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .priority-badge {
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 8px;
        }
        .priority-normal {
            background-color: #e2e3e5;
            color: #495057;
        }
        .priority-urgent {
            background-color: #f5c6cb;
            color: #721c24;
        }
        .btn-approve {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 0.9rem;
        }
        .btn-reject {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 0.9rem;
        }
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .stats-row {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="../../index.php">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="leave_approval.php">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="parent_binding.php">
                            <i class="fa fa-users"></i> 家长绑定
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> <?php echo safe_html($current_user['name'] ?? $current_user['email']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../../login.php?action=logout"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 统计信息 -->
        <div class="stats-row">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count(array_filter($leave_requests, function($r) { return $r['status'] === 'pending'; })); ?></div>
                        <div class="stat-label">待审批</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count(array_filter($leave_requests, function($r) { return $r['status'] === 'approved'; })); ?></div>
                        <div class="stat-label">已批准</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count(array_filter($leave_requests, function($r) { return $r['priority'] === 'urgent'; })); ?></div>
                        <div class="stat-label">紧急申请</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-item">
                        <div class="stat-number"><?php echo count($leave_requests); ?></div>
                        <div class="stat-label">总申请数</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 过滤器 -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5><i class="fa fa-filter"></i> 筛选条件</h5>
                </div>
                <div class="col-md-6">
                    <div class="row">
                        <div class="col-md-6">
                            <select class="form-select" id="statusFilter" onchange="applyFilter()">
                                <option value="all" <?php echo $status_filter === 'all' ? 'selected' : ''; ?>>全部状态</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>待审批</option>
                                <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>已批准</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>已拒绝</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" id="priorityFilter" onchange="applyFilter()">
                                <option value="all" <?php echo $priority_filter === 'all' ? 'selected' : ''; ?>>全部优先级</option>
                                <option value="normal" <?php echo $priority_filter === 'normal' ? 'selected' : ''; ?>>普通</option>
                                <option value="urgent" <?php echo $priority_filter === 'urgent' ? 'selected' : ''; ?>>紧急</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 请假申请列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list"></i> 请假申请列表</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($filtered_requests)): ?>
                            <div class="text-center text-muted py-5">
                                <i class="fa fa-inbox fa-3x mb-3"></i>
                                <p>暂无符合条件的请假申请</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($filtered_requests as $request): ?>
                                <div class="leave-request-card">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <?php echo safe_html($request['student_name']); ?> - <?php echo safe_html($request['course_name']); ?>
                                                        <span class="priority-badge priority-<?php echo $request['priority']; ?> ms-2">
                                                            <?php echo $request['priority'] === 'urgent' ? '紧急' : '普通'; ?>
                                                        </span>
                                                    </h6>
                                                    <span class="status-badge status-<?php echo $request['status']; ?>">
                                                        <?php 
                                                        $status_text = [
                                                            'pending' => '待审批',
                                                            'approved' => '已批准',
                                                            'rejected' => '已拒绝'
                                                        ];
                                                        echo $status_text[$request['status']];
                                                        ?>
                                                    </span>
                                                </div>
                                            </div>
                                            
                                            <div class="row text-muted small">
                                                <div class="col-md-6">
                                                    <div><i class="fa fa-clock"></i> 课程时间：<?php echo $request['course_time']; ?></div>
                                                    <div><i class="fa fa-map-marker"></i> 上课地点：<?php echo safe_html($request['classroom']); ?></div>
                                                    <div><i class="fa fa-calendar"></i> 请假日期：<?php echo $request['start_date']; ?> 至 <?php echo $request['end_date']; ?></div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div><i class="fa fa-phone"></i> 联系方式：<?php echo safe_html($request['student_phone']); ?></div>
                                                    <div><i class="fa fa-calendar-plus"></i> 申请时间：<?php echo $request['apply_time']; ?></div>
                                                    <?php if ($request['status'] !== 'pending'): ?>
                                                        <div><i class="fa fa-user"></i> 审批人：<?php echo safe_html($request['approved_by'] ?? '系统'); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <div class="mt-3">
                                                <strong>请假原因：</strong><?php echo safe_html($request['reason']); ?>
                                            </div>
                                            
                                            <?php if (isset($request['comment']) && !empty($request['comment'])): ?>
                                                <div class="mt-2">
                                                    <strong>审批意见：</strong><?php echo safe_html($request['comment']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-4 text-end">
                                            <?php if ($request['status'] === 'pending'): ?>
                                                <button class="btn btn-approve me-2 mb-2" onclick="approveLeave(<?php echo $request['id']; ?>, '<?php echo safe_html($request['student_name']); ?>', '<?php echo safe_html($request['course_name']); ?>')">
                                                    <i class="fa fa-check"></i> 批准
                                                </button>
                                                <button class="btn btn-reject mb-2" onclick="rejectLeave(<?php echo $request['id']; ?>, '<?php echo safe_html($request['student_name']); ?>', '<?php echo safe_html($request['course_name']); ?>')">
                                                    <i class="fa fa-times"></i> 拒绝
                                                </button>
                                            <?php else: ?>
                                                <div class="text-muted">
                                                    <i class="fa fa-check-circle"></i> 已处理<br>
                                                    <small><?php echo $request['approved_at'] ?? ''; ?></small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审批确认模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">请假审批</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="approvalModalBody">
                    <!-- 审批内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmApproval">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function applyFilter() {
            const status = document.getElementById('statusFilter').value;
            const priority = document.getElementById('priorityFilter').value;
            
            const url = new URL(window.location);
            url.searchParams.set('status', status);
            url.searchParams.set('priority', priority);
            window.location.href = url.toString();
        }

        function approveLeave(leaveId, student, subject) {
            document.getElementById('approvalModalTitle').textContent = '批准请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认批准 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">审批意见（可选）：</label>
                    <textarea class="form-control" id="approvalComment" rows="3" placeholder="请输入审批意见..."></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const comment = document.getElementById('approvalComment').value;
                
                // 这里应该调用实际的API
                alert('请假申请已批准');
                location.reload();
                
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        function rejectLeave(leaveId, student, subject) {
            document.getElementById('approvalModalTitle').textContent = '拒绝请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认拒绝 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">拒绝原因：</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请输入拒绝原因..." required></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const reason = document.getElementById('rejectReason').value;
                if (!reason.trim()) {
                    alert('请输入拒绝原因');
                    return;
                }
                
                // 这里应该调用实际的API
                alert('请假申请已拒绝');
                location.reload();
                
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }
    </script>
</body>
</html>
