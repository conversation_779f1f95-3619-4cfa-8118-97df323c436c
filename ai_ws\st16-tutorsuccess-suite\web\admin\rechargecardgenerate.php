<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_REQUEST["cardtype"])) {

    $batch_size = $_REQUEST["amount"];
    $password_length = 18;
    $existing_passwords = array();

    function generate_password($length, $existing_passwords)
    {
        $characters = '23456789abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ';
        $password = '';
        do {
            for ($i = 0; $i < $length; $i++) {
                $password .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (in_array($password, $existing_passwords));
        return $password;
    }

    for ($i = 0; $i < $batch_size; $i++) {
        $password = generate_password($password_length, $existing_passwords);
        $existing_passwords[] = $password;
        $characters = '23456789abcdefghijkmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ';
        $password .= $characters[(($_REQUEST["batchid"] - 1) / 56)] . $characters[(($_REQUEST["batchid"] - 1) % 56)];

        $lastId = $conn->max("card", "id") + 100001;
        $conn->insert("card", ["batchid" => $_REQUEST["batchid"], "cardtype" => $_REQUEST["cardtype"], "cardid" => $lastId, "cardpass" => $password, "createtime" => date('Y-m-d H:i:s'), "memo" => $_REQUEST["memo"]]);
        echo '<script>alert("生成完成！点击确定导出为Excel格式");location.href="saveexcel_rechargecard.php?batchid=' . $_REQUEST["batchid"] . '";setTimeout("location.href=\"rechargecardgenerate.php\";",1000);</script>';
    }
    exit;
}

$row_batchid = $conn->get('card', 'batchid', ['ORDER' => ['id' => 'DESC']]);
$batchid = empty($row_batchid) ? 1 : ($row_batchid + 1);
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>充值卡生成</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-money"></i> 充值管理</li>
                <li class="active">充值卡生成</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" name="recharge" onsubmit="return checkform();">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">充值卡批次：</label>

                                <label class="col-lg-4">
                                    第 <?php echo $batchid; ?> 批
                                    <input type="hidden" name="batchid" value="<?php echo $batchid; ?>">
                                </label>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">充值卡类型：</label>

                                <div class="col-lg-2">
                                    <select id="cardtype" name="cardtype">
                                        <option value="">请选择卡类型</option>
                                        <?php
                                        $result = $conn->select("cardtype", "*", ["ishidden[!]" => 1, "ORDER" => "id"]);
                                        foreach ($result as $row) {
                                            echo '<option value="' . $row["id"] . '">' . $row["cardname"] . '</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">生成数量：</label>

                                <div class="col-lg-2">
                                    <input type="text" style="text-align:left;" id="amount" name="amount" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">备注：</label>

                                <div class="col-lg-2">
                                    <textarea style="text-align:left;" id="memo" name="memo" class="bg-focus form-control"></textarea>
                                </div>
                            </div>

                            <div class="form-group" align="center" style="margin-right:150px;margin-top:35px">
                                <div class="col-lg-4 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认生成</submit>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function checkform() {
            if ((window.recharge.cardtype.value == "") || (window.recharge.amount.value == "")) {
                alert("请设置好卡类型和卡数量！");
                return false;
            } else {
                return true;
            }
        }
    </script>
</body>

</html>