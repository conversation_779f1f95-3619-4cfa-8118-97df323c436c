::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: lightgrey;
}

.marquee {
    position: absolute;
    top: 115px;
    left: 0;
    width: 100%;
    height: 20px;
    overflow: hidden;
    z-index: 9999;
    color: #000080;
    font-size: 16px;
    line-height: 20px;
    display: none;
}

.marquee span {
    position: absolute;
    width: 100%;
    top: 0;
    left: 100%;
    white-space: nowrap;
    animation-name: marquee;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-fill-mode: forwards;
}

@keyframes marquee {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-100%);
    }
}

#ChatPage li {
    list-style-type: decimal;
    margin-left: 20px;
}

.product {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 85%;
    padding: 10px;
    border-bottom: 1px solid #ccc;
    cursor: pointer;
}

.product:hover {
    background-color: #f5f5f5;
}

.product.selected {
    background-color: #e1f9fe;
}

.product-name {
    font-weight: bold;
    font-size: 1em;
    width: 30%;
}

.product-price {
    font-size: 0.8em;
    color: green;
    margin-right: 10px;
}

.product-limit {
    font-size: 0.8em;
    color: #666;
    margin-right: 10px;
}

.product-days {
    font-size: 0.8em;
    color: #666;
}

.divpanel {
    position: relative;
}

.divpanelbackground {
    bottom: 37px;
    width: 100vw;
    height: 60px;
    background-color: white;
    position: absolute;
}

.divnewchat {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 40px;
    width: 70px;
    text-align: center;
    padding: 8px 10px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    color: #FFF;
    background-color: #4F80E1;
    outline: none;
    cursor: pointer;
    position: absolute;
    bottom: 47px;
    margin-left: 20px;
}

.divmodel {
    position: absolute;
    margin-left: 82px;
}

.divmodel select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 40px;
    width: 80px;
    padding: 0 15px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    color: #FFF;
    background-color: #4F80E1;
    outline: none;
    cursor: pointer;
    position: absolute;
    bottom: 47px;
    margin-left: 20px;
    background: linear-gradient(to right, #4F80E1 55px, transparent 40px 100%);
    z-index: 100;
}

.divmodel::after {
    content: '\25B2';
    padding: 0 5px;
    border-radius: 0 20px 20px 0;
    background-color: #3D73C9;
    color: #FFF;
    position: absolute;
    left: 75px;
    bottom: 47px;
    line-height: 40px;
}

.divrole {
    position: absolute;
    margin-left: 174px;
}

.divrole select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 40px;
    width: 80px;
    padding: 0 15px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    color: #FFF;
    background-color: #4F80E1;
    outline: none;
    cursor: pointer;
    position: absolute;
    bottom: 47px;
    margin-left: 20px;
    background: linear-gradient(to right, #4F80E1 55px, transparent 40px 100%);
    z-index: 100;
}

.divrole::after {
    content: '\25B2';
    padding: 0 5px;
    border-radius: 0 20px 20px 0;
    background-color: #3D73C9;
    color: #FFF;
    position: absolute;
    left: 75px;
    bottom: 47px;
    line-height: 40px;
}

.divscene {
    position: absolute;
    margin-left: 266px;
}

.divscene select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 40px;
    width: 80px;
    padding: 0 15px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    color: #FFF;
    background-color: #4F80E1;
    outline: none;
    cursor: pointer;
    position: absolute;
    bottom: 47px;
    margin-left: 20px;
    background: linear-gradient(to right, #4F80E1 55px, transparent 40px 100%);
    z-index: 100;
}

.divscene::after {
    content: '\25B2';
    padding: 0 5px;
    border-radius: 0 20px 20px 0;
    background-color: #3D73C9;
    color: #FFF;
    position: absolute;
    left: 75px;
    bottom: 47px;
    line-height: 40px;
}

.answer pre code {
    display: block;
    word-break: break-all;
    padding: 1em;
    background: #f8f9fa;
    color: #000;
    font-family: monospace, monospace;
    font-size: 1em;
    line-height: 1.5em;
}

.codebutton {
    float: right;
    width: 50px;
    text-align: center;
    line-height: 22px;
    font-size: 14px;
    border-radius: var(--zhuluan-primary-border-radius);
    cursor: pointer;
    transition: all .4s;
    color: var(--zhuluan-black-3-color);
    background: linear-gradient(90deg, var(--zhuluan-custom-e8-color), var(--zhuluan-custom-d8-color));
}

.card-header {
    font-size: 16px;
    top: 130px;
    padding: 5px 0 5px 0;
    width: 30px;
    height: 110px;
    z-index: 9999;
    cursor: pointer;
    border-radius: 0 calc(0.375rem - (1px)) calc(0.375rem - (1px)) 0;
    position: fixed;
    left: 0;
    border: var(--bs-card-border-width) solid var(--bs-card-border-color) !important;
    border-left: 0 !important;
    text-align: center;
    background-color: #f5f5f5;
}

.goright {
    margin-left: 0 !important;
}

.cardlist {
    top: 130px;
    height: calc(100% - 280px);
    width: 80%;
    z-index: 9999;
    border-radius: 0 calc(0.375rem - (1px)) calc(0.375rem - (1px)) 0;
    position: fixed;
    left: 0;
    max-width: 450px;
    min-width: 220px;
    border: var(--bs-card-border-width) solid var(--bs-card-border-color) !important;
    border-left: 0 !important;
    background-color: white;
}

.hide-history {
    cursor: pointer;
    text-align: center;
    font-size: 20px;
    border-bottom: 1px;
    z-index: 99999;
    background-color: #f5f5f5;
    border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color) !important;
}

pre {
    white-space: pre-wrap !important;
    word-break: break-all !important;
}

code {
    white-space: pre-wrap !important;
    word-break: break-all !important;
}

@media (min-width:1200px) {
    .card-header {
        font-size: 20px;
        top: 100px;
        padding: 10x 0 10px 0;
        width: 40px;
        height: 145px;
    }

    .goright {
        margin-left: calc(65vw - 550px) !important;
    }

    .cardlist {
        top: 100px;
        left: 0;
        max-width: 450px;
        height: calc(100% - 250px);
        width: calc(60vw - 500px);
    }
}

.card-collapse {
    width: 200px !important;
    height: 50px !important;
    border: 0;
}

.list-group-item:hover {
    background-color: #f5f5f5;
}

.list-group-item {
    height: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 13px 20px 0 20px;
}

#qrcode-container {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 99999999;
}

#qrcode-container2 {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 99999999;
}

#mask {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 88888888;
}

.animation-toast-in {
    animation: slide-top .4s cubic-bezier(.25, .46, .45, .94) forwards
}

.animation-toast-out {
    animation: fade-out .2s ease-out both
}

@keyframes slide-top {
    0% {
        transform: translateY(60px)
    }

    to {
        transform: translateY(0)
    }
}

@keyframes fade-out {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

*,
:after,
:before {
    border: 0 solid #e5e7eb;
    box-sizing: border-box
}

:after,
:before {
    --tw-content: ""
}

body {
    line-height: inherit;
    margin: 0
}

*,
:after,
:before {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

@media (min-width:640px) {
    .container {
        max-width: 640px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 768px
    }
}

@media (min-width:1024px) {
    .container {
        max-width: 1024px
    }
}

@media (min-width:1280px) {
    .container {
        max-width: 1280px
    }
}

@media (min-width:1536px) {
    .container {
        max-width: 1536px
    }
}

.pointer-events-none {
    pointer-events: none
}

.pointer-events-auto {
    pointer-events: auto
}

.inset-0 {
    bottom: 0;
    left: 0;
    right: 0;
    top: 0
}

.z-50 {
    z-index: 50
}

.mb-5 {
    margin-bottom: 1.25rem
}

.h-full {
    height: 100%
}

.cursor-pointer {
    cursor: pointer
}

.flex-col-reverse {
    flex-direction: column-reverse
}

.items-start {
    align-items: flex-start
}

.self-end {
    align-self: flex-end
}

.overflow-hidden {
    overflow: hidden
}

.rounded-xl {
    border-radius: .75rem
}

.border-2 {
    border-width: 2px
}

.border-blue-600 {
    --tw-border-opacity: 1;
    border-color: rgb(37 99 235/var(--tw-border-opacity))
}

.border-orange-600 {
    --tw-border-opacity: 1;
    border-color: rgb(234 88 12/var(--tw-border-opacity))
}

.border-green-600 {
    --tw-border-opacity: 1;
    border-color: rgb(22 163 74/var(--tw-border-opacity))
}

.border-red-600 {
    --tw-border-opacity: 1;
    border-color: rgb(220 38 38/var(--tw-border-opacity))
}

.border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235/var(--tw-border-opacity))
}

.bg-blue-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(59 130 246/var(--tw-bg-opacity))
}

.bg-orange-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(234 88 12/var(--tw-bg-opacity))
}

.bg-green-600 {
    --tw-bg-opacity: 1;
    background-color: rgb(22 163 74/var(--tw-bg-opacity))
}

.bg-red-500 {
    --tw-bg-opacity: 1;
    background-color: rgb(239 68 68/var(--tw-bg-opacity))
}

.bg-gray-50 {
    --tw-bg-opacity: 1;
    background-color: rgb(249 250 251/var(--tw-bg-opacity))
}

.p-10 {
    padding: 2.5rem
}

.py-4 {
    padding-bottom: 1rem;
    padding-top: 1rem
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.shadow-xl {
    --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
    --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.transition {
    transition-duration: .15s;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1)
}

.ease-out {
    transition-timing-function: cubic-bezier(0, 0, .2, 1)
}

*,
:before,
:after {
    box-sizing: border-box;
    border-width: 0;
    border-style: solid;
    border-color: #e5e7eb
}

:before,
:after {
    --tw-content: ""
}

html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol, "Noto Color Emoji";
    font-feature-settings: normal
}

body {
    margin: 0;
    line-height: inherit
}

hr {
    height: 0;
    color: inherit;
    border-top-width: 1px
}

abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-size: inherit;
    font-weight: inherit
}

a {
    color: inherit;
    text-decoration: inherit
}

b,
strong {
    font-weight: bolder
}

code,
kbd,
samp,
pre {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
    font-size: 1em
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-size: 100%;
    font-weight: inherit;
    line-height: inherit;
    color: inherit;
    margin: 0;
    padding: 0
}

button,
select {
    text-transform: none
}

button,
[type=button],
[type=reset],
[type=submit] {
    -webkit-appearance: button;
    background-color: transparent;
    background-image: none
}

:-moz-focusring {
    outline: auto
}

:-moz-ui-invalid {
    box-shadow: none
}

progress {
    vertical-align: baseline
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

summary {
    display: list-item
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
    margin: 0
}

fieldset {
    margin: 0;
    padding: 0
}

legend {
    padding: 0
}

ol,
ul,
menu {
    margin: 0;
    padding: 0
}

textarea {
    resize: vertical
}

input::-moz-placeholder,
textarea::-moz-placeholder {
    opacity: 1;
    color: #9ca3af
}

input::placeholder,
textarea::placeholder {
    opacity: 1;
    color: #9ca3af
}

button,
[role=button] {
    cursor: pointer
}

:disabled {
    cursor: default
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
    display: block;
    vertical-align: middle
}

img,
video {
    max-width: 100%;
    height: auto
}

[hidden] {
    display: none
}

*,
:before,
:after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / .5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia:
}

.container {
    width: 100%
}

@media (min-width: 640px) {
    .container {
        max-width: 640px
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 768px
    }
}

@media (min-width: 1024px) {
    .container {
        max-width: 1024px
    }
}

@media (min-width: 1280px) {
    .container {
        max-width: 1280px
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1536px
    }
}

.fixed {
    position: fixed
}

.absolute {
    position: absolute
}

.relative {
    position: relative
}

.inset-x-0 {
    left: 0px;
    right: 0px
}

.bottom-0 {
    bottom: 0px
}

.left-0 {
    left: 0px
}

.right-0 {
    right: 0px
}

.top-0 {
    top: 0px
}

.z-20 {
    z-index: 20
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem
}

.my-2 {
    margin-top: .5rem;
    margin-bottom: .5rem
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem
}

.ml-3 {
    margin-left: .75rem
}

.mr-2 {
    margin-right: .5rem
}

.mr-3 {
    margin-right: .75rem
}

.mr-4 {
    margin-right: 1rem
}

.mt-3 {
    margin-top: .75rem
}

.block {
    display: block
}

.flex {
    display: flex
}

.hidden {
    display: none
}

.h-10 {
    height: 2.5rem
}

.h-24 {
    height: 6rem
}

.h-6 {
    height: 1.5rem
}

.w-10 {
    width: 2.5rem
}

.w-12 {
    width: 3rem
}

.w-40 {
    width: 10rem
}

.w-6 {
    width: 1.5rem
}

.w-auto {
    width: auto
}

.w-full {
    width: 100%
}

.shrink-0 {
    flex-shrink: 0
}

.flex-grow,
.grow {
    flex-grow: 1
}

.grow-0 {
    flex-grow: 0
}

.-translate-x-full {
    --tw-translate-x: -100%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform {
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.flex-row {
    flex-direction: row
}

.flex-col {
    flex-direction: column
}

.items-center {
    align-items: center
}

.justify-center {
    justify-content: center
}

.justify-between {
    justify-content: space-between
}

.whitespace-nowrap {
    white-space: nowrap
}

.whitespace-pre-wrap {
    white-space: pre-wrap
}

.rounded {
    border-radius: .25rem
}

.rounded-lg {
    border-radius: .5rem
}

.rounded-md {
    border-radius: .375rem
}

.border {
    border-width: 1px
}

.border-b {
    border-bottom-width: 1px
}

.border-t {
    border-top-width: 1px
}

.border-yellow-400 {
    --tw-border-opacity: 1;
    border-color: rgb(250 204 21 / var(--tw-border-opacity))
}

.bg-blue-700 {
    --tw-bg-opacity: 1;
    background-color: rgb(79 128 225 / var(--tw-bg-opacity))
}

.bg-gray-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity))
}

.bg-neutral-100 {
    --tw-bg-opacity: 1;
    background-color: rgb(245 245 245 / var(--tw-bg-opacity))
}

.bg-white {
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-yellow-400 {
    --tw-bg-opacity: 1;
    background-color: rgb(250 204 21 / var(--tw-bg-opacity))
}

.px-1 {
    padding-left: .25rem;
    padding-right: .25rem
}

.px-12 {
    padding-left: 3rem;
    padding-right: 3rem
}

.px-3 {
    padding-left: .75rem;
    padding-right: .75rem
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem
}

.py-0 {
    padding-top: 0;
    padding-bottom: 0
}

.py-1 {
    padding-top: .25rem;
    padding-bottom: .25rem
}

.py-2 {
    padding-top: .5rem;
    padding-bottom: .5rem
}

.py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem
}

.py-3 {
    padding-top: .75rem;
    padding-bottom: .75rem
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem
}

.pr-4 {
    padding-right: 1rem
}

.font-mono {
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace
}


.text-sm {
    font-size: .875rem;
    line-height: 1.25rem
}

.text-xs {
    font-size: .75rem;
    line-height: 1rem
}

.font-semibold {
    font-weight: 600
}

.tracking-wide {
    letter-spacing: .025em
}

.text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity))
}

.text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity))
}

.text-gray-700 {
    --tw-text-opacity: 1;
    color: rgb(55 65 81 / var(--tw-text-opacity))
}

.text-green-700 {
    --tw-text-opacity: 1;
    color: rgb(21 128 61 / var(--tw-text-opacity))
}

.text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity))
}

.shadow {
    --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);
    --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.filter {
    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    transition-duration: .15s
}

.duration-300 {
    transition-duration: .3s
}

[x-cloak] {
    display: none !important
}

path {
    fill: rgb(17 24 39 / var(--tw-bg-opacity))
}

.dark path {
    fill: #fff
}

.blink {
    display: inline-block;
    vertical-align: text-bottom;
    height: 1.25rem;
    width: .5rem;
    background: black;
    animation: blink 1s step-end infinite
}

.dark .blink {
    animation: darkblink 1s step-end infinite
}

@keyframes blink {

    0%,
    to {
        background: transparent
    }

    50% {
        background: rgb(34, 34, 34)
    }
}

@keyframes darkblink {

    0%,
    to {
        background: transparent
    }

    50% {
        background: rgb(211, 211, 211)
    }
}

.hover\:bg-blue-600:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity))
}

.hover\:bg-gray-100:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(243 244 246 / var(--tw-bg-opacity))
}

.hover\:text-blue-500:hover {
    --tw-text-opacity: 1;
    color: rgb(59 130 246 / var(--tw-text-opacity))
}

.hover\:text-gray-600:hover {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity))
}

.hover\:text-green-600:hover {
    --tw-text-opacity: 1;
    color: rgb(22 163 74 / var(--tw-text-opacity))
}

.focus\:border-blue-400:focus {
    --tw-border-opacity: 1;
    border-color: rgb(96 165 250 / var(--tw-border-opacity))
}

.focus\:border-blue-500:focus {
    --tw-border-opacity: 1;
    border-color: rgb(59 130 246 / var(--tw-border-opacity))
}

.focus\:bg-blue-600:focus {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity))
}

.focus\:text-gray-600:focus {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity))
}

.focus\:outline-none:focus {
    outline: 2px solid transparent;
    outline-offset: 2px
}

.focus\:ring:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)
}

.focus\:ring-blue-300:focus {
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity))
}

.focus\:ring-opacity-40:focus {
    --tw-ring-opacity: .4
}

.active\:text-gray-500:active {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity))
}

.dark .dark\:border-gray-600 {
    --tw-border-opacity: 1;
    border-color: rgb(75 85 99 / var(--tw-border-opacity))
}

.dark .dark\:border-gray-900 {
    --tw-border-opacity: 1;
    border-color: rgb(17 24 39 / var(--tw-border-opacity))
}

.dark .dark\:border-b-gray-900 {
    --tw-border-opacity: 1;
    border-bottom-color: rgb(17 24 39 / var(--tw-border-opacity))
}

.dark .dark\:border-t-gray-900 {
    --tw-border-opacity: 1;
    border-top-color: rgb(17 24 39 / var(--tw-border-opacity))
}

.dark .dark\:bg-gray-800 {
    --tw-bg-opacity: 1;
    background-color: rgb(31 41 55 / var(--tw-bg-opacity))
}

.dark .dark\:bg-gray-900 {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39 / var(--tw-bg-opacity))
}

.dark .dark\:text-gray-300 {
    --tw-text-opacity: 1;
    color: rgb(209 213 219 / var(--tw-text-opacity))
}

.dark .dark\:text-gray-400 {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity))
}

.dark .dark\:text-gray-500 {
    --tw-text-opacity: 1;
    color: rgb(107 114 128 / var(--tw-text-opacity))
}

.dark .dark\:hover\:bg-gray-900:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(17 24 39 / var(--tw-bg-opacity))
}

.dark .dark\:hover\:text-blue-400:hover {
    --tw-text-opacity: 1;
    color: rgb(96 165 250 / var(--tw-text-opacity))
}

.dark .dark\:hover\:text-gray-400:hover {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity))
}

.dark .dark\:focus\:border-blue-300:focus {
    --tw-border-opacity: 1;
    border-color: rgb(147 197 253 / var(--tw-border-opacity))
}

.dark .dark\:focus\:text-gray-400:focus {
    --tw-text-opacity: 1;
    color: rgb(156 163 175 / var(--tw-text-opacity))
}

.dark .active\:dark\:text-gray-600:active {
    --tw-text-opacity: 1;
    color: rgb(75 85 99 / var(--tw-text-opacity))
}

@media (min-width: 640px) {
    .sm\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .sm\:mr-4 {
        margin-right: 1rem
    }

    .sm\:h-12 {
        height: 3rem
    }

    .sm\:w-12 {
        width: 3rem
    }

    .sm\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem
    }

    .sm\:pl-2 {
        padding-left: .5rem
    }

    .sm\:pl-4 {
        padding-left: 1rem
    }

    .sm\:pr-6 {
        padding-right: 1.5rem
    }
}

@media (min-width: 768px) {
    .md\:mx-4 {
        margin-left: 1rem;
        margin-right: 1rem
    }

    .md\:mx-6 {
        margin-left: 1.5rem;
        margin-right: 1.5rem
    }

    .md\:my-0 {
        margin-top: 0;
        margin-bottom: 0
    }

    .md\:flex-row {
        flex-direction: row
    }
}

@media (min-width: 1024px) {
    .lg\:relative {
        position: relative
    }

    .lg\:top-0 {
        top: 0px
    }

    .lg\:mt-0 {
        margin-top: 0
    }

    .lg\:flex {
        display: flex
    }

    .lg\:hidden {
        display: none
    }

    .lg\:w-auto {
        width: auto
    }

    .lg\:translate-x-0 {
        --tw-translate-x: 0px;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
    }

    .lg\:items-center {
        align-items: center
    }

    .lg\:justify-between {
        justify-content: space-between
    }

    .lg\:border-b-0 {
        border-bottom-width: 0px
    }

    .lg\:bg-transparent {
        background-color: transparent
    }

    .lg\:p-0 {
        padding: 0
    }

    .lg\:opacity-100 {
        opacity: 1
    }
}