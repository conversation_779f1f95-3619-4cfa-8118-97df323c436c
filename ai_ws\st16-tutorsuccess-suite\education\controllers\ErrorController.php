<?php
/**
 * 错误控制器
 * 处理系统错误和异常
 */
class ErrorController extends Controller {
    /**
     * 显示错误页面
     * @param int $code 错误代码
     * @param string $message 错误消息
     * @return void
     */
    public function index($code = 404, $message = 'Page not found') {
        // 设置HTTP状态码
        http_response_code($code);
        
        // 准备错误数据
        $data = [
            'code' => $code,
            'message' => $message,
        ];
        
        // 如果是AJAX请求，返回JSON响应
        if ($this->isAjax()) {
            $this->json($data, $code, $message);
        }
        
        // 渲染错误视图
        $this->render('index', $data, 'main');
    }
    
    /**
     * 显示403错误页面（禁止访问）
     * @return void
     */
    public function forbidden() {
        $this->index(403, '您没有权限访问此页面');
    }
    
    /**
     * 显示404错误页面（页面未找到）
     * @return void
     */
    public function notFound() {
        $this->index(404, '页面未找到');
    }
    
    /**
     * 显示500错误页面（服务器内部错误）
     * @param string $message 错误消息
     * @return void
     */
    public function serverError($message = '服务器内部错误') {
        $this->index(500, $message);
    }
}