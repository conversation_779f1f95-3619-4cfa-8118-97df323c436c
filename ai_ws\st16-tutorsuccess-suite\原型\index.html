<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特靠谱教培系统 - 登录</title>
    <!-- Bootstrap CSS -->
    <link href="../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../web/css/font-awesome.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            margin: 20px;
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .login-header h2 {
            margin: 0;
            font-weight: 300;
        }
        .login-body {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            border: none;
            border-bottom: 2px solid #eee;
            border-radius: 0;
            padding: 10px 0;
            background: transparent;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-bottom-color: #667eea;
            box-shadow: none;
            background: transparent;
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            width: 100%;
            font-weight: 500;
            transition: transform 0.3s;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            color: white;
        }
        .btn-wechat {
            background: #1aad19;
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            width: 100%;
            font-weight: 500;
            margin-top: 15px;
            transition: transform 0.3s;
        }
        .btn-wechat:hover {
            transform: translateY(-2px);
            color: white;
            background: #179b16;
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #eee;
        }
        .divider span {
            background: white;
            padding: 0 15px;
            color: #999;
        }
        .role-selector {
            margin-top: 20px;
        }
        .role-btn {
            border: 2px solid #eee;
            background: white;
            color: #666;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 5px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .role-btn:hover {
            border-color: #667eea;
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fa fa-graduation-cap"></i> 特靠谱教培系统</h2>
            <p class="mb-0">课外补习班管理平台</p>
        </div>
        <div class="login-body">
            <form id="loginForm">
                <div class="form-group">
                    <input type="email" class="form-control" id="email" placeholder="邮箱地址" required>
                </div>
                <div class="form-group">
                    <input type="password" class="form-control" id="password" placeholder="密码" required>
                </div>
                <div class="form-check mb-3">
                    <input type="checkbox" class="form-check-input" id="remember">
                    <label class="form-check-label" for="remember">记住我</label>
                </div>
                <button type="submit" class="btn btn-login">
                    <i class="fa fa-sign-in"></i> 登录
                </button>
            </form>
            
            <div class="divider">
                <span>或</span>
            </div>
            
            <button class="btn btn-wechat" onclick="wechatLogin()">
                <i class="fa fa-wechat"></i> 微信登录
            </button>
            
            <div class="role-selector">
                <p class="text-center text-muted mb-2">快速体验（演示模式）</p>
                <div class="text-center">
                    <a href="student/dashboard.html" class="role-btn">
                        <i class="fa fa-user"></i> 学生端
                    </a>
                    <a href="teacher/dashboard.html" class="role-btn">
                        <i class="fa fa-chalkboard-teacher"></i> 教师端
                    </a>
                    <a href="admin/dashboard.html" class="role-btn">
                        <i class="fa fa-cog"></i> 管理员
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 表单提交处理
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            const email = $('#email').val();
            const password = $('#password').val();
            
            // 这里应该调用实际的登录API
            // 演示模式：根据邮箱判断角色
            if (email.includes('student')) {
                window.location.href = 'student/dashboard.html';
            } else if (email.includes('teacher')) {
                window.location.href = 'teacher/dashboard.html';
            } else if (email.includes('admin')) {
                window.location.href = 'admin/dashboard.html';
            } else {
                alert('演示模式：请使用包含student、teacher或admin的邮箱地址');
            }
        });
        
        // 微信登录处理（参考现有的ks1cck2登录流程）
        function wechatLogin() {
            // 生成随机字符串
            const userrndstr = Math.random().toString(36).substring(2, 15);
            const referral_code = '';
            
            // 创建隐藏iframe进行微信登录
            const iframe = $('<iframe style="display:none;" id="wechat_login_frame"></iframe>');
            $('body').append(iframe);
            
            // 设置iframe源地址，参考ks1cck2_init.php的实现
            iframe.attr('src', '../web/ks1cck2/login.php?userrndstr=' + userrndstr + '&referral_code=' + referral_code);
            
            // 显示登录提示
            alert('正在跳转到微信登录...');
        }
    </script>
</body>
</html>
