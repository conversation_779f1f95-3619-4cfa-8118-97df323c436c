<?php
/**
 * 学生数据模型
 * 创建时间：2025-01-22
 */

require_once 'BaseModel.php';

class Student extends BaseModel {
    
    public function __construct($conn) {
        parent::__construct($conn);
        $this->table = TABLE_STUDENT;
    }
    
    /**
     * 根据用户ID查找学生信息
     * @param int $user_id
     * @return array|null
     */
    public function findByUserId($user_id) {
        $sql = "SELECT * FROM {$this->table} WHERE user_id = ? AND status = ? LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('ii', $user_id, STATUS_ACTIVE);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }
    
    /**
     * 获取学生列表（包含用户信息）
     * @param array $conditions
     * @param string $order_by
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getStudentsWithUser($conditions = [], $order_by = 's.create_time DESC', $limit = 0, $offset = 0) {
        $sql = "SELECT s.*, u.username, u.email 
                FROM {$this->table} s 
                LEFT JOIN user u ON s.user_id = u.userid 
                WHERE s.status = ?";
        
        $params = [STATUS_ACTIVE];
        $types = 'i';
        
        if (!empty($conditions)) {
            foreach ($conditions as $field => $value) {
                $sql .= " AND s.{$field} = ?";
                $params[] = $value;
                $types .= is_int($value) ? 'i' : 's';
            }
        }
        
        if ($order_by) {
            $sql .= " ORDER BY {$order_by}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $students = [];
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
        
        return $students;
    }
    
    /**
     * 获取学生的课程列表
     * @param int $student_id
     * @return array
     */
    public function getCourses($student_id) {
        $sql = "SELECT c.*, t.name as teacher_name, sc.join_date
                FROM " . TABLE_STUDENT_COURSE . " sc
                JOIN " . TABLE_COURSE . " c ON sc.course_id = c.id
                JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                WHERE sc.student_id = ? AND sc.status = ? AND c.status = ?
                ORDER BY sc.join_date DESC";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('iii', $student_id, $active, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        
        return $courses;
    }
    
    /**
     * 获取学生的课程安排
     * @param int $student_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getSchedule($student_id, $start_date = null, $end_date = null) {
        $sql = "SELECT cs.*, c.name as course_name, cr.name as classroom_name, t.name as teacher_name
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                JOIN " . TABLE_CLASSROOM . " cr ON cs.classroom_id = cr.id
                JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                JOIN " . TABLE_STUDENT_COURSE . " sc ON c.id = sc.course_id
                WHERE sc.student_id = ? AND cs.status = ? AND c.status = ? AND sc.status = ?";
        
        $params = [$student_id, STATUS_ACTIVE, STATUS_ACTIVE, STATUS_ACTIVE];
        $types = 'iiii';
        
        if ($start_date) {
            $sql .= " AND cs.start_date >= ?";
            $params[] = $start_date;
            $types .= 's';
        }
        
        if ($end_date) {
            $sql .= " AND (cs.end_date IS NULL OR cs.end_date <= ?)";
            $params[] = $end_date;
            $types .= 's';
        }
        
        $sql .= " ORDER BY cs.day_of_week, cs.start_time";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $schedules = [];
        while ($row = $result->fetch_assoc()) {
            $schedules[] = $row;
        }
        
        return $schedules;
    }
    
    /**
     * 获取学生的请假记录
     * @param int $student_id
     * @param int $status 请假状态
     * @return array
     */
    public function getLeaveRequests($student_id, $status = null) {
        $sql = "SELECT lr.*, c.name as course_name, cs.day_of_week, cs.start_time, cs.end_time, t.name as teacher_name
                FROM " . TABLE_LEAVE_REQUEST . " lr
                JOIN " . TABLE_COURSE_SCHEDULE . " cs ON lr.course_schedule_id = cs.id
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                WHERE lr.student_id = ?";
        
        $params = [$student_id];
        $types = 'i';
        
        if ($status !== null) {
            $sql .= " AND lr.status = ?";
            $params[] = $status;
            $types .= 'i';
        }
        
        $sql .= " ORDER BY lr.create_time DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $requests = [];
        while ($row = $result->fetch_assoc()) {
            $requests[] = $row;
        }
        
        return $requests;
    }
    
    /**
     * 提交请假申请
     * @param int $student_id
     * @param int $course_schedule_id
     * @param string $leave_date
     * @param int $leave_type
     * @param string $reason
     * @param string $attachment
     * @return int|false
     */
    public function submitLeaveRequest($student_id, $course_schedule_id, $leave_date, $leave_type, $reason, $attachment = '') {
        // 检查是否已经提交过同一天的请假申请
        $existing = $this->query(
            "SELECT id FROM " . TABLE_LEAVE_REQUEST . " 
             WHERE student_id = ? AND course_schedule_id = ? AND leave_date = ?",
            [$student_id, $course_schedule_id, $leave_date]
        );
        
        if (!empty($existing)) {
            return false; // 已存在请假申请
        }
        
        $data = [
            'student_id' => $student_id,
            'course_schedule_id' => $course_schedule_id,
            'leave_date' => $leave_date,
            'leave_type' => $leave_type,
            'reason' => $reason,
            'attachment' => $attachment,
            'status' => LEAVE_STATUS_PENDING,
            'create_time' => date('Y-m-d H:i:s')
        ];
        
        $sql = "INSERT INTO " . TABLE_LEAVE_REQUEST . " 
                (student_id, course_schedule_id, leave_date, leave_type, reason, attachment, status, create_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('iiiissii', 
            $data['student_id'], 
            $data['course_schedule_id'], 
            $data['leave_date'], 
            $data['leave_type'], 
            $data['reason'], 
            $data['attachment'], 
            $data['status'], 
            $data['create_time']
        );
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * 获取学生的考勤记录
     * @param int $student_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getAttendance($student_id, $start_date = null, $end_date = null) {
        $sql = "SELECT a.*, c.name as course_name, cs.day_of_week, cs.start_time, cs.end_time
                FROM " . TABLE_ATTENDANCE . " a
                JOIN " . TABLE_COURSE_SCHEDULE . " cs ON a.course_schedule_id = cs.id
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                WHERE a.student_id = ?";
        
        $params = [$student_id];
        $types = 'i';
        
        if ($start_date) {
            $sql .= " AND a.attendance_date >= ?";
            $params[] = $start_date;
            $types .= 's';
        }
        
        if ($end_date) {
            $sql .= " AND a.attendance_date <= ?";
            $params[] = $end_date;
            $types .= 's';
        }
        
        $sql .= " ORDER BY a.attendance_date DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $attendance = [];
        while ($row = $result->fetch_assoc()) {
            $attendance[] = $row;
        }
        
        return $attendance;
    }
    
    /**
     * 获取学生统计信息
     * @param int $student_id
     * @return array
     */
    public function getStatistics($student_id) {
        // 选课数量
        $course_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_STUDENT_COURSE . " WHERE student_id = ? AND status = ?",
            [$student_id, STATUS_ACTIVE]
        )[0]['count'];
        
        // 本周课程数量
        $week_schedule_count = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_COURSE_SCHEDULE . " cs
             JOIN " . TABLE_STUDENT_COURSE . " sc ON cs.course_id = sc.course_id
             WHERE sc.student_id = ? AND cs.status = ? AND sc.status = ?
             AND cs.start_date <= CURDATE() 
             AND (cs.end_date IS NULL OR cs.end_date >= CURDATE())",
            [$student_id, STATUS_ACTIVE, STATUS_ACTIVE]
        )[0]['count'];
        
        // 请假申请数量
        $leave_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_LEAVE_REQUEST . " WHERE student_id = ?",
            [$student_id]
        )[0]['count'];
        
        // 待审批请假数量
        $pending_leave_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_LEAVE_REQUEST . " WHERE student_id = ? AND status = ?",
            [$student_id, LEAVE_STATUS_PENDING]
        )[0]['count'];
        
        return [
            'course_count' => $course_count,
            'week_schedule_count' => $week_schedule_count,
            'leave_count' => $leave_count,
            'pending_leave_count' => $pending_leave_count
        ];
    }
    
    /**
     * 选课
     * @param int $student_id
     * @param int $course_id
     * @return bool
     */
    public function enrollCourse($student_id, $course_id) {
        // 检查是否已经选过该课程
        $existing = $this->query(
            "SELECT id FROM " . TABLE_STUDENT_COURSE . " WHERE student_id = ? AND course_id = ?",
            [$student_id, $course_id]
        );
        
        if (!empty($existing)) {
            return false; // 已选过该课程
        }
        
        $sql = "INSERT INTO " . TABLE_STUDENT_COURSE . " (student_id, course_id, join_date, create_time) 
                VALUES (?, ?, CURDATE(), NOW())";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('ii', $student_id, $course_id);
        
        return $stmt->execute();
    }
    
    /**
     * 退课
     * @param int $student_id
     * @param int $course_id
     * @return bool
     */
    public function dropCourse($student_id, $course_id) {
        $sql = "UPDATE " . TABLE_STUDENT_COURSE . " 
                SET status = ?, update_time = NOW() 
                WHERE student_id = ? AND course_id = ?";
        
        $stmt = $this->conn->prepare($sql);
        $inactive = STATUS_INACTIVE;
        $stmt->bind_param('iii', $inactive, $student_id, $course_id);
        
        return $stmt->execute();
    }
    
    /**
     * 创建或更新学生信息
     * @param int $user_id
     * @param array $data
     * @return int|bool
     */
    public function createOrUpdate($user_id, $data) {
        $existing = $this->findByUserId($user_id);
        
        $data['user_id'] = $user_id;
        $data['update_time'] = date('Y-m-d H:i:s');
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            $data['create_time'] = date('Y-m-d H:i:s');
            return $this->create($data);
        }
    }
}
