<?php
/**
 * 教培系统本地开发测试入口
 * 仅限localhost访问，用于开发测试
 * 修订日期：2025-08-22
 */

// 检查是否为localhost访问
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    http_response_code(403);
    die('Access denied. This page is only accessible from localhost.');
}

session_start();

// 模拟用户登录状态
if (!isset($_SESSION['dev_mode'])) {
    $_SESSION['dev_mode'] = true;
}

$page_title = '特靠谱教培系统 - 本地测试';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 50px 0;
        }
        .welcome-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
        }
        .role-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .role-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        .role-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .role-desc {
            color: #666;
            margin-bottom: 20px;
        }
        .btn-role {
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s;
            font-weight: bold;
        }
        .btn-role:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .btn-admin {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .btn-teacher {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-student {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        .btn-parent {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        }
        .icon-admin {
            color: #007bff;
        }
        .icon-teacher {
            color: #28a745;
        }
        .icon-student {
            color: #17a2b8;
        }
        .icon-parent {
            color: #ffc107;
        }
        .dev-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            color: white;
        }
        .dev-badge {
            background: #ff6b6b;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        .test-links {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .test-link {
            display: inline-block;
            margin: 5px;
            padding: 8px 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-decoration: none;
            color: #495057;
            font-size: 0.9rem;
        }
        .test-link:hover {
            background: #e9ecef;
            text-decoration: none;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container main-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="welcome-card">
                    <h1>
                        <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
                        <span class="dev-badge">DEV MODE</span>
                    </h1>
                    <p class="lead">本地开发测试环境</p>
                    <p>选择不同身份进行功能测试：</p>
                </div>
                
                <div class="row">
                    <!-- 管理员测试 -->
                    <div class="col-md-3">
                        <div class="role-card" onclick="testRole('admin')">
                            <div class="role-icon icon-admin">
                                <i class="fa fa-cog"></i>
                            </div>
                            <div class="role-title">管理员测试</div>
                            <div class="role-desc">系统管理、用户管理、课程管理</div>
                            <button class="btn btn-role btn-admin" onclick="testRole('admin')">
                                <i class="fa fa-play"></i> 开始测试
                            </button>
                        </div>
                    </div>

                    <!-- 教师测试 -->
                    <div class="col-md-3">
                        <div class="role-card" onclick="testRole('teacher')">
                            <div class="role-icon icon-teacher">
                                <i class="fa fa-chalkboard-teacher"></i>
                            </div>
                            <div class="role-title">教师测试</div>
                            <div class="role-desc">课表管理、请假审批、学生管理</div>
                            <button class="btn btn-role btn-teacher" onclick="testRole('teacher')">
                                <i class="fa fa-play"></i> 开始测试
                            </button>
                        </div>
                    </div>

                    <!-- 学生测试 -->
                    <div class="col-md-3">
                        <div class="role-card" onclick="testRole('student')">
                            <div class="role-icon icon-student">
                                <i class="fa fa-user-graduate"></i>
                            </div>
                            <div class="role-title">学生测试</div>
                            <div class="role-desc">课表查看、请假申请、成绩查询</div>
                            <button class="btn btn-role btn-student" onclick="testRole('student')">
                                <i class="fa fa-play"></i> 开始测试
                            </button>
                        </div>
                    </div>

                    <!-- 家长测试 -->
                    <div class="col-md-3">
                        <div class="role-card" onclick="testRole('parent')">
                            <div class="role-icon icon-parent">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="role-title">家长测试</div>
                            <div class="role-desc">查看孩子课表、请假记录</div>
                            <button class="btn btn-role btn-parent" onclick="testRole('parent')">
                                <i class="fa fa-play"></i> 开始测试
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 快速测试链接 -->
                <div class="test-links">
                    <h5><i class="fa fa-link"></i> 快速测试链接 - 新架构</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <h6>管理员功能</h6>
                            <a href="admin_test.php" class="test-link">
                                <i class="fa fa-home"></i> 管理主页
                            </a>
                            <a href="admin_test.php?path=admin/users" class="test-link">
                                <i class="fa fa-users"></i> 用户管理
                            </a>
                            <a href="admin_test.php?path=admin/courses" class="test-link">
                                <i class="fa fa-book"></i> 课程管理
                            </a>
                            <a href="admin_test.php?path=admin/statistics" class="test-link">
                                <i class="fa fa-chart-bar"></i> 数据统计
                            </a>
                        </div>
                        <div class="col-md-3">
                            <h6>教师功能</h6>
                            <a href="index.php?path=teacher" class="test-link">
                                <i class="fa fa-home"></i> 教师主页
                            </a>
                            <a href="index.php?path=teacher/schedule" class="test-link">
                                <i class="fa fa-calendar"></i> 教师课表
                            </a>
                            <a href="index.php?path=teacher/leave" class="test-link">
                                <i class="fa fa-file-text"></i> 请假审批
                            </a>
                        </div>
                        <div class="col-md-3">
                            <h6>学生功能</h6>
                            <a href="index.php?path=student" class="test-link">
                                <i class="fa fa-home"></i> 学生主页
                            </a>
                            <a href="index.php?path=student/schedule" class="test-link">
                                <i class="fa fa-calendar"></i> 学生课表
                            </a>
                            <a href="index.php?path=student/leave" class="test-link">
                                <i class="fa fa-file-text"></i> 请假申请
                            </a>
                        </div>
                        <div class="col-md-3">
                            <h6>家长功能</h6>
                            <a href="parent_test.php" class="test-link">
                                <i class="fa fa-home"></i> 家长主页
                            </a>
                            <a href="parent_test.php?path=parent/schedule" class="test-link">
                                <i class="fa fa-calendar"></i> 孩子课表
                            </a>
                            <a href="parent_test.php?path=parent/leaves" class="test-link">
                                <i class="fa fa-file-text"></i> 请假记录
                            </a>
                        </div>
                    </div>

                    <hr>
                    <h6><i class="fa fa-database"></i> 开发工具</h6>
                    <a href="simple_db_test.php" class="test-link">
                        <i class="fa fa-database"></i> 数据库测试
                    </a>
                    <a href="test.php" class="test-link">
                        <i class="fa fa-code"></i> 路由测试
                    </a>
                    <a href="index.php" class="test-link">
                        <i class="fa fa-rocket"></i> 新架构入口
                    </a>
                </div>
                
                <div class="dev-info text-center">
                    <h5><i class="fa fa-info-circle"></i> 开发测试信息</h5>
                    <p>
                        <strong>访问地址：</strong> http://localhost:8099/ks1cck2/education/<br>
                        <strong>测试模式：</strong> 本地开发环境，自动模拟登录状态<br>
                        <strong>架构版本：</strong> 新MVC架构 - ThinkPHP 5.1风格<br>
                        <strong>功能状态：</strong> 四个角色功能已完成开发和测试
                    </p>
                    <p>
                        <strong>已完成功能：</strong><br>
                        ✅ 管理员端：系统概览、用户管理CRUD、数据统计<br>
                        ✅ 教师端：主页、课表管理、请假审批<br>
                        ✅ 学生端：主页、课表查看、请假申请<br>
                        ✅ 家长端：主页、孩子课表、请假记录<br>
                        ✅ 数据库：完整表结构、DAO层、真实数据支持
                    </p>
                    <p>
                        <a href="../index.php" class="text-white"><i class="fa fa-home"></i> 返回主系统</a> |
                        <a href="index.php" class="text-white"><i class="fa fa-rocket"></i> 新架构入口</a> |
                        <a href="../../../edutest.php" class="text-white"><i class="fa fa-cog"></i> 系统测试页面</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testRole(role) {
            // 设置测试用户身份到session
            fetch('set_test_session.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    role: role,
                    action: 'set_test_user'
                })
            }).then(() => {
                // 根据角色跳转到对应页面
                switch(role) {
                    case 'admin':
                        window.location.href = 'admin_test.php';
                        break;
                    case 'teacher':
                        window.location.href = 'index.php?path=teacher';
                        break;
                    case 'student':
                        window.location.href = 'index.php?path=student';
                        break;
                    case 'parent':
                        window.location.href = 'parent_test.php';
                        break;
                }
            }).catch(() => {
                // 如果AJAX失败，直接跳转
                switch(role) {
                    case 'admin':
                        window.location.href = 'admin_test.php';
                        break;
                    case 'teacher':
                        window.location.href = 'index.php?path=teacher';
                        break;
                    case 'student':
                        window.location.href = 'index.php?path=student';
                        break;
                    case 'parent':
                        window.location.href = 'parent_test.php';
                        break;
                }
            });
        }
        
        // 页面加载完成后的提示
        $(document).ready(function() {
            // 显示当前时间
            const now = new Date();
            const timeStr = now.toLocaleString('zh-CN');
            
            // 添加时间显示
            $('.dev-info').append(`<p><small>测试时间：${timeStr}</small></p>`);
        });
    </script>
</body>
</html>
