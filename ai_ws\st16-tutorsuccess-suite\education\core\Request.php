<?php
namespace Core;

/**
 * 请求处理类
 */
class Request
{
    private $get;
    private $post;
    private $server;
    private $cookie;
    private $session;
    private $files;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->get = $_GET;
        $this->post = $_POST;
        $this->server = $_SERVER;
        $this->cookie = $_COOKIE;
        $this->session = $_SESSION;
        $this->files = $_FILES;
    }
    
    /**
     * 获取请求方法
     * 
     * @return string 请求方法
     */
    public function getMethod()
    {
        return isset($this->server['REQUEST_METHOD']) ? $this->server['REQUEST_METHOD'] : 'GET';
    }
    
    /**
     * 获取请求路径
     * 
     * @return string 请求路径
     */
    public function getPath()
    {
        $path = isset($this->server['REQUEST_URI']) ? $this->server['REQUEST_URI'] : '/';

        // 移除查询字符串
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }

        // 移除BASE_URL前缀
        if (defined('BASE_URL') && BASE_URL !== '/') {
            if (strpos($path, BASE_URL) === 0) {
                $path = substr($path, strlen(BASE_URL));
            }
        }

        // 确保路径以/开头
        if (empty($path) || $path[0] !== '/') {
            $path = '/' . $path;
        }

        return $path;
    }
    
    /**
     * 获取GET参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function get($key = null, $default = null)
    {
        if ($key === null) {
            return $this->get;
        }
        
        return isset($this->get[$key]) ? $this->get[$key] : $default;
    }
    
    /**
     * 获取POST参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function post($key = null, $default = null)
    {
        if ($key === null) {
            return $this->post;
        }
        
        return isset($this->post[$key]) ? $this->post[$key] : $default;
    }
    
    /**
     * 获取SERVER参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function server($key = null, $default = null)
    {
        if ($key === null) {
            return $this->server;
        }
        
        return isset($this->server[$key]) ? $this->server[$key] : $default;
    }
    
    /**
     * 获取COOKIE参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function cookie($key = null, $default = null)
    {
        if ($key === null) {
            return $this->cookie;
        }
        
        return isset($this->cookie[$key]) ? $this->cookie[$key] : $default;
    }
    
    /**
     * 获取SESSION参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function session($key = null, $default = null)
    {
        if ($key === null) {
            return $this->session;
        }
        
        return isset($this->session[$key]) ? $this->session[$key] : $default;
    }
    
    /**
     * 获取FILES参数
     * 
     * @param string $key 参数名
     * @param mixed $default 默认值
     * @return mixed 参数值
     */
    public function files($key = null, $default = null)
    {
        if ($key === null) {
            return $this->files;
        }
        
        return isset($this->files[$key]) ? $this->files[$key] : $default;
    }
    
    /**
     * 判断是否为AJAX请求
     * 
     * @return bool 是否为AJAX请求
     */
    public function isAjax()
    {
        return isset($this->server['HTTP_X_REQUESTED_WITH']) && 
               strtolower($this->server['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * 判断是否为POST请求
     * 
     * @return bool 是否为POST请求
     */
    public function isPost()
    {
        return $this->getMethod() === 'POST';
    }
    
    /**
     * 判断是否为GET请求
     * 
     * @return bool 是否为GET请求
     */
    public function isGet()
    {
        return $this->getMethod() === 'GET';
    }
}