<?php
/**
 * 微信授权登录工具类
 */
class WechatAuth {
    private $appId;        // 微信公众号AppID
    private $appSecret;    // 微信公众号AppSecret
    private $redirectUri;  // 授权回调地址
    
    /**
     * 构造函数
     * 
     * @param string $appId 微信公众号AppID
     * @param string $appSecret 微信公众号AppSecret
     * @param string $redirectUri 授权回调地址
     */
    public function __construct($appId = null, $appSecret = null, $redirectUri = null) {
        global $config;
        
        $this->appId = $appId ?: $config['wechat']['app_id'];
        $this->appSecret = $appSecret ?: $config['wechat']['app_secret'];
        $this->redirectUri = $redirectUri ?: $config['wechat']['redirect_uri'];
    }
    
    /**
     * 获取微信授权链接
     * 
     * @param string $state 状态参数，用于防止CSRF攻击
     * @param string $scope 授权范围，snsapi_base或snsapi_userinfo
     * @return string 授权链接
     */
    public function getAuthUrl($state = '', $scope = 'snsapi_userinfo') {
        $params = [
            'appid' => $this->appId,
            'redirect_uri' => urlencode($this->redirectUri),
            'response_type' => 'code',
            'scope' => $scope,
            'state' => $state ?: md5(uniqid(mt_rand(), true))
        ];
        
        return 'https://open.weixin.qq.com/connect/oauth2/authorize?' . http_build_query($params) . '#wechat_redirect';
    }
    
    /**
     * 通过code获取access_token
     * 
     * @param string $code 授权code
     * @return array|false 成功返回包含access_token和openid的数组，失败返回false
     */
    public function getAccessToken($code) {
        $url = 'https://api.weixin.qq.com/sns/oauth2/access_token';
        $params = [
            'appid' => $this->appId,
            'secret' => $this->appSecret,
            'code' => $code,
            'grant_type' => 'authorization_code'
        ];
        
        $result = $this->httpGet($url, $params);
        if (isset($result['errcode']) && $result['errcode'] != 0) {
            return false;
        }
        
        return $result;
    }
    
    /**
     * 获取微信用户信息
     * 
     * @param string $accessToken 网页授权access_token
     * @param string $openid 用户的openid
     * @return array|false 成功返回用户信息数组，失败返回false
     */
    public function getUserInfo($accessToken, $openid) {
        $url = 'https://api.weixin.qq.com/sns/userinfo';
        $params = [
            'access_token' => $accessToken,
            'openid' => $openid,
            'lang' => 'zh_CN'
        ];
        
        $result = $this->httpGet($url, $params);
        if (isset($result['errcode']) && $result['errcode'] != 0) {
            return false;
        }
        
        return $result;
    }
    
    /**
     * 检验授权凭证是否有效
     * 
     * @param string $accessToken 网页授权access_token
     * @param string $openid 用户的openid
     * @return bool 有效返回true，无效返回false
     */
    public function validateAccessToken($accessToken, $openid) {
        $url = 'https://api.weixin.qq.com/sns/auth';
        $params = [
            'access_token' => $accessToken,
            'openid' => $openid
        ];
        
        $result = $this->httpGet($url, $params);
        return isset($result['errcode']) && $result['errcode'] == 0;
    }
    
    /**
     * 发起HTTP GET请求
     * 
     * @param string $url 请求URL
     * @param array $params 请求参数
     * @return array 返回JSON解码后的数组
     */
    private function httpGet($url, $params = []) {
        $url = $url . '?' . http_build_query($params);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        curl_close($ch);
        
        return json_decode($response, true);
    }
    
    /**
     * 生成微信JSSDK配置
     * 
     * @param array $jsApiList 需要使用的JS接口列表
     * @param string $url 当前网页的URL，不包含#及其后面部分
     * @return array JSSDK配置数组
     */
    public function getJssdkConfig($jsApiList = [], $url = '') {
        // 获取jsapi_ticket
        $jsapiTicket = $this->getJsapiTicket();
        
        // 当前URL
        $url = $url ?: 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $url = explode('#', $url)[0];
        
        // 生成签名
        $timestamp = time();
        $nonceStr = $this->createNonceStr();
        
        $string = "jsapi_ticket=$jsapiTicket&noncestr=$nonceStr&timestamp=$timestamp&url=$url";
        $signature = sha1($string);
        
        return [
            'appId' => $this->appId,
            'timestamp' => $timestamp,
            'nonceStr' => $nonceStr,
            'signature' => $signature,
            'jsApiList' => $jsApiList
        ];
    }
    
    /**
     * 获取jsapi_ticket
     * 
     * @return string jsapi_ticket
     */
    private function getJsapiTicket() {
        // 从缓存获取ticket
        $cacheFile = __DIR__ . '/../runtime/cache/jsapi_ticket.json';
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data['expire_time'] > time()) {
                return $data['ticket'];
            }
        }
        
        // 获取access_token
        $accessToken = $this->getGlobalAccessToken();
        
        // 请求jsapi_ticket
        $url = 'https://api.weixin.qq.com/cgi-bin/ticket/getticket';
        $params = [
            'access_token' => $accessToken,
            'type' => 'jsapi'
        ];
        
        $result = $this->httpGet($url, $params);
        
        if (isset($result['errcode']) && $result['errcode'] == 0) {
            $data = [
                'ticket' => $result['ticket'],
                'expire_time' => time() + 7000 // 有效期为7200秒，提前200秒刷新
            ];
            
            // 确保目录存在
            if (!is_dir(dirname($cacheFile))) {
                mkdir(dirname($cacheFile), 0777, true);
            }
            
            // 保存到缓存
            file_put_contents($cacheFile, json_encode($data));
            
            return $result['ticket'];
        }
        
        return '';
    }
    
    /**
     * 获取全局access_token
     * 
     * @return string 全局access_token
     */
    private function getGlobalAccessToken() {
        // 从缓存获取token
        $cacheFile = __DIR__ . '/../runtime/cache/access_token.json';
        
        if (file_exists($cacheFile)) {
            $data = json_decode(file_get_contents($cacheFile), true);
            if ($data['expire_time'] > time()) {
                return $data['access_token'];
            }
        }
        
        // 请求新的access_token
        $url = 'https://api.weixin.qq.com/cgi-bin/token';
        $params = [
            'grant_type' => 'client_credential',
            'appid' => $this->appId,
            'secret' => $this->appSecret
        ];
        
        $result = $this->httpGet($url, $params);
        
        if (isset($result['access_token'])) {
            $data = [
                'access_token' => $result['access_token'],
                'expire_time' => time() + 7000 // 有效期为7200秒，提前200秒刷新
            ];
            
            // 确保目录存在
            if (!is_dir(dirname($cacheFile))) {
                mkdir(dirname($cacheFile), 0777, true);
            }
            
            // 保存到缓存
            file_put_contents($cacheFile, json_encode($data));
            
            return $result['access_token'];
        }
        
        return '';
    }
    
    /**
     * 生成随机字符串
     * 
     * @param int $length 字符串长度
     * @return string 随机字符串
     */
    private function createNonceStr($length = 16) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $str = '';
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }
        return $str;
    }
}