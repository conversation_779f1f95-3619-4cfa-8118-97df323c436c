<?php
/**
 * 教师控制器
 * 处理教师相关的请求
 */
class TeacherController extends Controller {
    /**
     * 教师模型
     * @var TeacherModel
     */
    private $teacherModel;
    
    /**
     * 用户模型
     * @var UserModel
     */
    private $userModel;

    /**
     * 请假模型
     * @var LeaveModel
     */
    private $leaveModel;

    /**
     * 课程模型
     * @var CourseModel
     */
    private $courseModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        
        // 初始化模型
        $this->teacherModel = new TeacherModel();
        $this->userModel = new UserModel();
        $this->leaveModel = new LeaveModel();
        $this->courseModel = new CourseModel();
        
        // 检查权限（只有管理员和教师可以访问）
        $this->checkPermission();
    }
    
    /**
     * 检查权限
     * 只有管理员和教师可以访问教师相关功能
     */
    private function checkPermission() {
        // 获取当前用户
        $user = isset($_SESSION['user']) ? $_SESSION['user'] : null;
        
        // 检查用户角色
        if (!$user || ($user['role'] !== 'admin' && $user['role'] !== 'teacher')) {
            // 如果不是管理员或教师，则重定向到错误页面
            $this->redirect('error/forbidden');
        }
    }
    
    /**
     * 教师列表页面
     */
    public function index() {
        // 获取当前页码
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page);
        
        // 每页显示数量
        $perPage = 10;
        
        // 获取教师列表
        $teachers = $this->teacherModel->getTeachers($page, $perPage);
        
        // 获取教师总数
        $total = $this->teacherModel->getTeacherCount();
        
        // 计算总页数
        $totalPages = ceil($total / $perPage);
        
        // 渲染视图
        $this->render('index', [
            'teachers' => $teachers,
            'page' => $page,
            'perPage' => $perPage,
            'total' => $total,
            'totalPages' => $totalPages
        ]);
    }
    
    /**
     * 添加教师页面
     */
    public function add() {
        // 检查是否为管理员
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->redirect('error/forbidden');
        }
        
        // 渲染视图
        $this->render('add');
    }
    
    /**
     * 处理添加教师请求
     */
    public function doAdd() {
        // 检查是否为管理员
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取表单数据
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $title = $this->post('title');
        $subject = $this->post('subject');
        $description = $this->post('description');
        
        // 验证数据
        if (empty($name) || empty($email) || empty($password)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 检查邮箱是否已存在
        if ($this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 创建用户
            $userId = $this->userModel->createUser([
                'name' => $name,
                'email' => $email,
                'password' => password_hash($password, PASSWORD_DEFAULT),
                'role' => 'teacher',
                'phone' => $phone,
                'status' => 1
            ]);
            
            // 创建教师
            $teacherId = $this->teacherModel->createTeacher([
                'user_id' => $userId,
                'title' => $title,
                'subject' => $subject,
                'description' => $description
            ]);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(['id' => $teacherId], 200, '教师添加成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '添加教师失败：' . $e->getMessage());
        }
    }
    
    /**
     * 编辑教师页面
     * @param int $id 教师ID
     */
    public function edit($id) {
        // 检查是否为管理员
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->redirect('error/forbidden');
        }
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherById($id);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('edit', ['teacher' => $teacher]);
    }
    
    /**
     * 处理编辑教师请求
     */
    public function doEdit() {
        // 检查是否为管理员
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取表单数据
        $id = $this->post('id');
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $title = $this->post('title');
        $subject = $this->post('subject');
        $description = $this->post('description');
        
        // 验证数据
        if (empty($id) || empty($name) || empty($email)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherById($id);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->json(null, 404, '教师不存在');
        }
        
        // 检查邮箱是否已被其他用户使用
        if ($email !== $teacher['email'] && $this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新用户信息
            $userData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone
            ];
            
            // 如果提供了新密码，则更新密码
            if (!empty($password)) {
                $userData['password'] = password_hash($password, PASSWORD_DEFAULT);
            }
            
            $this->userModel->updateUser($teacher['user_id'], $userData);
            
            // 更新教师信息
            $this->teacherModel->updateTeacher($id, [
                'title' => $title,
                'subject' => $subject,
                'description' => $description
            ]);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(['id' => $id], 200, '教师信息更新成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '更新教师信息失败：' . $e->getMessage());
        }
    }
    
    /**
     * 处理删除教师请求
     */
    public function delete() {
        // 检查是否为管理员
        if ($_SESSION['user']['role'] !== 'admin') {
            $this->json(null, 403, '权限不足');
        }
        
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取教师ID
        $id = $this->post('id');
        
        // 验证数据
        if (empty($id)) {
            $this->json(null, 400, '请提供教师ID');
        }
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherById($id);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->json(null, 404, '教师不存在');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 删除教师
            $this->teacherModel->deleteTeacher($id);
            
            // 删除用户
            $this->userModel->deleteUser($teacher['user_id']);
            
            // 提交事务
            $db->commit();
            
            // 返回成功响应
            $this->json(null, 200, '教师删除成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '删除教师失败：' . $e->getMessage());
        }
    }
    
    /**
     * 教师详情页面
     * @param int $id 教师ID
     */
    public function view($id) {
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherById($id);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('view', ['teacher' => $teacher]);
    }
    
    /**
     * 教师个人资料页面
     */
    public function profile() {
        // 获取当前用户ID
        $userId = $_SESSION['user']['id'];
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($userId);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->redirect('error/notFound');
        }
        
        // 渲染视图
        $this->render('profile', ['teacher' => $teacher]);
    }
    
    /**
     * 处理更新教师个人资料请求
     */
    public function updateProfile() {
        // 检查是否为POST请求
        if (!$this->isPost()) {
            $this->json(null, 400, '无效的请求方法');
        }
        
        // 获取当前用户ID
        $userId = $_SESSION['user']['id'];
        
        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($userId);
        
        // 检查教师是否存在
        if (!$teacher) {
            $this->json(null, 404, '教师不存在');
        }
        
        // 获取表单数据
        $name = $this->post('name');
        $email = $this->post('email');
        $password = $this->post('password');
        $phone = $this->post('phone');
        $title = $this->post('title');
        $subject = $this->post('subject');
        $description = $this->post('description');
        
        // 验证数据
        if (empty($name) || empty($email)) {
            $this->json(null, 400, '请填写必填字段');
        }
        
        // 检查邮箱是否已被其他用户使用
        if ($email !== $_SESSION['user']['email'] && $this->userModel->emailExists($email)) {
            $this->json(null, 400, '邮箱已被使用');
        }
        
        // 开始事务
        $db = Database::getInstance();
        $db->beginTransaction();
        
        try {
            // 更新用户信息
            $userData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone
            ];
            
            // 如果提供了新密码，则更新密码
            if (!empty($password)) {
                $userData['password'] = password_hash($password, PASSWORD_DEFAULT);
            }
            
            $this->userModel->updateUser($userId, $userData);
            
            // 更新教师信息
            $this->teacherModel->updateTeacher($teacher['id'], [
                'title' => $title,
                'subject' => $subject,
                'description' => $description
            ]);
            
            // 提交事务
            $db->commit();
            
            // 更新会话中的用户信息
            $_SESSION['user']['name'] = $name;
            $_SESSION['user']['email'] = $email;
            
            // 返回成功响应
            $this->json(null, 200, '个人资料更新成功');
        } catch (Exception $e) {
            // 回滚事务
            $db->rollBack();
            
            // 返回错误响应
            $this->json(null, 500, '更新个人资料失败：' . $e->getMessage());
        }
    }

    /**
     * 教师主页
     */
    public function dashboard() {
        // 检查是否为教师
        if ($_SESSION['user']['role'] !== 'teacher') {
            $this->redirect('error/forbidden');
        }

        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
        if (!$teacher) {
            $this->redirect('error/notFound');
        }

        // 获取统计数据
        $stats = $this->teacherModel->getTeacherStats($teacher['id']);

        // 获取今日课程
        $todaySchedule = $this->teacherModel->getTodaySchedule($teacher['id']);

        // 获取待审批请假申请
        $pendingLeaves = $this->leaveModel->getPendingLeavesByTeacher($teacher['id'], 5);

        // 渲染视图
        $this->render('dashboard', [
            'teacher' => $teacher,
            'stats' => $stats,
            'todaySchedule' => $todaySchedule,
            'pendingLeaves' => $pendingLeaves
        ]);
    }

    /**
     * 教师课表
     */
    public function schedule() {
        // 检查是否为教师
        if ($_SESSION['user']['role'] !== 'teacher') {
            $this->redirect('error/forbidden');
        }

        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
        if (!$teacher) {
            $this->redirect('error/notFound');
        }

        // 获取日期参数
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

        // 获取教师课表
        $schedule = $this->teacherModel->getTeacherSchedule($teacher['id'], $date);

        // 按星期几分组
        $weekSchedule = [];
        foreach ($schedule as $item) {
            $weekSchedule[$item['day_of_week']][] = $item;
        }

        // 渲染视图
        $this->render('schedule', [
            'teacher' => $teacher,
            'schedule' => $schedule,
            'weekSchedule' => $weekSchedule,
            'date' => $date
        ]);
    }

    /**
     * 请假审批页面
     */
    public function leaveApproval() {
        // 检查是否为教师
        if ($_SESSION['user']['role'] !== 'teacher') {
            $this->redirect('error/forbidden');
        }

        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
        if (!$teacher) {
            $this->redirect('error/notFound');
        }

        // 获取页码和过滤条件
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $page = max(1, $page);
        $perPage = 10;

        $filters = ['teacher_id' => $teacher['id']];
        if (isset($_GET['status']) && !empty($_GET['status'])) {
            $filters['status'] = $_GET['status'];
        }

        // 获取请假申请列表
        $leaves = $this->leaveModel->getLeaves($page, $perPage, $filters);

        // 获取总数
        $total = $this->leaveModel->getLeaveCount($filters);
        $totalPages = ceil($total / $perPage);

        // 渲染视图
        $this->render('leave-approval', [
            'teacher' => $teacher,
            'leaves' => $leaves,
            'page' => $page,
            'perPage' => $perPage,
            'total' => $total,
            'totalPages' => $totalPages,
            'filters' => $filters
        ]);
    }

    /**
     * 家长绑定页面
     */
    public function parentBinding() {
        // 检查是否为教师
        if ($_SESSION['user']['role'] !== 'teacher') {
            $this->redirect('error/forbidden');
        }

        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
        if (!$teacher) {
            $this->redirect('error/notFound');
        }

        // 获取教师的学生列表
        $students = $this->teacherModel->getTeacherStudents($teacher['id']);

        // 渲染视图
        $this->render('parent-binding', [
            'teacher' => $teacher,
            'students' => $students
        ]);
    }

    /**
     * 获取教师课程的学生列表（API）
     */
    public function getStudents() {
        // 检查是否为教师
        if ($_SESSION['user']['role'] !== 'teacher') {
            $this->json(null, 403, '权限不足');
        }

        // 获取教师信息
        $teacher = $this->teacherModel->getTeacherByUserId($_SESSION['user']['id']);
        if (!$teacher) {
            $this->json(null, 404, '教师信息不存在');
        }

        // 获取课程ID
        $courseId = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;

        if ($courseId) {
            // 获取特定课程的学生
            $students = $this->courseModel->getCourseStudents($courseId);
        } else {
            // 获取教师的所有学生
            $students = $this->teacherModel->getTeacherStudents($teacher['id']);
        }

        $this->json($students, 200, '获取成功');
    }
}