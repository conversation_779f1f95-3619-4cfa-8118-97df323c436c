<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_POST["action"])) {
    $conn->update('main',['weixinaddress'=>$_POST["weixinaddress"],'newweixinaddress'=>$_POST["newweixinaddress"],'wxappletaddress'=>$_POST["wxappletaddress"],'weixinredirecturl'=>$_POST["weixinredirecturl"],'isweixinregister'=>$_POST["isweixinregister"],'isweixinlogin'=>(($_POST["isweixinlogin"] == 'on')?1:0),'isthirdpartylogin'=>(($_POST["isthirdpartylogin"] == 'on')?1:0),'iswindowlogin'=>$_POST["iswindowlogin"],'issupereasylogin'=>$_POST["issupereasylogin"],'thirdpartytoken'=>$_POST["thirdpartytoken"],'thirdpartyloginurl'=>$_POST["thirdpartyloginurl"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('main','*',['id'=>1]);
$weixinaddress = $row["weixinaddress"];
$newweixinaddress = $row["newweixinaddress"];
$weixinredirecturl = $row["weixinredirecturl"];
$isweixinlogin = $row["isweixinlogin"];
$wxappletaddress = $row["wxappletaddress"];
$isweixinregister = $row["isweixinregister"];
$isthirdpartylogin = $row["isthirdpartylogin"];
$iswindowlogin = $row["iswindowlogin"];
$issupereasylogin = $row["issupereasylogin"];
$thirdpartytoken = $row["thirdpartytoken"];
$thirdpartyloginurl = $row["thirdpartyloginurl"];
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>登录方式配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">登录方式配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <form class="form-horizontal" method=post target=temp>
                <input type=hidden name=action value=set>
                <div class="space-6"></div>
                <div class="col-xs-12">
                    <div style="padding-left:50px;">
                        <div style="font-size:25px;padding-bottom:20px;"><input type=checkbox name=isweixinlogin <?php if ($isweixinlogin) echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">微信一键登陆</div>

                        <div class="form-group">
                            <label class="col-lg-4 control-label">一键登录绑定的微信服务号URL：</label>

                            <div class="col-lg-4">
                                <input type="text" value="<?php echo $weixinaddress; ?>" style="text-align:left;" id="weixinaddress" name="weixinaddress" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-4 control-label">一键登录绑定的新微信服务号URL：</label>

                            <div class="col-lg-4">
                                <input type="text" value="<?php echo $newweixinaddress; ?>" style="text-align:left;" id="newweixinaddress" name="newweixinaddress" class="bg-focus form-control" autoComplete="off" placeholder="不懂请留空，不要乱填">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-4 control-label">一键登录绑定的微信小程序：</label>

                            <div class="col-lg-4">
                                <input type="text" value="<?php echo $wxappletaddress; ?>" style="text-align:left;" id="wxappletaddress" name="wxappletaddress" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-4 control-label">手机微信扫码登录后跳转到URL：</label>

                            <div class="col-lg-4">
                                <input type="text" value="<?php echo $weixinredirecturl; ?>" style="text-align:left;" id="weixinredirecturl" name="weixinredirecturl" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-4 control-label">首次登录仅支持微信扫码：</label>

                            <div class="col-lg-4">
                                <select name=isweixinregister>
                                    <option value=1 <?php echo ($isweixinregister) ? "selected" : "" ?>>是</option>
                                    <option value=0 <?php echo ($isweixinregister) ? "" : "selected" ?>>否</option>
                                </select>
                            </div>
                        </div>
                        <div style="font-size:20px;">关于微信登录原理和配置方式介绍请 <a href='https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=6' target='_blank'>点击这里查看</a></div>
                    </div>
                    <div style="padding:50px 0 0 50px;">
                        <div style="font-size:25px;padding-bottom:20px;"><input type=checkbox name=isthirdpartylogin <?php if ($isthirdpartylogin) echo 'checked'; ?> style="width:20px;height:20px;margin:2px 10px 3px 10px;">注册登录 / 第三方系统登录</div>
                        <div class="form-group">
                            <label class="col-lg-4 control-label">用户登录模式：</label>

                            <div class="col-lg-4">
                                <select name=iswindowlogin>
                                    <option value=1 <?php echo ($iswindowlogin) ? "selected" : "" ?>>系统内置用户登录</option>
                                    <option value=0 <?php echo ($iswindowlogin) ? "" : "selected" ?>>第三方用户登录</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <input type=hidden name=action value=set>
                            <label class="col-lg-4 control-label">第三方单点登录超级简单模式：</label>

                            <div class="col-lg-4">
                                <select name=issupereasylogin>
                                    <option value=1 <?php echo ($issupereasylogin) ? "selected" : "" ?>>是</option>
                                    <option value=0 <?php echo ($issupereasylogin) ? "" : "selected" ?>>否</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-4 control-label">第三方登录接口Token：</label>

                            <div class="col-lg-2">
                                <input type="text" value="<?php echo $thirdpartytoken; ?>" style="text-align:left;" id="thirdpartytoken" name="thirdpartytoken" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-4 control-label">第三方登录URL：</label>

                            <div class="col-lg-4">
                                <input type="text" value="<?php echo $thirdpartyloginurl; ?>" style="text-align:left;" id="thirdpartyloginurl" name="thirdpartyloginurl" class="bg-focus form-control" autoComplete="off">
                            </div>
                        </div>
                        <div style="font-size:20px;">第三方单点登录接口文档请 <a href='https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=62' target='_blank'>点击这里查看</a></div>
                    </div>
                </div>
                <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                    <div class="col-lg-6 col-lg-offset-3">
                        <button type="submit" class="btn btn-primary">确认设置</submit>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>