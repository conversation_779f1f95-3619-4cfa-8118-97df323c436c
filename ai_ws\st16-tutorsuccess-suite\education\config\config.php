<?php
/**
 * 系统配置文件
 */

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'education_system');
define('DB_USER', 'root');
define('DB_PASS', 'root');
define('DB_CHARSET', 'utf8');

// 应用配置
define('APP_NAME', '特靠谱教培系统');
define('APP_VERSION', '1.0.0');
define('DEBUG_MODE', true);

// 路径配置
define('BASE_URL', '/education.php');
define('UPLOAD_PATH', APP_PATH . '/public/uploads');

// 微信配置
define('WX_APPID', '');
define('WX_SECRET', '');
define('WX_TOKEN', '');
define('WX_REDIRECT_URL', '');

// 会话配置
define('SESSION_PREFIX', 'edu_');
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_PREFIX . 'sid');
    session_start();
}

// 错误处理
if (DEBUG_MODE) {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    error_reporting(0);
}

// 时区设置
date_default_timezone_set('Asia/Shanghai');