<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
$ipagecurrent = isset($_REQUEST["page"]) ? $_REQUEST["page"] : 1;
$searchitem = isset($_REQUEST["searchitem"]) ? $_REQUEST["searchitem"] : '';
$keyword = isset($_REQUEST["keyword"]) ? $_REQUEST["keyword"] : '';
$order = isset($_REQUEST["order"]) ? $_REQUEST["order"] : '';
$orderdesc = isset($_REQUEST["orderdesc"]) ? $_REQUEST["orderdesc"] : '';
$pagenumber = 5; //每页显示数据条数
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>对话日志</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        pre,
        code {
            font-family: 'Monaco', courier, monospace;
            font-size: 12px;
            color: black;
            background: transparent;
            border: 0;
            padding: 0;
            margin: 0;
            line-height: 15px;
            word-wrap: break-word;
            white-space: break-spaces;
        }

        form {
            margin: 0px;
            display: inline
        }

        select {
            vertical-align: bottom;
        }
    </style>
</head>

<body class="no-skin" style="font-family:'微软雅黑';overflow-x: auto">
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-bar-chart-o"></i> 统计日志</li>
                <li class="active">对话日志</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px;">
            <table class="table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <td colspan="4">
                            <form target=_self method=get style="margin:0px;display: inline">
                                搜索：<select name=searchitem style='width:120px;margin-right:10px;' onchange='changecontent(this.value);'>
                                    <option value="">请选择</option>
                                    <option value=userid <?php if ($searchitem == "userid") echo "selected"; ?>>UID</option>
                                    <option value=realtime <?php if ($searchitem == "realtime") echo "selected"; ?>>提问时间范围</option>
                                    <option value=question <?php if ($searchitem == "question") echo "selected"; ?>>提问内容</option>
                                    <option value=answer <?php if ($searchitem == "answer") echo "selected"; ?>>回答内容</option>
                                </select>
                                <span id=keywordpanel style="display:<?php if ($searchitem == "realtime") echo "none"; ?>;">关键字：<input name=keyword value="<?php echo $keyword; ?>" id='keyword' style='width:140px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:8px;'></span>
                                <span id=selecttime style="display:<?php if ($searchitem != "realtime")  echo "none"; ?>;">时间范围：<input size=12 name=time1 style="height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;padding-left:5px;" value='<?php echo isset($_REQUEST["time1"]) ? $_REQUEST["time1"] : date("Y-m-d"); ?>'> 至 <input style="padding-left:5px;height:30px;font-size:14px;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;" size=12 name=time2 value='<?php echo isset($_REQUEST["time2"]) ? $_REQUEST["time2"] : date("Y-m-d"); ?>'></span>
                                &nbsp;&nbsp;&nbsp;按照
                                <select name=order style="width:120px;">
                                    <option value="">请选择</option>
                                    <option value=userid <?php if ($order == "userid") echo "selected"; ?>>UID</option>
                                    <option value=realtime <?php if ($order == "realtime") echo "selected"; ?>>提问时间范围</option>
                                </select>&nbsp;&nbsp;&nbsp;
                                <select name=orderdesc style="width:100px;">
                                    <option value="desc" <?php if ($orderdesc == 'desc') echo "selected"; ?>>降序排列</option>
                                    <option value="">升序排列</option>
                                </select>
                                &nbsp;&nbsp;&nbsp;
                                <button class="btn btn-sm btn-info" style="padding:2px 10px;" type=submit>查询</button>
                                <!--
                                        <button class="btn btn-sm btn-info" style="padding:2px 10px;" onclick="document.getElementById('temp').src='saveexcel_userlist.php';return false;">导出所有用户</button>
                                        -->
                            </form>
                        </td>
                    </tr>
                    <tr>
                        <th style="width:50px;">序号</th>
                        <th style="width:150px;">提问时间</th>
                        <th style="width:70px;">用户UID</th>
                        <th>问答记录</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $sql = "select * from chathistory ";
                    if ($searchitem != "") {
                        if ($searchitem == "realtime") {
                            $sql .= " where " . addslashes($searchitem) . " between '" . addslashes($_REQUEST["time1"]) . "' and '" . addslashes(date("Y-m-d", strtotime("+1 day", strtotime($_REQUEST["time2"])))) . "' ";
                        } else if ($searchitem == "userid") {
                            $sql .= " where userid = " . addslashes(($keyword - 1001)) . " ";
                        } else {
                            $sql .= " where " . addslashes($searchitem) . " like '%" . addslashes($keyword) . "%' ";
                        }
                    }
                    if ($order !== "") {
                        $sql .= " order by " . addslashes($order) . " " . addslashes($orderdesc);
                    } else {
                        $sql .= " order by id desc";
                    }
                    $sqlcount = "select count(t.id) from (" . $sql . ") t";
                    $result = $conn->query($sqlcount);
                    $row = $result->fetch();
                    $totalnumber = $row[0];
                    $ipagecount = ceil($totalnumber / $pagenumber);
                    if ($totalnumber == 0) {
                        echo "<tr><td colspan=4>未找到匹配的结果。</td></tr>";
                    } else {
                        if ($ipagecurrent > $ipagecount) $ipagecurrent = $ipagecount;
                        if ($ipagecurrent < 1) $ipagecurrent = 1;
                        $count = 0;
                        $startcount = ($ipagecurrent - 1) * $pagenumber;
                        $sql .= " limit " . $startcount . "," . $pagenumber;
                        $result = $conn->query($sql);
                        while ($row = $result->fetch()) {
                            $count++;
                            if (!empty($row["modelid"])) {
                                $modelarr = $conn->get('model', ['modelname', 'modelprice'], ['id' => $row["modelid"]]);
                            } else {
                                $modelarr = $conn->get('model', ['modelname', 'modelprice'], ['ORDER' => ['sequenceid', 'id']]);
                            }
                    ?>
                            <tr>
                                <td rowspan='2' class='center'><?php echo $count ?></td>
                                <td rowspan='1' class='center'><?php echo $row["realtime"] ?></td>
                                <td rowspan='1' class='center'><?php echo ($row["userid"] + 1001) ?></td>
                                <td>
                                    <pre><?php echo htmlspecialchars($row["question"]) ?></pre>
                                    <?php
                                    if (substr($row["question"], 0, 6) == '![IMG]') {
                                        $markdownCode = $row["question"];
                                        $htmlCode = str_replace('![IMG](', '<img src="', $markdownCode);
                                        $htmlCode = str_replace(')', '">', $htmlCode);
                                        echo $htmlCode;
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <td colspan=2>
                                    <?php echo $modelarr["modelname"] . "（" . $modelarr["modelprice"] . "积分）" ?>
                                </td>
                                <td>
                                    <pre><?php echo htmlspecialchars($row["answer"]) ?></pre>
                                    <?php
                                    if (substr($row["answer"], 0, 6) == '![IMG]') {
                                        $markdownCode = $row["answer"];
                                        $htmlCode = str_replace('![IMG](', '<img src="', $markdownCode);
                                        $htmlCode = str_replace(')', '">', $htmlCode);
                                        echo $htmlCode;
                                    }
                                    ?>
                                </td>
                            </tr>
                    <?php
                        }
                    }

                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
    echo "<table width='100%' border='0' cellspacing='0' cellpadding='5'><tr><td height='20' align=center style='border-width:0pt'>每页显示 " . $pagenumber . " 个记录　共有 " . $ipagecount . " 页　当前为第 " . $ipagecurrent . " 页 ";
    if ($ipagecurrent == 1) {
        echo "　　首页　 | ";
    } else {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='1'>　　首页　</form> | ";
    }
    if ($ipagecurrent == 1) {
        echo "　上一页　 | ";
    } else {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent - 1) . "'>　上一页　</form> | ";
    }
    if ($ipagecount > $ipagecurrent) {
        echo "<form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . ($ipagecurrent + 1) . "'>　下一页　</form> ";
    } else {
        echo "　下一页　";
    }
    if ($ipagecount > $ipagecurrent) {
        echo "| <form method=post onclick='this.submit();' style='cursor:pointer;'><input type=hidden name=page value='" . $ipagecount . "'>　末页　</form> ";
    } else {
        echo "| 　末页　 ";
    }
    echo "</td></tr></table>";
    ?>
    <br><br>
    <script>
        function changecontent(cnt) {
            if (cnt == "realtime") {
                document.getElementById("selecttime").style.display = "";
                document.getElementById("keywordpanel").style.display = "none";
                document.getElementById("keywordpanel").value = "";
            } else {
                document.getElementById("selecttime").style.display = "none";
                document.getElementById("keywordpanel").style.display = "";
                document.getElementById("keywordpanel").value = "";
            }
        }
    </script>
    <iframe name=temp id=temp style='display:none'></iframe>
</body>

</body>

</html>