当前版本：6.6.2434
版本发布说明：https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=132

运行环境要求：
1. 建议使用Nginx，版本无所谓，比Apache好配置一些。
2. PHP建议7.4版本（其他版本没测试，已知某些版本部分页面会有bug）。
3. MySQL建议5.7版本（任何版本都能正常运行，低版本性能消耗低一些）

——————————————————————————————————————————————

初次安装请按照如下步骤配置网站：
1. web文件夹中是源代码，请把里面的文件全部复制到网站根目录。复制完成后记得给所有目录和文件写权限（宝塔默认就会给）。
2. chatgpt_v6.6.2434.sql文件是Mysql数据库文件，您可以请使用宝塔或phpmyadmin等工具导入数据库中。数据库名、对应的用户名和密码可自行设置。
3. 修改/admin/mysqlconn.php文件，将上一步设置的数据库名、用户名和密码配置进去。
4. 使用浏览器访问网站后台http://您的域名/admin，默认用户名：admin，默认密码：admin@chatgpt，登录后建议立即修改密码。
5. 在后台配置模型和API_KEY，每个模型都要单独设置API_KEY。
6. 修改参数配置，根据您的喜好替换网站名称、赠送的查询次数、有效期天数、首页logo等配置。注：logo如果更换失败就是文件权限的问题。
7. 后台“检查更新”页面可以实现网站的在线更新，首次使用需要绑定授权，不知道的请找我要。更新需要绑定域名，顶级域名即可，不限二级域名。
8. 访问http://您的域名/test.php，如果您看到的0-9是一个一个蹦出来的，说明您的环境不需要修改配置，否则请到内部论坛参考我的文章修改您的配置，不修改配置的话无法实现问答在1秒左右回复。配置方法见：https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=10
9. 建议您开启网站的https方式访问，否则语音识别等功能可能会受浏览器安全限制而无法正常工作

以上安装说明图文版可以在内部论坛的以下地址看到：
https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=3

关于域名、服务器、微信公众号、支付方法等相关问题内部论坛上已有文章介绍，建议认真阅读一下。

——————————————————————————————————————————————

老版本升级方法：
1. 把您原来的网站做好备份，比如直接把原网站目录改名
2. 解压后将web目录中的所有文件放到网站根目录
3. 修改admin/mysqlconn.php文件内容，或不覆盖上个版本的同名文件
4. 备份原网站数据库，以防万一，通过phpmyadmin导出或宝塔备份都可以
5. 访问http://您的域名/install.php同步数据库结构

以上操作不会改动、覆盖或删除任何用户数据，请放心升级。

——————————————————————————————————————————————

常用的网址（国内无法访问时可以翻墙）：
最新网站源码下载地址：
https://update.ipfei.cn/download.php
自助修改绑定域名地址：
https://update.ipfei.cn/updatedomain.php
内部论坛地址：
https://bbs.ipfei.cn
网站后台一键登录绑定的微信服务号默认地址（仅供测试使用，正式运营的网站请您使用自己的微信服务号）：
https://openid.ipfei.cn/getwxopenid.php
