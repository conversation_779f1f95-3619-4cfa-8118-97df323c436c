<?php
function customError($errno, $errstr, $errfile, $errline)
{
    echo "<b>Error number:</b> [$errno],error on line $errline in $errfile<br />";
    die();
}
set_error_handler("customError", E_ERROR);
$getfilter = "'|(and|or)\\b.+?(>|<|=|in|like)|\\/\\*.+?\\*\\/|<\\s*script\\b|\\bEXEC\\b|UNION.+?Select|Update.+?SET|Insert\\s+INTO.+?VALUES|(Select|Delete).+?FROM|(Create|Alter|Drop|TRUNCATE)\\s+(TABLE|DATABASE)";
$postfilter = "\\b(and|or)\\b.{1,6}?(=|>|<|\\bin\\b|\\blike\\b)|\\/\\*.+?\\*\\/|<\\s*script\\b|\\bEXEC\\b|UNION.+?Select|Update.+?SET|Insert\\s+INTO.+?VALUES|(Select|Delete).+?FROM|(Create|Alter|Drop|TRUNCATE)\\s+(TABLE|DATABASE)";
$cookiefilter = "\\b(and|or)\\b.{1,6}?(=|>|<|\\bin\\b|\\blike\\b)|\\/\\*.+?\\*\\/|<\\s*script\\b|\\bEXEC\\b|UNION.+?Select|Update.+?SET|Insert\\s+INTO.+?VALUES|(Select|Delete).+?FROM|(Create|Alter|Drop|TRUNCATE)\\s+(TABLE|DATABASE)";
function StopAttack($StrFiltKey, $StrFiltValue, $ArrFiltReq)
{

    if (is_array($StrFiltValue)) {
        $StrFiltValue = implode($StrFiltValue);
    }
    if (preg_match("/" . $ArrFiltReq . "/is", $StrFiltValue) == 1) {
        slog("<br><br>操作IP: " . $_SERVER["REMOTE_ADDR"] . "<br>操作时间: " . date("Y-m-d H:i:s") . "<br>操作页面:" . $_SERVER["PHP_SELF"] . "<br>提交方式: " . $_SERVER["REQUEST_METHOD"] . "<br>提交参数: " . $StrFiltKey . "<br>提交数据: " . $StrFiltValue);
        echo '{"error":{"code":"sql_injection","message":"您的提问触发了SQL注入攻击防护机制，请修改下问题重新提问。"}}';
        exit();
    }
}
foreach ($_GET as $key => $value) {
    StopAttack($key, $value, $getfilter);
}
foreach ($_POST as $key => $value) {
    StopAttack($key, $value, $postfilter);
}
foreach ($_COOKIE as $key => $value) {
    StopAttack($key, $value, $cookiefilter);
}
function slog($logs)
{
    $toppath = $_SERVER["DOCUMENT_ROOT"] . "/attacklog.php";
    $Ts = fopen($toppath, "a+");
    fputs($Ts, $logs . "\r\n");
    fclose($Ts);
}
