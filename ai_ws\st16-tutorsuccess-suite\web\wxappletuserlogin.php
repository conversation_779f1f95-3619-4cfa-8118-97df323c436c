<?php
use Medoo\Medoo;
if (isset($_GET["from"])) {
    $ismobile = 1;
} else {
    $ismobile = 0;
}

if (isset($_GET["openid"])) {
    if (trim($_GET["openid"]) == "") {
        echo "未获取到用户信息\n\n请点击下面的按钮返回主程序";
        exit;
    }
    require_once('admin/mysqlconn.php');
    $row = $conn->get('main','*',['id'=>1]);
    $freetry = $row["freetry"];
    $freedays = $row["freedays"];
    $weixinaddress = $row["weixinaddress"];
    $weixinredirecturl = $row["weixinredirecturl"];
    $httpprotocol = "https";
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' || isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
        $httpprotocol = "https";
    }
    $row = $conn->get("user","*",['appletopenid'=>$_GET["openid"]]);
    if (empty($row)) {
        $conn->insert('user',['appletopenid'=>$_GET["openid"],'rndstr'=>$_GET["userrndstr"],'quota'=>$freetry,'expiretime'=>date('Y-m-d H:i:s', strtotime('+' . $freedays . ' day')),'registertime'=>date('Y-m-d H:i:s'),'loginip'=>$_SERVER["REMOTE_ADDR"],'logintime'=>date('Y-m-d H:i:s'),'ismobile'=>$ismobile]);
        if (!$conn->error) {
            $conn->update('user',['userid'=>Medoo::raw('id+1001')],['rndstr'=>$_GET["userrndstr"]]);
            if ($ismobile) {
                echo "欢迎新用户，您已完成登录\n\n安卓用户请点击下方按钮返回\n苹果用户请点击屏幕左上角切回";
            } else {
                echo "欢迎新用户，您已完成登录";
            }
        } else {
            echo $conn->error;
        }
    } else {
        if ($row["isforbidden"]) {
            echo "对不起，您的账号已被封禁";
        } else {
            $conn->update('user',['rndstr'=>$_GET['userrndstr'],'ismobile'=>$ismobile,'loginip'=>$row['loginip'] . ";" . $_SERVER["REMOTE_ADDR"],'logintime'=>$row['logintime'] . ";" . date('Y-m-d H:i:s')],['id'=>$row['id']]);
            if ($ismobile) {
                echo "欢迎回来，您已完成登录\n\n安卓用户请点击下方按钮返回\n苹果用户请点击屏幕左上角切回";
            } else {
                echo "欢迎回来，您已完成登录";
            }
        }
    }
    
}
