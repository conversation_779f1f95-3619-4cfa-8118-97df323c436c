<?php
session_start();
use Medoo\Medoo;
if ((isset($_GET["userrndstr"])) && (isset($_GET["cardpass"]))) {
    if (!isset($_SESSION['rechargetime'])) {
        $_SESSION['rechargetime'] = 6;
    }
    if ($_SESSION['rechargetime'] <= 0) {
        echo '{"success":false,"message":"充值卡密码错误次数过多，请半小时后再来"}';
        exit(0);
    }

    require_once('admin/mysqlconn.php');
    if (!$conn->has('user',['rndstr'=>$_GET["userrndstr"]])) {
        echo '{"success":false,"message":"当前用户登录状态已过期，请刷新页面重新登录"}';
    } else {
        $row = $conn->get('card','*',['cardpass'=>$_GET["cardpass"]]);
        if (empty($row)) {
            echo '{"success":false,"message":"充值密码错误，您还有' . $_SESSION['rechargetime'] . '次尝试机会"}';
            $_SESSION['rechargetime']--;
        } else {
            $cardid = $row["cardid"];
            if ($row["isused"]) {
                echo '{"success":false,"message":"该充值卡已于' . $row["bindtime"] . '被使用"}';
            } else if ($row["isinvalid"]) {
                echo '{"success":false,"message":"该充值卡已被管理员作废"}';
            } else {
                $row = $conn->get('cardtype','*',['id'=>$row["cardtype"]]);
                $_SESSION['rechargetime'] = 6;
                $row2 = $conn->get('user','*',['rndstr'=>$_GET["userrndstr"]]);
                if (strtotime($row2["expiretime"]) < strtotime(date("Y-m-d H:i:s"))) {
                    $conn->update("user",["quota[+]"=>$row["quota"],"expiretime"=>Medoo::raw("DATE_ADD(CURDATE(), INTERVAL ".$row["extenddays"]." DAY)")],["rndstr"=>$_GET["userrndstr"]]); 
                } else {
                    $conn->update("user",["quota[+]"=>$row["quota"],"expiretime"=>Medoo::raw("DATE_ADD(expiretime, INTERVAL ".$row["extenddays"]." DAY)")],["rndstr"=>$_GET["userrndstr"]]);
                }
                
                $conn->update('card',['isused'=>1,'binduser'=>$row2["userid"],'bindtime'=>date('Y-m-d H:i:s')],['cardpass'=>$_GET["cardpass"]]);
                $conn->insert('rechargelog',['userid'=>$row2["userid"],'quota'=>$row["quota"],'extenddays'=>$row["extenddays"],'rechargetime'=>date('Y-m-d H:i:s'),'rechargecardid'=>$cardid]);
                echo '{"success":true}';
            }
        }
    }
}
