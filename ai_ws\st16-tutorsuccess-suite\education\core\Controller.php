<?php
namespace Core;

/**
 * 控制器基类
 */
class Controller
{
    protected $request;
    protected $view;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->request = new Request();
        $this->view = new View();
    }
    
    /**
     * 渲染视图
     * 
     * @param string $view 视图名称
     * @param array $data 数据
     * @return string 渲染结果
     */
    protected function render($view, $data = [])
    {
        return $this->view->render($view, $data);
    }
    
    /**
     * 重定向
     * 
     * @param string $url 重定向URL
     */
    protected function redirect($url)
    {
        header('Location: ' . $url);
        exit;
    }
    
    /**
     * 返回JSON响应
     * 
     * @param mixed $data 数据
     * @param int $status HTTP状态码
     */
    protected function json($data, $status = 200)
    {
        header('Content-Type: application/json');
        http_response_code($status);
        echo json_encode($data);
        exit;
    }
    
    /**
     * 获取当前登录用户
     * 
     * @return array|null 用户信息
     */
    protected function getCurrentUser()
    {
        return isset($_SESSION[SESSION_PREFIX . 'user']) ? $_SESSION[SESSION_PREFIX . 'user'] : null;
    }
    
    /**
     * 检查用户是否已登录
     * 
     * @param bool $redirect 是否重定向到登录页
     * @return bool 是否已登录
     */
    protected function checkLogin($redirect = true)
    {
        if (!$this->getCurrentUser()) {
            if ($redirect) {
                $this->redirect(BASE_URL . '/login');
            }
            return false;
        }
        return true;
    }
    
    /**
     * 检查用户是否有权限
     * 
     * @param array $roles 角色列表
     * @param bool $redirect 是否重定向到无权限页
     * @return bool 是否有权限
     */
    protected function checkPermission($roles, $redirect = true)
    {
        $user = $this->getCurrentUser();
        if (!$user || !isset($user['role']) || !in_array($user['role'], $roles)) {
            if ($redirect) {
                $this->redirect(BASE_URL . '/nopermission');
            }
            return false;
        }
        return true;
    }
}