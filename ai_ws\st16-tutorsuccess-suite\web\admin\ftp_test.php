<?php

error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_WARNING);
if (empty($_REQUEST["ftp"])) {
    showalert("未填写FTP服务器地址");
    exit(1);
}
$host = $_REQUEST["ftp"];
if (strpos($host, ":") !== false) {
    $port = explode(":", $_REQUEST["ftp"])[1];
    $host = explode(":", $_REQUEST["ftp"])[0];
}
$user = $_REQUEST["user"];
$pwd = $_REQUEST["pass"];
$dir = $_REQUEST["dir"];

// 进行ftp连接，根据port是否设置，传递的参数会不同
if (empty($port)) {
    $f_conn = ftp_connect($host, 21, 5);
} else {
    $f_conn = ftp_connect($host, $port, 5);
}
if (!$f_conn) {
    $alertstr = "连接服务器失败";
    showalert($alertstr);
    exit(1);
}
$alertstr = "***连接测试开始***\\n连接服务器成功\\n";

// 进行ftp登录，使用给定的ftp登录用户名和密码进行login
$f_login = ftp_login($f_conn, $user, $pwd);
if (!$f_login) {
    $alertstr .= "用户登陆失败\\n";
    showalert($alertstr);
    exit(1);
}
$alertstr .= "用户登陆成功\\n";

// 获取当前所在ftp目录下包含的目录与文件
$exist_dir = ftp_nlist($f_conn, $dir);
if (!is_array($exist_dir)) {
    $alertstr .= "进入被动模式\\n";
    ftp_pasv($f_conn, true);
    $exist_dir = ftp_nlist($f_conn, $dir);
}

// 要求是按照日期在ftp目录下创建文件夹作为文件上传存放目录

$dir_name = $dir . "dir_test";
// 检查ftp目录下是否已存在当前日期的文件夹，如不存在则进行创建
if ((!in_array("$dir_name", $exist_dir)) && (!in_array("dir_test", $exist_dir))) {
    if (!ftp_mkdir($f_conn, $dir_name)) {
        $alertstr .= "建立测试目录失败\\n";
        showalert($alertstr);
        exit(1);
    } else {
        $alertstr .= "建立测试目录成功\\n";
    }
} else {
    $alertstr .= "测试目录已存在\\n";
}
// 切换目录
if (!ftp_chdir($f_conn, $dir_name)) {
    $alertstr .= "切换目录失败\\n";
    showalert($alertstr);
    exit(1);
} else {
    $alertstr .= "切换目录成功\\n";
}
// 进行文件上传
$numbytes = file_put_contents('test.txt', 'test');
$result = ftp_put($f_conn, 'test.txt', 'test.txt', FTP_BINARY);
if (!$result) {
    $alertstr .= "上传测试文件失败\\n";
    showalert($alertstr);
    exit(1);
} else {
    $alertstr .= "上传测试文件成功\\n";
}
@unlink('test.txt');
$result = ftp_delete($f_conn, 'test.txt');
if (!$result) {
    $alertstr .= "删除测试文件失败\\n***连接测试结束***\\n\\n无删除权限不影响备份数据库文件，请点击“确认设置”保存";
    showalert($alertstr);
    exit(1);
} else {
    $alertstr .= "删除测试文件成功\\n";
}
$result = ftp_rmdir($f_conn, $dir_name);
if (!$result) {
    $alertstr .= "删除测试目录失败\\n***连接测试结束***\\n\\n无删除权限不影响备份数据库文件，请点击“确认设置”保存";
    showalert($alertstr);
    exit(1);
} else {
    $alertstr .= "删除测试目录成功\\n***连接测试结束***\\n\\n测试成功完成，请点击“确认设置”保存";
    showalert($alertstr);
    exit(0);
}
function showalert($str)
{
    echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';setTimeout(function(){alert('" . $str . "');},100);</script></body></html>";
}
