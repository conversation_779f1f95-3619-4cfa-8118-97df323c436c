<?php

use Medoo\Medoo;

if (isset($_GET["attachid"])) {
    require_once('admin/mysqlconn.php');
    $row = $conn->get('wxpaylist', '*', ['attachid' => $_GET["attachid"]]);
    if (empty($row)) {
        echo "fail";
        exit;
    } else {
        echo "success";
        if (!$row["isconfirmed"]) {
            $userid = $row["userid"];
            $wxpaylistid = $row["id"];
            $row = $conn->get('cardtype', '*', ['id' => $row["cardtype"]]);
            $quota = $row["quota"];
            $extenddays = $row["extenddays"];
            $conn->update('wxpaylist', ['isconfirmed' => 1, 'out_trade_no' => $_GET['out_trade_no'], 'transaction_id' => $_GET['transaction_id'], 'payopenid' => $_GET['payopenid'], 'confirmtime' => date('Y-m-d H:i:s')], ['attachid' => $_GET["attachid"]]);
            $row = $conn->get('user', '*', ['userid' => $userid]);
            if (strtotime($row["expiretime"]) < strtotime(date("Y-m-d H:i:s"))) {
                $conn->update("user", ["quota[+]" => $quota, "expiretime" => Medoo::raw("DATE_ADD(CURDATE(), INTERVAL " . $extenddays . " DAY)")], ["userid" => $userid]);
            } else {
                $conn->update("user", ["quota[+]" => $quota, "expiretime" => Medoo::raw("DATE_ADD(expiretime, INTERVAL " . $extenddays . " DAY)")], ["userid" => $userid]);
            }
            $conn->insert('rechargelog', ['userid' => $userid, 'quota' => $quota, 'extenddays' => $extenddays, 'rechargetime' => date('Y-m-d H:i:s'), 'wxpaylistid' => $wxpaylistid]);
        }
        exit;
    }
}
