<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特靠谱教培系统 - 新架构测试</title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            border: none;
            padding: 30px;
        }
        .role-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border-radius: 15px;
            padding: 30px;
            margin: 15px 0;
            background: white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .role-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        .role-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .teacher-card {
            border-left: 5px solid #28a745;
        }
        .teacher-card .role-icon {
            color: #28a745;
        }
        .student-card {
            border-left: 5px solid #007bff;
        }
        .student-card .role-icon {
            color: #007bff;
        }
        .admin-card {
            border-left: 5px solid #dc3545;
        }
        .admin-card .role-icon {
            color: #dc3545;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #28a745;
            margin-right: 10px;
        }
        .new-badge {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 10px;
        }
        .url-example {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header text-center">
                        <h1><i class="fa fa-graduation-cap"></i> 特靠谱教培系统</h1>
                        <p class="mb-0">全新ThinkPHP架构 - 开发测试版本 <span class="new-badge">NEW</span></p>
                    </div>
                    <div class="card-body p-5">
                        <div class="row mb-4">
                            <div class="col-12">
                                <h4 class="text-center mb-4">🎯 新架构特性</h4>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6><i class="fa fa-check-circle text-success"></i> URL结构优化</h6>
                                        <div class="url-example">
                                            <strong>新:</strong> /education/teacher/<br>
                                            <strong>旧:</strong> /education/views/teacher/
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6><i class="fa fa-check-circle text-success"></i> MVC架构</h6>
                                        <ul class="feature-list">
                                            <li><i class="fa fa-folder"></i> Controller 控制器</li>
                                            <li><i class="fa fa-folder"></i> Model 模型</li>
                                            <li><i class="fa fa-folder"></i> View 视图</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4 class="text-center mb-4">🚀 选择身份进入系统</h4>
                        
                        <div class="row">
                            <!-- 教师入口 -->
                            <div class="col-md-4">
                                <div class="role-card teacher-card text-center" onclick="location.href='test.php?path=teacher'">
                                    <div class="role-icon">
                                        <i class="fa fa-chalkboard-teacher"></i>
                                    </div>
                                    <h5>教师端</h5>
                                    <p class="text-muted">课程管理、请假审批、学生管理</p>
                                    <ul class="feature-list text-start">
                                        <li><i class="fa fa-check"></i> 教师主页</li>
                                        <li><i class="fa fa-check"></i> 课程表管理</li>
                                        <li><i class="fa fa-check"></i> 请假审批</li>
                                        <li><i class="fa fa-check"></i> 学生考勤</li>
                                    </ul>
                                    <div class="url-example">
                                        <strong>URL:</strong> /education/teacher/
                                    </div>
                                </div>
                            </div>

                            <!-- 学生入口 -->
                            <div class="col-md-4">
                                <div class="role-card student-card text-center" onclick="location.href='test.php?path=student'">
                                    <div class="role-icon">
                                        <i class="fa fa-user-graduate"></i>
                                    </div>
                                    <h5>学生端</h5>
                                    <p class="text-muted">课程查看、请假申请、成绩查询</p>
                                    <ul class="feature-list text-start">
                                        <li><i class="fa fa-check"></i> 学生主页</li>
                                        <li><i class="fa fa-check"></i> 我的课表</li>
                                        <li><i class="fa fa-check"></i> 请假申请</li>
                                        <li><i class="fa fa-check"></i> 成绩查询</li>
                                    </ul>
                                    <div class="url-example">
                                        <strong>URL:</strong> /education/student/
                                    </div>
                                </div>
                            </div>

                            <!-- 管理员入口 -->
                            <div class="col-md-4">
                                <div class="role-card admin-card text-center" onclick="location.href='test.php?path=admin'">
                                    <div class="role-icon">
                                        <i class="fa fa-user-cog"></i>
                                    </div>
                                    <h5>管理员</h5>
                                    <p class="text-muted">系统管理、用户管理、数据统计</p>
                                    <ul class="feature-list text-start">
                                        <li><i class="fa fa-check"></i> 系统概览</li>
                                        <li><i class="fa fa-check"></i> 用户管理</li>
                                        <li><i class="fa fa-check"></i> 课程管理</li>
                                        <li><i class="fa fa-check"></i> 数据统计</li>
                                    </ul>
                                    <div class="url-example">
                                        <strong>URL:</strong> /education/admin/
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-5">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fa fa-info-circle"></i> 开发说明</h6>
                                    <p class="mb-2">本版本采用全新的ThinkPHP 5.1风格架构：</p>
                                    <ul class="mb-0">
                                        <li><strong>控制器:</strong> application/controller/ - 处理业务逻辑</li>
                                        <li><strong>视图:</strong> application/view/ - 页面模板</li>
                                        <li><strong>模型:</strong> application/model/ - 数据操作</li>
                                        <li><strong>路由:</strong> 支持友好URL，无需暴露views目录</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <a href="index2dev.php" class="btn btn-outline-secondary">
                                <i class="fa fa-arrow-left"></i> 返回旧版测试页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>
