/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-primary {
    background-color: #1976d2;
    border-color: #1976d2;
}

.btn-primary:hover {
    background-color: #1565c0;
    border-color: #1565c0;
}

/* 表单样式 */
.form-control {
    border-radius: 4px;
    padding: 10px 12px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.2rem rgba(25, 118, 210, 0.25);
}

/* 表格样式 */
.table {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 页脚样式 */
footer {
    margin-top: 50px;
    border-top: 1px solid #eee;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 15px;
    }
}

/* 课表样式 */
.schedule-table th, .schedule-table td {
    text-align: center;
    vertical-align: middle;
}

.schedule-table th {
    background-color: #f0f8ff;
}

.schedule-item {
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 5px;
    font-size: 14px;
}

.schedule-item-course {
    background-color: #e3f2fd;
    border-left: 3px solid #1976d2;
}

.schedule-item-break {
    background-color: #f5f5f5;
    border-left: 3px solid #9e9e9e;
}

/* 请假表单样式 */
.leave-form {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 状态标签样式 */
.status-pending {
    background-color: #ffecb3;
    color: #ff8f00;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-approved {
    background-color: #c8e6c9;
    color: #2e7d32;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.status-rejected {
    background-color: #ffcdd2;
    color: #c62828;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
}