<?php
/**
 * 课程数据模型
 * 创建时间：2025-01-22
 */

require_once 'BaseModel.php';

class Course extends BaseModel {
    
    public function __construct($conn) {
        parent::__construct($conn);
        $this->table = TABLE_COURSE;
    }
    
    /**
     * 获取课程列表（包含教师信息）
     * @param array $conditions
     * @param string $order_by
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function getCoursesWithTeacher($conditions = [], $order_by = 'c.create_time DESC', $limit = 0, $offset = 0) {
        $sql = "SELECT c.*, t.name as teacher_name, t.subject as teacher_subject,
                COUNT(DISTINCT sc.student_id) as student_count
                FROM {$this->table} c 
                LEFT JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                LEFT JOIN " . TABLE_STUDENT_COURSE . " sc ON c.id = sc.course_id AND sc.status = ?
                WHERE c.status = ?";
        
        $params = [STATUS_ACTIVE, STATUS_ACTIVE];
        $types = 'ii';
        
        if (!empty($conditions)) {
            foreach ($conditions as $field => $value) {
                $sql .= " AND c.{$field} = ?";
                $params[] = $value;
                $types .= is_int($value) ? 'i' : 's';
            }
        }
        
        $sql .= " GROUP BY c.id";
        
        if ($order_by) {
            $sql .= " ORDER BY {$order_by}";
        }
        
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        
        return $courses;
    }
    
    /**
     * 获取课程的学生列表
     * @param int $course_id
     * @return array
     */
    public function getStudents($course_id) {
        $sql = "SELECT s.*, u.username, u.email, sc.join_date
                FROM " . TABLE_STUDENT_COURSE . " sc
                JOIN " . TABLE_STUDENT . " s ON sc.student_id = s.id
                JOIN user u ON s.user_id = u.userid
                WHERE sc.course_id = ? AND sc.status = ? AND s.status = ?
                ORDER BY sc.join_date DESC";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('iii', $course_id, $active, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $students = [];
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
        
        return $students;
    }
    
    /**
     * 获取课程的课程安排
     * @param int $course_id
     * @return array
     */
    public function getSchedules($course_id) {
        $sql = "SELECT cs.*, cr.name as classroom_name, cr.location as classroom_location
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                JOIN " . TABLE_CLASSROOM . " cr ON cs.classroom_id = cr.id
                WHERE cs.course_id = ? AND cs.status = ?
                ORDER BY cs.day_of_week, cs.start_time";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('ii', $course_id, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $schedules = [];
        while ($row = $result->fetch_assoc()) {
            $schedules[] = $row;
        }
        
        return $schedules;
    }
    
    /**
     * 检查课程时间冲突
     * @param int $teacher_id
     * @param int $day_of_week
     * @param string $start_time
     * @param string $end_time
     * @param int $exclude_course_id 排除的课程ID（用于编辑时）
     * @return bool
     */
    public function checkTimeConflict($teacher_id, $day_of_week, $start_time, $end_time, $exclude_course_id = 0) {
        $sql = "SELECT COUNT(*) as count
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                WHERE c.teacher_id = ? AND cs.day_of_week = ? AND cs.status = ? AND c.status = ?
                AND ((cs.start_time <= ? AND cs.end_time > ?) OR (cs.start_time < ? AND cs.end_time >= ?))";
        
        $params = [$teacher_id, $day_of_week, STATUS_ACTIVE, STATUS_ACTIVE, $start_time, $start_time, $end_time, $end_time];
        $types = 'iiissss';
        
        if ($exclude_course_id > 0) {
            $sql .= " AND c.id != ?";
            $params[] = $exclude_course_id;
            $types .= 'i';
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
    
    /**
     * 检查教室时间冲突
     * @param int $classroom_id
     * @param int $day_of_week
     * @param string $start_time
     * @param string $end_time
     * @param int $exclude_schedule_id 排除的课程安排ID
     * @return bool
     */
    public function checkClassroomConflict($classroom_id, $day_of_week, $start_time, $end_time, $exclude_schedule_id = 0) {
        $sql = "SELECT COUNT(*) as count
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                WHERE cs.classroom_id = ? AND cs.day_of_week = ? AND cs.status = ?
                AND ((cs.start_time <= ? AND cs.end_time > ?) OR (cs.start_time < ? AND cs.end_time >= ?))";
        
        $params = [$classroom_id, $day_of_week, STATUS_ACTIVE, $start_time, $start_time, $end_time, $end_time];
        $types = 'iissss';
        
        if ($exclude_schedule_id > 0) {
            $sql .= " AND cs.id != ?";
            $params[] = $exclude_schedule_id;
            $types .= 'i';
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
    
    /**
     * 创建课程安排
     * @param int $course_id
     * @param int $classroom_id
     * @param int $day_of_week
     * @param string $start_time
     * @param string $end_time
     * @param string $start_date
     * @param string $end_date
     * @return int|false
     */
    public function createSchedule($course_id, $classroom_id, $day_of_week, $start_time, $end_time, $start_date, $end_date = null) {
        $sql = "INSERT INTO " . TABLE_COURSE_SCHEDULE . " 
                (course_id, classroom_id, day_of_week, start_time, end_time, start_date, end_date, create_time) 
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param('iiissss', $course_id, $classroom_id, $day_of_week, $start_time, $end_time, $start_date, $end_date);
        
        if ($stmt->execute()) {
            return $this->conn->insert_id;
        }
        
        return false;
    }
    
    /**
     * 获取可选的课程列表（学生选课用）
     * @param int $student_id
     * @return array
     */
    public function getAvailableCourses($student_id) {
        $sql = "SELECT c.*, t.name as teacher_name, t.subject as teacher_subject,
                COUNT(DISTINCT sc.student_id) as current_students,
                CASE WHEN sc2.id IS NOT NULL THEN 1 ELSE 0 END as is_enrolled
                FROM {$this->table} c 
                LEFT JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                LEFT JOIN " . TABLE_STUDENT_COURSE . " sc ON c.id = sc.course_id AND sc.status = ?
                LEFT JOIN " . TABLE_STUDENT_COURSE . " sc2 ON c.id = sc2.course_id AND sc2.student_id = ? AND sc2.status = ?
                WHERE c.status = ?
                GROUP BY c.id
                HAVING current_students < c.max_students OR is_enrolled = 1
                ORDER BY c.create_time DESC";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('iiii', $active, $student_id, $active, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $courses = [];
        while ($row = $result->fetch_assoc()) {
            $courses[] = $row;
        }
        
        return $courses;
    }
    
    /**
     * 获取课程统计信息
     * @param int $course_id
     * @return array
     */
    public function getStatistics($course_id) {
        // 学生数量
        $student_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_STUDENT_COURSE . " WHERE course_id = ? AND status = ?",
            [$course_id, STATUS_ACTIVE]
        )[0]['count'];
        
        // 课程安排数量
        $schedule_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_COURSE_SCHEDULE . " WHERE course_id = ? AND status = ?",
            [$course_id, STATUS_ACTIVE]
        )[0]['count'];
        
        // 请假申请数量
        $leave_count = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_LEAVE_REQUEST . " lr
             JOIN " . TABLE_COURSE_SCHEDULE . " cs ON lr.course_schedule_id = cs.id
             WHERE cs.course_id = ?",
            [$course_id]
        )[0]['count'];
        
        // 待审批请假数量
        $pending_leave_count = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_LEAVE_REQUEST . " lr
             JOIN " . TABLE_COURSE_SCHEDULE . " cs ON lr.course_schedule_id = cs.id
             WHERE cs.course_id = ? AND lr.status = ?",
            [$course_id, LEAVE_STATUS_PENDING]
        )[0]['count'];
        
        return [
            'student_count' => $student_count,
            'schedule_count' => $schedule_count,
            'leave_count' => $leave_count,
            'pending_leave_count' => $pending_leave_count
        ];
    }
    
    /**
     * 删除课程安排
     * @param int $schedule_id
     * @return bool
     */
    public function deleteSchedule($schedule_id) {
        $sql = "UPDATE " . TABLE_COURSE_SCHEDULE . " SET status = ?, update_time = NOW() WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $inactive = STATUS_INACTIVE;
        $stmt->bind_param('ii', $inactive, $schedule_id);
        return $stmt->execute();
    }
    
    /**
     * 获取课程的详细信息（包含教师和统计信息）
     * @param int $course_id
     * @return array|null
     */
    public function getDetailById($course_id) {
        $sql = "SELECT c.*, t.name as teacher_name, t.subject as teacher_subject, t.phone as teacher_phone,
                COUNT(DISTINCT sc.student_id) as student_count,
                COUNT(DISTINCT cs.id) as schedule_count
                FROM {$this->table} c 
                LEFT JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                LEFT JOIN " . TABLE_STUDENT_COURSE . " sc ON c.id = sc.course_id AND sc.status = ?
                LEFT JOIN " . TABLE_COURSE_SCHEDULE . " cs ON c.id = cs.course_id AND cs.status = ?
                WHERE c.id = ? AND c.status = ?
                GROUP BY c.id";
        
        $stmt = $this->conn->prepare($sql);
        $active = STATUS_ACTIVE;
        $stmt->bind_param('iiii', $active, $active, $course_id, $active);
        $stmt->execute();
        $result = $stmt->get_result();
        
        return $result->fetch_assoc();
    }
}
