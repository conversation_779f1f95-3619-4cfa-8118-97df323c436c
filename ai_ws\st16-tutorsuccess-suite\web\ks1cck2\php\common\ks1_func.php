<?php

define( 'BR' , '<br>'.PHP_EOL );

define('WEB_ROOT_PATH', config('WEB_ROOT_PATH'));

function ks1echo($str){
    echo str_ireplace(APP_ROOT,'**',$str).BR;
}


// 定义一个函数来获取缓存的 HTML 或从数据库生成新的 HTML
function getCachedSelectOptions($type1, $cacheDuration = 43200) {
    $type = strtolower(trim($type1));
    // 定义缓存文件路径
    $cacheFilePath = CACHE_ROOT . $type . '.html';
    
    // 检查缓存文件是否存在
    if (file_exists($cacheFilePath)) {
        // 如果传入的有效期时长小于等于0，则不检查缓存文件的过期时间
        if ($cacheDuration > 0) {
            // 检查缓存文件是否存在且未过期
            if (time() - filemtime($cacheFilePath) < $cacheDuration) {
                // 读取缓存文件内容
                return file_get_contents($cacheFilePath);
            }
        }else{
            return file_get_contents($cacheFilePath);
        }
    }
    
    // 从数据库获取数据
    switch ($type) {
        case 'category':
            $options = getCategoriesFromDb();
            $options_idx = 1;
            $placeholder = '分类';
            break;

        case 'venues':
            $options = getVenuesFromDb();
            $options_idx = 2;
            $placeholder = '场所';
            break;

        case 'roles':
            $options = getRolesFromDb();
            $options_idx = 3;
            $placeholder = '角色';
            break;
            
        default:
            die(__METHOD__ . '(' . $type1 . ') error! ');
            break;
    }
    
    // 生成 HTML 下拉列表
    $html = generateSelectHtml($options, $type, $options_idx, ' ' . $placeholder);
    
    // 将 HTML 内容写入缓存文件
    file_put_contents($cacheFilePath, $html);
    
    // 返回新生成的 HTML
    return $html;
}

// 从数据库获取活动类别
function getCategoriesFromDb() {
    //$tmp_sql = 'SELECT `name` FROM `ks1_category`';
    //$sql_ret = get_data($tmp_sql);
    //return $sql_ret;
    return get_category_list();
}

// 从数据库获取活动场地
function getVenuesFromDb() {
    /*
    $tmp_sql = 'SELECT `name` FROM `ks1_venues`';
    $sql_ret = get_data($tmp_sql);
    return $sql_ret;
    */
    return get_venues_list();
}

// 从数据库获取角色列表
function getRolesFromDb() {
    $tmp_sql = 'SELECT `role_name` as name, `permission_id` as id  FROM `role_permissions`';
    $sql_ret = get_data($tmp_sql);
    return $sql_ret;
}

// 生成下拉列表的 HTML

function generateSelectHtml($options, $fieldName, $index, $placeholder='',$groupSize = 9, $searchThreshold = 9) {
    $html = '';

    if($options && is_array($options)){
        //pass
    }else{
        echo __METHOD__.'() err!';exit;
    }

    // 只有在选项超过搜索阈值时才显示搜索框
    if (count($options) > $searchThreshold) {
        $html .= "<input type='text' id='search-{$fieldName}-{$index}' onkeyup=\"filterOptions(this, '{$index}')\" placeholder='筛选{$fieldName}{$placeholder}'>\n";
    }

    // 创建临时的<select>元素，每个分组一个
    $groupCount = ceil(count($options) / $groupSize);
    for ($i = 0; $i < $groupCount; $i++) {
        $groupName = "{$fieldName}-temp-{$i}-{$index}";
        $html .= "<select name='{$groupName}' id='{$groupName}' class='temp-select-{$index}' style='display:xx;'>\n";
        $html .= "<option value=''>请选择</option>\n";

        for ($j = $i * $groupSize; $j < ($i + 1) * $groupSize && $j < count($options); $j++) {
            $option = $options[$j];
            $html .= "<option value='{$option['name']}'>{$option['name']}</option>\n";
        }
        $html .= "</select>\n";
    }

    // 创建一个隐藏的<input>元素来存储最终选中的内容
    $html .= "<input type='text' name='{$fieldName}' id='txt-{$fieldName}-{$index}' readonly placeholder='当前选中的{$fieldName}{$placeholder}'>\n";

    // JavaScript代码，用于更新隐藏的<input>元素的值
    $html .= "<script>
        // 确保不会重复添加updateHiddenInput函数
        if (typeof updateHiddenInput === 'undefined') {
            function updateHiddenInput(fieldName, index) {
                var tempSelects = document.getElementsByClassName('temp-select-' + index);
                for (var i = 0; i < tempSelects.length; i++) {
                    tempSelects[i].onchange = function() {
                        var hiddenInput = document.getElementById('txt-' + fieldName + '-' + index);
                        hiddenInput.value = this.value;
                    };
                }
            }
        }

        // 初始化updateHiddenInput函数
        updateHiddenInput('{$fieldName}', '{$index}');
    </script>\n";

    return $html;
}