.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    z-index: 9999;
}

.overlay img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 100%;
    max-height: 100%;
}

.button_icon-button {
    /* background-color: var(--white);//导致放大缩小出现选中背景 */
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    cursor: pointer;
    transition: all .3s ease;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    outline: none;
    border: none;
    color: var(--black)
}

.button_icon-button[disabled] {
    cursor: not-allowed;
    opacity: .5
}

.button_icon-button.button_primary {
    background-color: var(--primary);
    color: #fff
}

.button_icon-button.button_primary path {
    fill: #fff !important
}

.button_shadow {
    box-shadow: var(--card-shadow)
}

.button_border {
    border: var(--border-in-light)
}

.button_icon-button:hover {
    border-color: var(--primary)
}

.button_icon-button-icon {
    width: 16px;
    height: 16px;
    display: flex;
    justify-content: center;
    align-items: center
}

@media only screen and (max-width:600px) {
    .button_icon-button {
        padding: 16px
    }
}

.button_icon-button-text {
    margin-left: 5px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.home_container {
    border: var(--border-in-light);
    border-radius: 20px;
    box-shadow: var(--shadow);
    color: var(--black);
    background-color: var(--white);
    min-width: 600px;
    min-height: 480px;
    max-width: 1200px;
    display: flex;
    overflow: hidden;
    box-sizing: border-box;
    width: var(--window-width);
    height: var(--window-height)
}

@media only screen and (min-width:600px) {
    .home_tight-container {
        --window-width: 100vw;
        --window-height: var(--full-height);
        --window-content-width: calc(100% - var(--sidebar-width));
        border-radius: 20px;
        box-shadow: var(--shadow);
        color: var(--black);
        background-color: var(--white);
        min-width: 600px;
        min-height: 480px;
        max-width: 1200px;
        display: flex;
        overflow: hidden;
        box-sizing: border-box;
        width: var(--window-width);
        height: var(--window-height);
        max-width: 100vw;
        max-height: var(--full-height);
        border-radius: 0;
        border: 0
    }
}

/* 最大最下按钮消失 */
@media screen and (max-width: 600px) {
    .max_min {
        display: none;
    }
}


.home_sidebar {
    top: 0;
    width: var(--sidebar-width);
    box-sizing: border-box;
    padding: 20px;
    background-color: var(--second);
    display: flex;
    flex-direction: column;
    box-shadow: inset -2px 0 2px 0 rgba(0, 0, 0, .05);
    position: relative;
    transition: width .05s ease
}

.home_sidebar .home_sidebar-header-bar {
    display: flex;
    margin-bottom: 20px
}

.home_sidebar .home_sidebar-header-bar .home_sidebar-bar-button {
    flex-grow: 1
}

.home_sidebar .home_sidebar-header-bar .home_sidebar-bar-button:not(:last-child) {
    margin-right: 10px
}

.home_window-content {
    width: var(--window-content-width);
    height: 100%;
    display: flex;
    flex-direction: column
}

.home_mobile {
    display: none
}

@media only screen and (max-width:600px) {
    .home_container {
        min-height: unset;
        max-height: unset;
        min-width: unset;
        border: 0;
        border-radius: 0
    }

    .home_sidebar {
        position: absolute;
        left: -100%;
        z-index: 1000;
        height: var(--full-height);
        transition: all .3s ease;
        box-shadow: none
    }

    .home_sidebar-show {
        left: 0
    }

    .home_mobile {
        display: block
    }
}

.home_sidebar-header {
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px
}

.home_sidebar-logo {
    position: absolute;
    right: 0;
    top: 10px
}

.home_sidebar-title {
    font-size: 20px;
    font-weight: 700;
}

.home_sidebar-sub-title {
    font-size: 12px;
    font-weight: 400px;
}

.home_sidebar-body {
    flex: 1 1;
    overflow: auto;
    overflow-x: hidden
}

.home_chat-item {
    padding: 10px 14px;
    background-color: var(--white);
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: var(--card-shadow);
    transition: background-color .3s ease;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
    border: 2px solid transparent;
    position: relative
}

.home_chat-item:hover {
    background-color: var(--hover-color)
}

.home_chat-item-selected {
    border-color: var(--primary)
}

.home_chat-item-title {
    font-size: 14px;
    font-weight: bolder;
    display: block;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.home_chat-item-delete {
    position: absolute;
    top: 10px;
    right: 10px;
    transition: all .3s ease;
    opacity: .5;
    cursor: pointer
}

.home_chat-item-delete:hover {
    opacity: 1
}

.home_chat-item-info {
    display: flex;
    justify-content: space-between;
    color: #a6a6a6;
    font-size: 12px;
    margin-top: 8px;
}

.home_chat-item-count,
.home_chat-item-date {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.home_sidebar-tail {
    display: flex;
    justify-content: space-between;
    padding-top: 20px
}

.home_chat {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%
}

.home_chat-body {
    flex: 1 1;
    overflow: auto;
    padding: 20px 20px 40px;
    position: relative;
    overscroll-behavior: none
}

.home_chat-message {
    display: flex;
    flex-direction: row
}

.home_chat-message-user {
    display: flex;
    flex-direction: row-reverse
}

.home_chat-message-container {
    max-width: var(--message-max-width);
    display: flex;
    flex-direction: column;
    align-items: flex-start
}

.home_chat-message-container:hover .home_chat-message-top-actions {
    opacity: 1;
    right: 10px;
    pointer-events: all
}

.home_chat-message-user>.home_chat-message-container {
    align-items: flex-end
}

.home_chat-message-status {
    font-size: 12px;
    color: #aaa;
    line-height: 1.5;
    margin-top: 5px
}

.home_chat-message-item {
    box-sizing: border-box;
    max-width: 100%;
    margin-top: 30px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, .05);
    padding: 10px;
    font-size: 14px;
    -webkit-user-select: text;
    -moz-user-select: text;
    user-select: text;
    word-break: break-word;
    border: var(--border-in-light);
    position: relative
}

.home_chat-message-top-actions {
    font-size: 12px;
    position: absolute;
    right: 20px;
    top: -26px;
    left: 100px;
    transition: all .3s ease;
    opacity: 0;
    pointer-events: none;
    display: flex;
    flex-direction: row-reverse
}

.home_chat-message-top-actions .home_chat-message-top-action {
    opacity: .5;
    color: var(--black);
    white-space: nowrap;
    cursor: pointer
}

.home_chat-message-top-actions .home_chat-message-top-action:hover {
    opacity: 1
}

.home_chat-message-top-actions .home_chat-message-top-action:not(:first-child) {
    margin-right: 10px
}

.home_chat-message-user>.home_chat-message-container>.home_chat-message-item {
    background-color: var(--second)
}

.home_chat-message-actions {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;
    padding-top: 5px;
    box-sizing: border-box;
    font-size: 12px
}

.home_chat-message-action-date {
    color: #aaa
}

.home_chat-input-panel {
    position: relative;
    width: 100%;
    padding: 10px 20px 20px;
    box-sizing: border-box;
    flex-direction: column;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    border-top: var(--border-in-light);
    box-shadow: var(--card-shadow)
}

.home_prompt-hints {
    min-height: 20px;
    width: 100%;
    max-height: 50vh;
    overflow: auto;
    /* display: flex; */
    flex-direction: column-reverse;
    background-color: var(--white);
    /* border: var(--border-in-light); */
    border-radius: 10px;
    margin-bottom: 10px;
    /* box-shadow: var(--shadow) */
}

.home_prompt-hints_div {
    min-height: 20px;
    width: 48%;
    /* max-height: 50vh; */
    overflow: auto;
    /* display: flex; */
    float: left;
    margin: 0px 20px;
    flex-direction: column-reverse;
    background-color: var(--white);
    border: var(--border-in-light);
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: var(--shadow)
}

.home_prompt-hints_div2 {
    min-height: 20px;
    width: 100%;
    /* max-height: 50vh; */
    overflow: auto;
    /* display: flex; */
    float: left;
    margin: 0px 20px;
    flex-direction: column-reverse;
    background-color: var(--white);
    border: var(--border-in-light);
    border-radius: 10px;
    margin-bottom: 10px;
    box-shadow: var(--shadow)
}

.home_prompt-hints .home_prompt-hint {
    color: var(--black);
    padding: 6px 10px;
    cursor: pointer;
    transition: all .3s ease;
    border: 1px solid transparent;
    margin: 4px;
    border-radius: 8px
}

.home_prompt-hints .home_prompt-hint:not(:last-child) {
    margin-top: 0
}

.home_prompt-hints .home_prompt-hint .home_hint-title {
    font-size: 12px;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home_prompt-hints .home_prompt-hint .home_hint-content {
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home_prompt-hints .home_prompt-hint:hover {
    border-color: var(--primary)
}

.home_chat-input-panel-inner {
    display: flex;
    flex: 1 1
}

.home_chat-input {
    height: 100%;
    width: 100%;
    border-radius: 10px;
    border: var(--border-in-light);
    box-shadow: 0 -2px 5px rgba(0, 0, 0, .03);
    background-color: var(--white);
    color: var(--black);
    font-family: inherit;
    padding: 10px 110px 10px 14px;
    resize: none;
    outline: none
}

.home_chat-input:focus {
    border: 1px solid var(--primary)
}

.home_chat-input-send {
    background-color: var(--primary);
    color: #fff;
    position: absolute;
    right: 30px;
    bottom: 32px
}

@media only screen and (max-width:600px) {
    .home_chat-input {
        font-size: 16px
    }

    .home_chat-input-send {
        bottom: 30px
    }
}

.home_export-content {
    white-space: break-spaces;
    padding: 10px !important
}

.markdown-body li {
    list-style-type: decimal;
}

.ui-lib_modal-container {
    box-shadow: var(--card-shadow);
    background-color: var(--white);
    border-radius: 12px;
    width: 60vw;
    animation: ui-lib_slide-in .3s ease;
    --modal-padding: 20px
}

.ui-lib_modal-container .ui-lib_modal-header {
    padding: var(--modal-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: var(--border-in-light)
}

.ui-lib_modal-container .ui-lib_modal-header .ui-lib_modal-title {
    font-weight: bolder;
    font-size: 16px
}

.ui-lib_modal-container .ui-lib_modal-header .ui-lib_modal-close-btn {
    cursor: pointer
}

.ui-lib_modal-container .ui-lib_modal-header .ui-lib_modal-close-btn:hover {
    filter: brightness(1.2)
}

.ui-lib_modal-container .ui-lib_modal-content {
    max-height: 40vh;
    padding: var(--modal-padding);
    overflow: auto
}

.ui-lib_modal-container .ui-lib_modal-footer {
    padding: var(--modal-padding);
    display: flex;
    justify-content: flex-end;
    border-top: var(--border-in-light);
    box-shadow: var(--shadow)
}

.ui-lib_modal-container .ui-lib_modal-footer .ui-lib_modal-actions {
    display: flex;
    align-items: center
}

.ui-lib_modal-container .ui-lib_modal-footer .ui-lib_modal-actions .ui-lib_modal-action:not(:last-child) {
    margin-right: 20px
}

@media screen and (max-width:600px) {
    .ui-lib_modal-container {
        width: 100vw;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0
    }

    .ui-lib_modal-container .ui-lib_modal-content {
        max-height: 50vh
    }
}

/* 移动端tab切换 */
.tabs {
    position: relative;
    height: 300px;
    max-height: 50vh;
}

.tab-pane {
    display: inline-block;
}

.tabs input[type='radio'] {
    position: absolute;
    clip: rect(0, 0, 0, 0)
}

.tab-item {
    display: block;
    height: 34px;
    line-height: 34px;
    cursor: pointer;
    padding: 0 10px
}

.tabs input[type='radio']:checked+.tab-item {
    color: var(--black);
    font-size: larger;
    border: 1px solid transparent;
    border-radius: 10px;
    border-color: var(--primary);
}

.tab-content {
    position: absolute;
    left: 0;
    height: 85%;
    bottom: 0;
    right: 0;
    background: var(--white);
    overflow: auto;
}

.tabs input[type='radio']:checked+.tab-item+.tab-content {
    z-index: 1
}