<?php
//无论客户端是否关闭浏览器，下面的代码都将得到执行。
ignore_user_abort(true);
set_time_limit(0);
ini_set("max_execution_time", "180"); //避免数据量过大，导出不全的情况出现。

require_once('check_admin.php');
require_once('mysqlconn.php');

$filename = date("Y-m-d") . "_chatgpt.sql";
$i = 0;
$crlf = "\r\n";
header("Content-disposition:filename=" . $filename); //所保存的文件名
header("Content-type:application/octetstream");
header("Pragma:no-cache");
header("Expires:0");
print "-- filename=" . $filename;
print $crlf;
$result = $conn->query("SHOW TABLES");
while ($row = $result->fetch()) {
	$table = $row[0];
	print $crlf;
	echo get_table_structure($table, $crlf) . ";$crlf$crlf";
	echo get_table_content($table, $crlf);
	$i++;
}

function get_table_structure($table, $crlf)
{
	global $drop, $conn;
	$schema_create = "";
	if (!empty($drop)) {
		$schema_create .= "DROP TABLE IF EXISTS `$table`;$crlf";
	}
	$result = $conn->query("SHOW CREATE TABLE $table");
	$row = $result->fetch();
	$schema_create   .= $crlf . "-- " . $row[0] . $crlf;
	$schema_create   .= $row[1] . $crlf;
	return $schema_create;
}


//获得表内容
function   get_table_content($table, $crlf)
{
	global $conn;
	$schema_create = "";
	$temp = "";
	$result = $conn->query("SELECT * FROM $table");
	$i = 0;
	while ($row = $result->fetch()) {
		$schema_insert = "INSERT INTO `$table` VALUES (";
		for ($j = 0; $j < mysqli_num_fields($result); $j++) {
			if (!isset($row[$j]))
				$schema_insert .= " NULL,";
			elseif ($row[$j] != "")
				$schema_insert .= " '" . addslashes($row[$j]) . "',";
			else
				$schema_insert .= " '',";
		}
		$schema_insert = substr($schema_insert, 0, strlen($schema_insert) - 1);
		$schema_insert = str_replace(",$", "", $schema_insert);
		$schema_insert .= ");$crlf";
		$temp = $temp . $schema_insert;
		$i++;
	}
	return $temp;
}
