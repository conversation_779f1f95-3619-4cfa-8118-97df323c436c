<?php
/**
 * 教师控制器
 * 修订日期：2025-01-22
 */

namespace app\controller;

require_once dirname(__DIR__) . '/model/Database.php';
require_once dirname(__DIR__) . '/model/UserDao.php';
require_once dirname(__DIR__) . '/model/CourseDao.php';
require_once dirname(__DIR__) . '/model/LeaveDao.php';

use app\model\Database;
use app\model\UserDao;
use app\model\CourseDao;
use app\model\LeaveDao;

class Teacher
{
    /**
     * 教师主页
     */
    public function index()
    {
        // 检查是否已登录
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            header('Location: ../login.php');
            exit;
        }

        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        $user_roles = get_user_roles($user_id);

        // 检查是否有教师权限
        if (!in_array(ROLE_TEACHER, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
            header('Location: ../index.php');
            exit;
        }

        // 获取教师信息
        $teacher_info = null;
        if (in_array(ROLE_TEACHER, $user_roles)) {
            // 暂时使用模拟数据，后续连接数据库时启用
            // $teacher_model = new TeacherModel();
            // $teacher_info = $teacher_model->getByUserId($user_id);
            $teacher_info = ['name' => $current_user['name']];
        }

        // 获取统计数据
        $stats = $this->getTeacherStats($user_id);
        
        // 获取今日课程
        $today_schedule = $this->getTodaySchedule($user_id);
        
        // 获取待审批请假
        $pending_leaves = $this->getPendingLeaves($user_id);

        // 渲染视图
        include __DIR__ . '/../view/teacher/index.php';
    }

    /**
     * 教师课表
     */
    public function schedule()
    {
        // 检查权限
        $this->checkTeacherAuth();
        
        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        
        // 获取课表数据
        $schedule_data = $this->getScheduleData($user_id);
        
        // 渲染视图
        include __DIR__ . '/../view/teacher/schedule.php';
    }

    /**
     * 请假审批
     */
    public function leave()
    {
        // 检查权限
        $this->checkTeacherAuth();
        
        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        
        // 获取过滤条件
        $status_filter = $_GET['status'] ?? 'all';
        $priority_filter = $_GET['priority'] ?? 'all';
        
        // 获取请假申请列表
        $leave_requests = $this->getLeaveRequests($user_id, $status_filter, $priority_filter);
        
        // 渲染视图
        include '../application/view/teacher/leave.php';
    }

    /**
     * 处理请假审批
     */
    public function approveLeave()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['code' => 400, 'msg' => '请求方法错误']);
        }
        
        $this->checkTeacherAuth();
        
        $leave_id = $_POST['leave_id'] ?? 0;
        $action = $_POST['action'] ?? ''; // approve 或 reject
        $comment = $_POST['comment'] ?? '';
        
        if (!$leave_id || !in_array($action, ['approve', 'reject'])) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        
        $leave_model = new LeaveRequestModel();
        $status = ($action === 'approve') ? LEAVE_STATUS_APPROVED : LEAVE_STATUS_REJECTED;
        
        $result = $leave_model->updateStatus($leave_id, $status, $user_id, $comment);
        
        if ($result) {
            $this->jsonResponse(['code' => 200, 'msg' => '操作成功']);
        } else {
            $this->jsonResponse(['code' => 500, 'msg' => '操作失败']);
        }
    }

    /**
     * 开始上课
     */
    public function startClass()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['code' => 400, 'msg' => '请求方法错误']);
        }
        
        $this->checkTeacherAuth();
        
        $course_id = $_POST['course_id'] ?? 0;
        $schedule_id = $_POST['schedule_id'] ?? 0;
        
        if (!$course_id) {
            $this->jsonResponse(['code' => 400, 'msg' => '参数错误']);
        }
        
        // 这里可以添加开始上课的逻辑，比如记录上课时间等
        
        $this->jsonResponse(['code' => 200, 'msg' => '上课开始']);
    }

    /**
     * 检查教师权限
     */
    private function checkTeacherAuth()
    {
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            header('Location: ../login.php');
            exit;
        }

        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        $user_roles = get_user_roles($user_id);

        if (!in_array(ROLE_TEACHER, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
            header('Location: ../index.php');
            exit;
        }
    }

    /**
     * 获取教师统计数据
     */
    private function getTeacherStats($user_id)
    {
        // 这里应该从数据库获取真实数据
        // 暂时返回模拟数据
        return [
            'week_courses' => 8,
            'total_students' => 25,
            'pending_leaves' => 3,
            'attendance_rate' => 92
        ];
    }

    /**
     * 获取今日课程
     */
    private function getTodaySchedule($user_id)
    {
        // 这里应该从数据库获取真实数据
        // 暂时返回模拟数据
        return [
            [
                'time' => '09:00-10:30',
                'course' => '数学 - 代数基础',
                'students' => 12,
                'classroom' => '教室A101'
            ],
            [
                'time' => '14:00-15:30',
                'course' => '数学 - 几何基础',
                'students' => 8,
                'classroom' => '教室A102'
            ],
            [
                'time' => '16:00-17:30',
                'course' => '数学 - 综合练习',
                'students' => 15,
                'classroom' => '教室A101'
            ]
        ];
    }

    /**
     * 获取待审批请假
     */
    private function getPendingLeaves($user_id)
    {
        // 这里应该从数据库获取真实数据
        // 暂时返回模拟数据
        return [
            [
                'id' => 1,
                'student_name' => '张同学',
                'course_name' => '数学课',
                'course_time' => '2025-01-22 09:00-10:30',
                'apply_time' => '2025-01-21 10:30',
                'reason' => '身体不适，需要看医生',
                'phone' => '138****5678'
            ],
            [
                'id' => 2,
                'student_name' => '王同学',
                'course_name' => '数学课',
                'course_time' => '2025-01-22 14:00-15:30',
                'apply_time' => '2025-01-21 09:15',
                'reason' => '家庭事务，需要陪同家长办事',
                'phone' => '139****1234'
            ]
        ];
    }

    /**
     * 获取课表数据
     */
    private function getScheduleData($user_id)
    {
        // 这里应该从数据库获取真实数据
        // 暂时返回模拟数据
        return [
            1 => [ // 周一
                ['time' => '09:00-10:30', 'course' => '数学 - 代数基础', 'students' => 12, 'classroom' => 'A101'],
                ['time' => '14:00-15:30', 'course' => '数学 - 几何基础', 'students' => 8, 'classroom' => 'A102']
            ],
            2 => [ // 周二
                ['time' => '10:00-11:30', 'course' => '数学 - 函数基础', 'students' => 15, 'classroom' => 'A101'],
                ['time' => '16:00-17:30', 'course' => '数学 - 综合练习', 'students' => 10, 'classroom' => 'A103']
            ],
            3 => [ // 周三
                ['time' => '09:00-10:30', 'course' => '数学 - 代数进阶', 'students' => 12, 'classroom' => 'A101'],
                ['time' => '14:00-15:30', 'course' => '数学 - 应用题', 'students' => 9, 'classroom' => 'A102']
            ],
            4 => [ // 周四
                ['time' => '10:00-11:30', 'course' => '数学 - 复习课', 'students' => 18, 'classroom' => 'A101'],
                ['time' => '15:00-16:30', 'course' => '数学 - 测试课', 'students' => 18, 'classroom' => 'A101']
            ],
            5 => [ // 周五
                ['time' => '09:00-10:30', 'course' => '数学 - 总结课', 'students' => 20, 'classroom' => 'A101']
            ],
            6 => [], // 周六
            7 => []  // 周日
        ];
    }

    /**
     * 获取请假申请列表
     */
    private function getLeaveRequests($user_id, $status_filter, $priority_filter)
    {
        // 这里应该从数据库获取真实数据
        // 暂时返回模拟数据
        $leave_requests = [
            [
                'id' => 1,
                'student_name' => '张同学',
                'student_phone' => '138****5678',
                'course_name' => '数学 - 代数基础',
                'course_time' => '2025-01-22 09:00-10:30',
                'classroom' => '教室A101',
                'apply_time' => '2025-01-21 10:30',
                'start_date' => '2025-01-22',
                'end_date' => '2025-01-22',
                'reason' => '身体不适，需要看医生',
                'status' => 'pending',
                'priority' => 'normal'
            ],
            [
                'id' => 2,
                'student_name' => '王同学',
                'student_phone' => '139****1234',
                'course_name' => '数学 - 几何基础',
                'course_time' => '2025-01-22 14:00-15:30',
                'classroom' => '教室A102',
                'apply_time' => '2025-01-21 09:15',
                'start_date' => '2025-01-22',
                'end_date' => '2025-01-22',
                'reason' => '家庭事务，需要陪同家长办事',
                'status' => 'pending',
                'priority' => 'normal'
            ]
        ];

        // 过滤数据
        return array_filter($leave_requests, function($request) use ($status_filter, $priority_filter) {
            $status_match = ($status_filter === 'all') || ($request['status'] === $status_filter);
            $priority_match = ($priority_filter === 'all') || ($request['priority'] === $priority_filter);
            return $status_match && $priority_match;
        });
    }

    /**
     * JSON响应
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
