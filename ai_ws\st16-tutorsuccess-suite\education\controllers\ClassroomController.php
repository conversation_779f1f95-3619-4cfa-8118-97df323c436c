<?php
/**
 * 教室控制器
 * 处理教室相关的请求
 */
class ClassroomController extends Controller {
    /**
     * 教室模型实例
     * @var ClassroomModel
     */
    private $classroomModel;
    
    /**
     * 课程模型实例
     * @var CourseModel
     */
    private $courseModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        $this->classroomModel = new ClassroomModel();
        $this->courseModel = new CourseModel();
        
        // 检查用户是否登录，除了特定方法外
        $noAuthMethods = ['index', 'view'];
        if (!in_array($this->getMethod(), $noAuthMethods)) {
            $this->checkLogin();
        }
    }
    
    /**
     * 教室列表页
     */
    public function index() {
        // 获取分页参数
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // 获取教室列表和总数
        $classrooms = $this->classroomModel->getClassrooms($page, $perPage);
        $totalClassrooms = $this->classroomModel->getClassroomCount();
        
        // 计算总页数
        $totalPages = ceil($totalClassrooms / $perPage);
        
        // 设置视图变量
        $this->setVar('classrooms', $classrooms);
        $this->setVar('currentPage', $page);
        $this->setVar('totalPages', $totalPages);
        $this->setVar('totalClassrooms', $totalClassrooms);
        
        // 渲染视图
        $this->render('classroom/index');
    }
    
    /**
     * 查看教室详情
     */
    public function view() {
        // 获取教室ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('classroom/index');
        }
        
        // 获取教室信息
        $classroom = $this->classroomModel->getClassroomById($id);
        
        if (!$classroom) {
            $this->setVar('error', '教室不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取当前日期
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
        
        // 获取教室在特定日期的课程
        $courses = $this->courseModel->getCoursesByClassroomAndDate($id, $date);
        
        // 设置视图变量
        $this->setVar('classroom', $classroom);
        $this->setVar('courses', $courses);
        $this->setVar('date', $date);
        
        // 渲染视图
        $this->render('classroom/view');
    }
    
    /**
     * 添加教室页面
     */
    public function add() {
        // 检查权限
        $this->checkPermission();
        
        // 渲染视图
        $this->render('classroom/add');
    }
    
    /**
     * 处理添加教室请求
     */
    public function create() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('classroom/add');
        }
        
        // 获取表单数据
        $name = $this->getPost('name');
        $location = $this->getPost('location');
        $capacity = (int)$this->getPost('capacity');
        $facilities = $this->getPost('facilities');
        
        // 验证数据
        if (empty($name) || empty($location) || empty($capacity)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'name' => $name,
            'location' => $location,
            'capacity' => $capacity,
            'facilities' => $facilities,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // 创建教室
        $classroomId = $this->classroomModel->createClassroom($data);
        
        if ($classroomId) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '教室创建成功', 'id' => $classroomId]);
            } else {
                redirect('classroom/view?id=' . $classroomId);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '教室创建失败，请重试']);
            } else {
                $this->setVar('error', '教室创建失败，请重试');
                $this->setVar('formData', $_POST);
                $this->add();
            }
        }
    }
    
    /**
     * 编辑教室页面
     */
    public function edit() {
        // 检查权限
        $this->checkPermission();
        
        // 获取教室ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('classroom/index');
        }
        
        // 获取教室信息
        $classroom = $this->classroomModel->getClassroomById($id);
        
        if (!$classroom) {
            $this->setVar('error', '教室不存在');
            $this->render('error/index');
            return;
        }
        
        // 设置视图变量
        $this->setVar('classroom', $classroom);
        
        // 渲染视图
        $this->render('classroom/edit');
    }
    
    /**
     * 处理更新教室请求
     */
    public function update() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('classroom/index');
        }
        
        // 获取教室ID
        $id = (int)$this->getPost('id');
        
        if (!$id) {
            redirect('classroom/index');
        }
        
        // 获取教室信息
        $classroom = $this->classroomModel->getClassroomById($id);
        
        if (!$classroom) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '教室不存在']);
            } else {
                $this->setVar('error', '教室不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 获取表单数据
        $name = $this->getPost('name');
        $location = $this->getPost('location');
        $capacity = (int)$this->getPost('capacity');
        $facilities = $this->getPost('facilities');
        
        // 验证数据
        if (empty($name) || empty($location) || empty($capacity)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'name' => $name,
            'location' => $location,
            'capacity' => $capacity,
            'facilities' => $facilities,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 更新教室
        $result = $this->classroomModel->updateClassroom($id, $data);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '教室更新成功']);
            } else {
                redirect('classroom/view?id=' . $id);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '教室更新失败，请重试']);
            } else {
                $this->setVar('error', '教室更新失败，请重试');
                $this->setVar('formData', $_POST);
                $this->edit();
            }
        }
    }
    
    /**
     * 删除教室
     */
    public function delete() {
        // 检查权限
        $this->checkPermission();
        
        // 获取教室ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的教室ID']);
            } else {
                redirect('classroom/index');
            }
            return;
        }
        
        // 获取教室信息
        $classroom = $this->classroomModel->getClassroomById($id);
        
        if (!$classroom) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '教室不存在']);
            } else {
                $this->setVar('error', '教室不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 检查教室是否有关联的课表
        $schedules = $this->classroomModel->getClassroomSchedules($id);
        if (count($schedules) > 0) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '该教室已被课程使用，无法删除']);
            } else {
                $this->setVar('error', '该教室已被课程使用，无法删除');
                $this->render('error/index');
            }
            return;
        }
        
        // 删除教室
        $result = $this->classroomModel->deleteClassroom($id);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '教室删除成功']);
            } else {
                redirect('classroom/index');
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '教室删除失败，请重试']);
            } else {
                $this->setVar('error', '教室删除失败，请重试');
                $this->render('error/index');
            }
        }
    }
    
    /**
     * 搜索教室
     */
    public function search() {
        // 获取搜索关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        if (empty($keyword)) {
            redirect('classroom/index');
        }
        
        // 获取分页参数
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // 搜索教室
        $classrooms = $this->classroomModel->searchClassrooms($keyword, $page, $perPage);
        $totalClassrooms = $this->classroomModel->getSearchCount($keyword);
        
        // 计算总页数
        $totalPages = ceil($totalClassrooms / $perPage);
        
        // 设置视图变量
        $this->setVar('classrooms', $classrooms);
        $this->setVar('keyword', $keyword);
        $this->setVar('currentPage', $page);
        $this->setVar('totalPages', $totalPages);
        $this->setVar('totalClassrooms', $totalClassrooms);
        
        // 渲染视图
        $this->render('classroom/search');
    }
    
    /**
     * 教室可用性检查
     */
    public function checkAvailability() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的请求方法']);
            } else {
                redirect('classroom/index');
            }
            return;
        }
        
        // 获取表单数据
        $classroomId = (int)$this->getPost('classroom_id');
        $dayOfWeek = (int)$this->getPost('day_of_week');
        $startTime = $this->getPost('start_time');
        $endTime = $this->getPost('end_time');
        $excludeScheduleId = (int)$this->getPost('exclude_schedule_id', 0);
        
        // 验证数据
        if (!$classroomId || !isset($dayOfWeek) || empty($startTime) || empty($endTime)) {
            jsonResponse(['status' => 'error', 'message' => '请提供所有必要的参数']);
            return;
        }
        
        // 检查教室是否存在
        $classroom = $this->classroomModel->getClassroomById($classroomId);
        if (!$classroom) {
            jsonResponse(['status' => 'error', 'message' => '教室不存在']);
            return;
        }
        
        // 检查教室在指定时间是否可用
        $isAvailable = $this->classroomModel->isClassroomAvailable(
            $classroomId, 
            $dayOfWeek, 
            $startTime, 
            $endTime, 
            $excludeScheduleId
        );
        
        if ($isAvailable) {
            jsonResponse(['status' => 'success', 'available' => true, 'message' => '教室在指定时间可用']);
        } else {
            // 获取冲突的课程
            $conflicts = $this->classroomModel->getClassroomConflicts(
                $classroomId, 
                $dayOfWeek, 
                $startTime, 
                $endTime, 
                $excludeScheduleId
            );
            
            jsonResponse([
                'status' => 'success', 
                'available' => false, 
                'message' => '教室在指定时间不可用', 
                'conflicts' => $conflicts
            ]);
        }
    }
    
    /**
     * 检查用户权限
     * 只有管理员可以管理教室
     */
    private function checkPermission() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] != 'admin') {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '您没有权限执行此操作']);
                exit;
            } else {
                $this->setVar('error', '您没有权限执行此操作');
                $this->render('error/index');
                exit;
            }
        }
    }
}