<?php
/**
 * 教培系统主页
 * 创建时间：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../_ks1.php';
require_once '../mysqlconn.php';
require_once 'init.php';

// 检查是否已登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: ../login.php');
    exit;
}

$current_user = get_current_login_user();
$user_id = $current_user['id'];
$user_roles = get_user_roles($user_id);
$primary_role = get_user_primary_role($user_id);

// 如果用户有明确的角色，直接跳转
if ($primary_role) {
    switch ($primary_role) {
        case ROLE_ADMIN:
            header('Location: views/admin/dashboard.php');
            break;
        case ROLE_TEACHER:
            header('Location: views/teacher/dashboard.php');
            break;
        case ROLE_STUDENT:
            header('Location: views/student/dashboard.php');
            break;
    }
    exit;
}

$page_title = '特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .main-container {
            padding: 50px 0;
        }
        .welcome-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
        }
        .role-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        .role-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        .role-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .role-desc {
            color: #666;
            margin-bottom: 20px;
        }
        .btn-role {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 10px 30px;
            color: white;
            text-decoration: none;
            transition: transform 0.3s;
        }
        .btn-role:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        .system-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container main-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="welcome-card">
                    <h1><i class="fa fa-graduation-cap"></i> 特靠谱教培系统</h1>
                    <p class="lead">欢迎，<?php echo safe_html($current_user['name'] ?? $current_user['email']); ?>！</p>
                    <p>请选择您的身份进入对应的系统：</p>
                </div>
                
                <div class="row">
                    <?php if (empty($user_roles)): ?>
                    <div class="col-md-12">
                        <div class="alert alert-warning text-center">
                            <h4><i class="fa fa-exclamation-triangle"></i> 暂无系统权限</h4>
                            <p>您的账户暂未分配教培系统权限，请联系管理员为您分配角色。</p>
                            <a href="../index.php" class="btn btn-primary">返回主系统</a>
                        </div>
                    </div>
                    <?php else: ?>
                    
                    <?php if (in_array(ROLE_ADMIN, $user_roles)): ?>
                    <div class="col-md-4">
                        <div class="role-card" onclick="location.href='views/admin/dashboard.php'">
                            <div class="role-icon">
                                <i class="fa fa-cog"></i>
                            </div>
                            <div class="role-title">管理员</div>
                            <div class="role-desc">系统管理、用户管理、课程管理</div>
                            <a href="views/admin/dashboard.php" class="btn btn-role">进入管理</a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (in_array(ROLE_TEACHER, $user_roles)): ?>
                    <div class="col-md-4">
                        <div class="role-card" onclick="location.href='views/teacher/dashboard.php'">
                            <div class="role-icon">
                                <i class="fa fa-chalkboard-teacher"></i>
                            </div>
                            <div class="role-title">教师</div>
                            <div class="role-desc">课表管理、请假审批、学生管理</div>
                            <a href="views/teacher/dashboard.php" class="btn btn-role">进入教学</a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (in_array(ROLE_STUDENT, $user_roles)): ?>
                    <div class="col-md-4">
                        <div class="role-card" onclick="location.href='views/student/dashboard.php'">
                            <div class="role-icon">
                                <i class="fa fa-user-graduate"></i>
                            </div>
                            <div class="role-title">学生</div>
                            <div class="role-desc">课表查看、请假申请、成绩查询</div>
                            <a href="views/student/dashboard.php" class="btn btn-role">进入学习</a>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php endif; ?>
                </div>
                
                <div class="system-info text-center">
                    <p><i class="fa fa-info-circle"></i> 系统版本：<?php echo EDU_VERSION; ?> | 当前学期：<?php echo get_current_semester(); ?></p>
                    <p>
                        <a href="../index.php" class="text-white"><i class="fa fa-home"></i> 返回主系统</a> | 
                        <a href="../login.php?action=logout" class="text-white"><i class="fa fa-sign-out"></i> 退出登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../bootstrap/js/bootstrap.bundle.min.js"></script>
</body>
</html>
