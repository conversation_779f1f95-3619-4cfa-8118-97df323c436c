<?php
require_once('check_admin.php');
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "addnew")) {
    require_once('mysqlconn.php');
    $result = $conn->get('officer','*',['username'=>$_REQUEST['username']]);
    /*
     * 判断有没有重复信息
     * 没有重复数据则写入数据
     * 写入成功则给出提示信息和密码
     * 写入失败输出错误信息
     * */
    if (empty($result)) {
        $conn->insert('officer',[
            'usertype'=>0,
            'username'=>$_REQUEST["username"],
            'password'=>md5(md5($_REQUEST["password"]) . "chatgpt@2023"),
            'realname'=>$_REQUEST["realname"],
            'tel'=>$_REQUEST["tel"],
            'email'=>$_REQUEST["email"],
            'memo'=>$_REQUEST["memo"],
            'registertime'=>date('Y-m-d H:i:s'),
        ]);
        if ($conn->error) die($conn->error);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('添加成功！');document.location.href='manageuser.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('用户名已存在！创建失败！');history.back();</script>";
    }
    exit;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>添加管理员</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">添加管理员</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" name=adduser onsubmit="return checkform();">
                            <input type=hidden name="action" value="addnew">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">登录ID：</label>

                                <div class="col-lg-4">

                                    <input type="text" value="admin" style="position: absolute;z-index: -1;" disabled autocomplete="off" /><!-- 这个username会被浏览器记住，我随便用个admin-->
                                    <input type="password" value=" " style="position: absolute;z-index: -1;" disabled autocomplete="off" />
                                    <input type="text" id="username" name="username" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">密码：</label>

                                <div class="col-lg-4">
                                    <input type="text" onfocus="this.value='';document.getElementById('password').value=''" onchange="document.getElementById('password').value=md5(md5(document.getElementById('fakepassword').value)+'Libra');this.value='******';" id="fakepassword" name="fakepassword" class="bg-focus form-control" autoComplete="off">
                                    <input type=hidden id=password name=password autoComplete="off">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">邮箱：</label>

                                <div class="col-lg-4">
                                    <input type="text" id="email" name="email" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <br />


                            <div class="form-group">
                                <label class="col-lg-4 control-label">姓名：</label>

                                <div class="col-lg-4">
                                    <input type="text" id="realname" name="realname" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">电话：</label>

                                <div class="col-lg-4">
                                    <input type="text" id="tel" name="tel" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group">
                                <label class="col-lg-4 control-label">备注：</label>

                                <div class="col-lg-4">
                                    <input type="text" id="memo" name="memo" class="bg-focus form-control">
                                </div>
                            </div>
                            <div align="center">
                                <span style="color:red;" id="realNameErrorMsg"></span>
                            </div>
                            <br />

                            <div class="form-group" align="center" style="margin-right:150px;margin-top:15px">
                                <div class="col-lg-6 col-lg-offset-3">
                                    <button type="button" onclick="if (checkform()){window.adduser.submit();}" class="btn btn-primary">确认添加</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function noauto(what) {
            if (what.value == "") {
                what.type = 'password';
            }
        }

        function inputdone(event, what) {
            var keyCode = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode;
            if (keyCode == 13) {
                what.type = "text";
            }
        }

        function checkform() {
            if ((window.adduser.username.value == "") || (window.adduser.password.value == "") || (window.adduser.realname.value == "")) {
                alert("请至少填写登陆ID、密码和姓名！");
                return false;
            } else {
                return true;
            }
        }
    </script>
</body>

</html>