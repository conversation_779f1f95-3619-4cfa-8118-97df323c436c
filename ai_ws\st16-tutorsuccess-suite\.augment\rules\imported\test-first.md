---
type: "always_apply"
---

# 测试优先原则

> Rule Type: `testing`

## 目录
- [基本原则](mdc:#基本原则)
- [测试流程](mdc:#测试流程)
- [测试脚本规范](mdc:#测试脚本规范)
- [网页测试规范](mdc:#网页测试规范)
- [测试用例模板](mdc:#测试用例模板)
- [自动化测试](mdc:#自动化测试)

## 基本原则

1. **测试优先开发** - 任务回复完成前，必须先尽可能测试
2. **全面测试覆盖** - 确保所有功能代码都有对应的测试用例
3. **测试驱动开发** - 先编写测试，再实现功能
4. **持续测试** - 在开发过程中持续运行测试，而不仅在开发完成后

## 测试流程

1. 为新功能编写测试脚本，明确预期结果
2. 实现功能代码
3. 运行测试并验证结果
4. 根据测试结果修改代码，
5. 使用MCP浏览器 模拟用户操作进行测试 ，自动修正发现的问题
6. 重复测试直到通过

## 注意事项

1. 终端PowerShell中不支持`&&`，多行指令需要分开写
2. 建议写成test开头的临时脚本执行测试
3. 测试数据应与生产数据分离，避免污染生产环境

## 版本信息

- 版本: 1.0
- 最后更新: 2025.6.14

