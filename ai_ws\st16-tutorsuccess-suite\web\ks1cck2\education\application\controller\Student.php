<?php
/**
 * 学生控制器
 * 修订日期：2025-08-22
 */

namespace app\controller;

// 暂时注释掉模型引用，使用模拟数据
// require_once __DIR__ . '/../model/Database.php';
// require_once __DIR__ . '/../model/UserDao.php';
// require_once __DIR__ . '/../model/CourseDao.php';
// require_once __DIR__ . '/../model/LeaveDao.php';

// use app\model\Database;
// use app\model\UserDao;
// use app\model\CourseDao;
// use app\model\LeaveDao;

class Student
{
    /**
     * 学生主页
     */
    public function index()
    {
        // 在测试环境下跳过权限检查
        if (!$this->isTestMode()) {
            // 检查权限
            $this->checkStudentAuth();
            $current_user = get_current_login_user();
            $user_id = $current_user['id'];
        } else {
            // 测试模式下使用模拟用户
            $current_user = ['id' => 1, 'name' => '张同学'];
            $user_id = 1;
        }

        // 获取学生信息（暂时注释掉，使用模拟数据）
        // $student_model = new StudentModel();
        // $student_info = $student_model->getByUserId($user_id);

        // 获取统计数据
        $stats = $this->getStudentStats($user_id);

        // 获取今日课程
        $today_schedule = $this->getTodaySchedule($user_id);

        // 获取最近请假记录
        $recent_leaves = $this->getRecentLeaves($user_id);

        // 渲染视图
        include __DIR__ . '/../view/student/index.php';
    }

    /**
     * 学生课表
     */
    public function schedule()
    {
        // 在测试环境下跳过权限检查
        if (!$this->isTestMode()) {
            // 检查权限
            $this->checkStudentAuth();
            $current_user = get_current_login_user();
            $user_id = $current_user['id'];
        } else {
            // 测试模式下使用模拟用户
            $current_user = ['id' => 1, 'name' => '张同学'];
            $user_id = 1;
        }

        // 获取课表数据
        $schedule_data = $this->getScheduleData($user_id);

        // 渲染视图
        include __DIR__ . '/../view/student/schedule.php';
    }

    /**
     * 请假申请
     */
    public function leave()
    {
        // 在测试环境下跳过权限检查
        if (!$this->isTestMode()) {
            // 检查权限
            $this->checkStudentAuth();
            $current_user = get_current_login_user();
            $user_id = $current_user['id'];
        } else {
            // 测试模式下使用模拟用户
            $current_user = ['id' => 1, 'name' => '张同学'];
            $user_id = 1;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理请假申请提交
            $this->submitLeaveRequest($user_id);
        } else {
            // 显示请假申请页面
            $leave_requests = $this->getLeaveRequests($user_id);
            $courses = $this->getStudentCourses($user_id);

            // 渲染视图
            include __DIR__ . '/../view/student/leave.php';
        }
    }

    /**
     * 检查是否为测试模式
     */
    private function isTestMode()
    {
        // 检查是否通过test.php访问
        return strpos($_SERVER['SCRIPT_NAME'], 'test.php') !== false;
    }

    /**
     * 检查学生权限
     */
    private function checkStudentAuth()
    {
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            header('Location: ../login.php');
            exit;
        }

        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        $user_roles = get_user_roles($user_id);

        if (!in_array(ROLE_STUDENT, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
            header('Location: ../index.php');
            exit;
        }
    }

    /**
     * 获取学生统计数据
     */
    private function getStudentStats($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            'total_courses' => 5,
            'this_week_classes' => 12,
            'attendance_rate' => 95,
            'pending_leaves' => 1
        ];
    }

    /**
     * 获取今日课程
     */
    private function getTodaySchedule($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'time' => '09:00-10:30',
                'course' => '数学 - 代数基础',
                'teacher' => '李老师',
                'classroom' => '教室A101'
            ],
            [
                'time' => '14:00-15:30',
                'course' => '英语 - 口语练习',
                'teacher' => '王老师',
                'classroom' => '教室B201'
            ]
        ];
    }

    /**
     * 获取最近请假记录
     */
    private function getRecentLeaves($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'id' => 1,
                'course_name' => '数学课',
                'leave_date' => '2025-01-20',
                'reason' => '身体不适',
                'status' => 'approved',
                'apply_time' => '2025-01-19 15:30'
            ]
        ];
    }

    /**
     * 获取课表数据
     */
    private function getScheduleData($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            1 => [ // 周一
                ['time' => '09:00-10:30', 'course' => '数学 - 代数基础', 'teacher' => '李老师', 'classroom' => 'A101'],
                ['time' => '14:00-15:30', 'course' => '英语 - 语法', 'teacher' => '王老师', 'classroom' => 'B201']
            ],
            2 => [ // 周二
                ['time' => '10:00-11:30', 'course' => '数学 - 几何', 'teacher' => '李老师', 'classroom' => 'A101'],
                ['time' => '16:00-17:30', 'course' => '英语 - 口语', 'teacher' => '王老师', 'classroom' => 'B201']
            ],
            3 => [ // 周三
                ['time' => '09:00-10:30', 'course' => '数学 - 应用题', 'teacher' => '李老师', 'classroom' => 'A101']
            ],
            4 => [ // 周四
                ['time' => '14:00-15:30', 'course' => '英语 - 写作', 'teacher' => '王老师', 'classroom' => 'B201']
            ],
            5 => [ // 周五
                ['time' => '09:00-10:30', 'course' => '数学 - 复习', 'teacher' => '李老师', 'classroom' => 'A101']
            ],
            6 => [], // 周六
            7 => []  // 周日
        ];
    }

    /**
     * 获取请假申请列表
     */
    private function getLeaveRequests($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'id' => 1,
                'course_name' => '数学课',
                'leave_date' => '2025-01-20',
                'reason' => '身体不适，需要看医生',
                'status' => 'approved',
                'apply_time' => '2025-01-19 15:30',
                'approved_by' => '李老师',
                'approved_at' => '2025-01-19 16:00',
                'comment' => '同意请假，注意身体健康'
            ],
            [
                'id' => 2,
                'course_name' => '英语课',
                'leave_date' => '2025-01-22',
                'reason' => '家庭事务',
                'status' => 'pending',
                'apply_time' => '2025-01-21 10:30'
            ]
        ];
    }

    /**
     * 获取学生课程列表
     */
    private function getStudentCourses($user_id)
    {
        // 这里应该从数据库获取真实数据
        return [
            ['id' => 1, 'name' => '数学 - 代数基础', 'teacher' => '李老师'],
            ['id' => 2, 'name' => '英语 - 综合课程', 'teacher' => '王老师']
        ];
    }

    /**
     * 提交请假申请
     */
    private function submitLeaveRequest($user_id)
    {
        $course_id = $_POST['course_id'] ?? 0;
        $leave_date = $_POST['leave_date'] ?? '';
        $leave_type = $_POST['leave_type'] ?? 2;
        $reason = $_POST['reason'] ?? '';

        if (!$course_id || !$leave_date || !$reason) {
            $this->jsonResponse(['code' => 400, 'msg' => '请填写完整信息']);
        }

        // 这里应该保存到数据库
        // $leave_model = new LeaveRequestModel();
        // $result = $leave_model->create([...]);

        $this->jsonResponse(['code' => 200, 'msg' => '请假申请提交成功']);
    }

    /**
     * JSON响应
     */
    private function jsonResponse($data)
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }
}
