<?php
/**
 * 教师管理页面
 * 创建时间：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../../_ks1.php';
require_once '../../mysqlconn.php';
require_once '../../education/init.php';

// 检查管理员权限
require_permission(ROLE_ADMIN, '../../login.php');

// 引入模型
require_once '../../education/models/Teacher.php';

$teacher_model = new Teacher($conn);

// 处理删除操作
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $teacher_id = intval($_POST['teacher_id']);
    if ($teacher_id > 0) {
        if ($teacher_model->softDelete($teacher_id)) {
            $message = '教师删除成功！';
            log_operation('teacher_delete', "删除教师ID: {$teacher_id}");
        } else {
            $error = '教师删除失败！';
        }
    }
}

// 获取分页参数
$page = intval($_GET['page'] ?? 1);
$per_page = intval($_GET['per_page'] ?? 20);
$search = trim($_GET['search'] ?? '');

// 构建查询条件
$conditions = [];
if ($search) {
    // 这里需要修改为支持模糊搜索的查询
    $teachers_data = $teacher_model->query(
        "SELECT t.*, u.username, u.email 
         FROM " . TABLE_TEACHER . " t 
         LEFT JOIN user u ON t.user_id = u.userid 
         WHERE t.status = ? AND (t.name LIKE ? OR t.subject LIKE ? OR u.email LIKE ?)
         ORDER BY t.create_time DESC
         LIMIT ? OFFSET ?",
        [STATUS_ACTIVE, "%{$search}%", "%{$search}%", "%{$search}%", $per_page, ($page - 1) * $per_page]
    );
    
    $total_count = $teacher_model->query(
        "SELECT COUNT(*) as count
         FROM " . TABLE_TEACHER . " t 
         LEFT JOIN user u ON t.user_id = u.userid 
         WHERE t.status = ? AND (t.name LIKE ? OR t.subject LIKE ? OR u.email LIKE ?)",
        [STATUS_ACTIVE, "%{$search}%", "%{$search}%", "%{$search}%"]
    )[0]['count'];
    
    $pagination = [
        'current_page' => $page,
        'per_page' => $per_page,
        'total' => $total_count,
        'total_pages' => ceil($total_count / $per_page),
        'has_prev' => $page > 1,
        'has_next' => $page < ceil($total_count / $per_page)
    ];
    
    $result = [
        'data' => $teachers_data,
        'pagination' => $pagination
    ];
} else {
    $result = $teacher_model->paginate($page, $per_page, $conditions, 't.create_time DESC');
    $result['data'] = $teacher_model->getTeachersWithUser($conditions, 't.create_time DESC', $per_page, ($page - 1) * $per_page);
}

$teachers = $result['data'];
$pagination = $result['pagination'];

$page_title = '教师管理';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统 - 管理员
            </a>
            <div class="collapse navbar-collapse">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fa fa-dashboard"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="teachers.php">
                            <i class="fa fa-chalkboard-teacher"></i> 教师管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fa fa-user-graduate"></i> 学生管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.php">
                            <i class="fa fa-door-open"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="../../login.php?action=logout">
                            <i class="fa fa-sign-out"></i> 退出
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h4 class="mb-0">
                                    <i class="fa fa-chalkboard-teacher"></i> <?php echo $page_title; ?>
                                </h4>
                            </div>
                            <div class="col-auto">
                                <a href="teacher_form.php" class="btn btn-light">
                                    <i class="fa fa-plus"></i> 添加教师
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($message)): ?>
                        <div class="alert alert-success alert-dismissible fade show">
                            <?php echo safe_html($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger alert-dismissible fade show">
                            <?php echo safe_html($error); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>
                        
                        <!-- 搜索表单 -->
                        <form method="get" class="mb-4">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" 
                                               value="<?php echo safe_html($search); ?>" 
                                               placeholder="搜索教师姓名、科目或邮箱...">
                                        <button class="btn btn-primary" type="submit">
                                            <i class="fa fa-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <select name="per_page" class="form-select" onchange="this.form.submit()">
                                        <option value="10" <?php echo $per_page == 10 ? 'selected' : ''; ?>>每页10条</option>
                                        <option value="20" <?php echo $per_page == 20 ? 'selected' : ''; ?>>每页20条</option>
                                        <option value="50" <?php echo $per_page == 50 ? 'selected' : ''; ?>>每页50条</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <?php if ($search): ?>
                                    <a href="teachers.php" class="btn btn-outline-secondary">
                                        <i class="fa fa-times"></i> 清除搜索
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                        
                        <!-- 教师列表 -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>姓名</th>
                                        <th>科目</th>
                                        <th>联系方式</th>
                                        <th>邮箱</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($teachers)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">
                                            <?php echo $search ? '没有找到匹配的教师' : '暂无教师数据'; ?>
                                        </td>
                                    </tr>
                                    <?php else: ?>
                                        <?php foreach ($teachers as $teacher): ?>
                                        <tr>
                                            <td><?php echo $teacher['id']; ?></td>
                                            <td>
                                                <strong><?php echo safe_html($teacher['name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo safe_html($teacher['username']); ?></small>
                                            </td>
                                            <td><?php echo safe_html($teacher['subject']); ?></td>
                                            <td><?php echo safe_html($teacher['phone']); ?></td>
                                            <td><?php echo safe_html($teacher['email']); ?></td>
                                            <td>
                                                <small><?php echo format_time_display($teacher['create_time']); ?></small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="teacher_form.php?id=<?php echo $teacher['id']; ?>" 
                                                       class="btn btn-outline-primary" title="编辑">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <a href="teacher_detail.php?id=<?php echo $teacher['id']; ?>" 
                                                       class="btn btn-outline-info" title="详情">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger" 
                                                            onclick="deleteTeacher(<?php echo $teacher['id']; ?>, '<?php echo safe_html($teacher['name']); ?>')" 
                                                            title="删除">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <?php if ($pagination['total_pages'] > 1): ?>
                        <nav aria-label="分页导航">
                            <ul class="pagination justify-content-center">
                                <?php if ($pagination['has_prev']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&per_page=<?php echo $per_page; ?>&search=<?php echo urlencode($search); ?>">
                                        上一页
                                    </a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?php echo $i == $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&per_page=<?php echo $per_page; ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&per_page=<?php echo $per_page; ?>&search=<?php echo urlencode($search); ?>">
                                        下一页
                                    </a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        
                        <div class="text-center text-muted">
                            显示第 <?php echo ($pagination['current_page'] - 1) * $per_page + 1; ?> - 
                            <?php echo min($pagination['current_page'] * $per_page, $pagination['total']); ?> 条，
                            共 <?php echo $pagination['total']; ?> 条记录
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除教师 <strong id="teacherName"></strong> 吗？</p>
                    <p class="text-danger">此操作不可恢复！</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form method="post" style="display: inline;">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="teacher_id" id="deleteTeacherId">
                        <button type="submit" class="btn btn-danger">确认删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function deleteTeacher(id, name) {
            document.getElementById('deleteTeacherId').value = id;
            document.getElementById('teacherName').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }
    </script>
</body>
</html>
