<?php
/**
 * 教师课表页面
 * 修订日期：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../../../_ks1.php';
require_once '../../../mysqlconn.php';
require_once '../../init.php';

// 检查是否已登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: ../../login.php');
    exit;
}

$current_user = get_current_login_user();
$user_id = $current_user['id'];
$user_roles = get_user_roles($user_id);

// 检查是否有教师权限
if (!in_array(ROLE_TEACHER, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
    header('Location: ../index.php');
    exit;
}

// 模拟课表数据
$schedule_data = [
    1 => [ // 周一
        ['time' => '09:00-10:30', 'course' => '数学 - 代数基础', 'students' => 12, 'classroom' => 'A101'],
        ['time' => '14:00-15:30', 'course' => '数学 - 几何基础', 'students' => 8, 'classroom' => 'A102']
    ],
    2 => [ // 周二
        ['time' => '10:00-11:30', 'course' => '数学 - 函数基础', 'students' => 15, 'classroom' => 'A101'],
        ['time' => '16:00-17:30', 'course' => '数学 - 综合练习', 'students' => 10, 'classroom' => 'A103']
    ],
    3 => [ // 周三
        ['time' => '09:00-10:30', 'course' => '数学 - 代数进阶', 'students' => 12, 'classroom' => 'A101'],
        ['time' => '14:00-15:30', 'course' => '数学 - 应用题', 'students' => 9, 'classroom' => 'A102']
    ],
    4 => [ // 周四
        ['time' => '10:00-11:30', 'course' => '数学 - 复习课', 'students' => 18, 'classroom' => 'A101'],
        ['time' => '15:00-16:30', 'course' => '数学 - 测试课', 'students' => 18, 'classroom' => 'A101']
    ],
    5 => [ // 周五
        ['time' => '09:00-10:30', 'course' => '数学 - 总结课', 'students' => 20, 'classroom' => 'A101']
    ],
    6 => [], // 周六
    7 => []  // 周日
];

$weekdays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
$page_title = '教师课表 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .schedule-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }
        .schedule-table th {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-align: center;
            padding: 15px 10px;
            border: none;
        }
        .schedule-table td {
            padding: 15px 10px;
            border: 1px solid #e9ecef;
            vertical-align: top;
            height: 120px;
        }
        .course-item {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 5px;
            border-left: 4px solid #28a745;
            transition: all 0.3s;
            cursor: pointer;
        }
        .course-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .course-time {
            font-size: 0.8rem;
            color: #28a745;
            font-weight: bold;
        }
        .course-name {
            font-size: 0.9rem;
            font-weight: bold;
            margin: 2px 0;
        }
        .course-info {
            font-size: 0.75rem;
            color: #666;
        }
        .today-highlight {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%) !important;
            border-left-color: #ffc107 !important;
        }
        .week-navigation {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .btn-week {
            border-radius: 20px;
            padding: 8px 20px;
            margin: 0 5px;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
            margin-bottom: 10px;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="../../index.php">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="schedule.php">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_approval.php">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="parent_binding.php">
                            <i class="fa fa-users"></i> 家长绑定
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> <?php echo safe_html($current_user['name'] ?? $current_user['email']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../../login.php?action=logout"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题和统计 -->
        <div class="row">
            <div class="col-md-8">
                <div class="week-navigation">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4><i class="fa fa-calendar"></i> 我的课表</h4>
                        <div>
                            <button class="btn btn-outline-primary btn-week">
                                <i class="fa fa-chevron-left"></i> 上周
                            </button>
                            <button class="btn btn-primary btn-week">
                                本周 (<?php echo date('m月d日'); ?>)
                            </button>
                            <button class="btn btn-outline-primary btn-week">
                                下周 <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="row">
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">本周课程</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <div class="stat-number">156</div>
                                <div class="stat-label">总课时</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 课表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-0">
                        <table class="table schedule-table mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 100px;">时间</th>
                                    <?php for ($i = 1; $i <= 7; $i++): ?>
                                        <th><?php echo $weekdays[$i]; ?></th>
                                    <?php endfor; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // 获取所有时间段
                                $time_slots = [];
                                foreach ($schedule_data as $day => $courses) {
                                    foreach ($courses as $course) {
                                        if (!in_array($course['time'], $time_slots)) {
                                            $time_slots[] = $course['time'];
                                        }
                                    }
                                }
                                sort($time_slots);
                                
                                foreach ($time_slots as $time_slot):
                                ?>
                                <tr>
                                    <td class="text-center align-middle">
                                        <strong><?php echo $time_slot; ?></strong>
                                    </td>
                                    <?php for ($day = 1; $day <= 7; $day++): ?>
                                        <td>
                                            <?php
                                            $today = date('N'); // 1-7 (Monday-Sunday)
                                            $course_found = false;
                                            
                                            if (isset($schedule_data[$day])) {
                                                foreach ($schedule_data[$day] as $course) {
                                                    if ($course['time'] === $time_slot) {
                                                        $course_found = true;
                                                        $is_today = ($day == $today);
                                                        ?>
                                                        <div class="course-item <?php echo $is_today ? 'today-highlight' : ''; ?>" 
                                                             onclick="showCourseDetail('<?php echo safe_html($course['course']); ?>', '<?php echo $course['time']; ?>', '<?php echo $course['students']; ?>', '<?php echo safe_html($course['classroom']); ?>')">
                                                            <div class="course-time"><?php echo $course['time']; ?></div>
                                                            <div class="course-name"><?php echo safe_html($course['course']); ?></div>
                                                            <div class="course-info">
                                                                <i class="fa fa-users"></i> <?php echo $course['students']; ?>人 | 
                                                                <i class="fa fa-map-marker"></i> <?php echo safe_html($course['classroom']); ?>
                                                            </div>
                                                        </div>
                                                        <?php
                                                        break;
                                                    }
                                                }
                                            }
                                            
                                            if (!$course_found) {
                                                echo '<div class="text-muted text-center">无课程</div>';
                                            }
                                            ?>
                                        </td>
                                    <?php endfor; ?>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 课程说明 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-info-circle"></i> 课程说明</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fa fa-clock"></i> 时间安排</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fa fa-check text-success"></i> 每节课90分钟</li>
                                    <li><i class="fa fa-check text-success"></i> 课间休息15分钟</li>
                                    <li><i class="fa fa-check text-success"></i> 提前10分钟到教室</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fa fa-exclamation-triangle"></i> 注意事项</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fa fa-info text-info"></i> 黄色高亮为今日课程</li>
                                    <li><i class="fa fa-info text-info"></i> 点击课程可查看详情</li>
                                    <li><i class="fa fa-info text-info"></i> 如有调课请及时通知学生</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 课程详情模态框 -->
    <div class="modal fade" id="courseDetailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="courseDetailTitle">课程详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="courseDetailBody">
                    <!-- 课程详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="startClass()">开始上课</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentCourse = null;

        function showCourseDetail(course, time, students, classroom) {
            currentCourse = course;
            document.getElementById('courseDetailTitle').textContent = course;
            document.getElementById('courseDetailBody').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fa fa-clock"></i> 课程时间</h6>
                        <p>${time}</p>
                        
                        <h6><i class="fa fa-users"></i> 学生人数</h6>
                        <p>${students}名学生</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fa fa-map-marker"></i> 上课地点</h6>
                        <p>${classroom}</p>
                        
                        <h6><i class="fa fa-book"></i> 课程状态</h6>
                        <p><span class="badge bg-success">正常进行</span></p>
                    </div>
                </div>
                <hr>
                <h6><i class="fa fa-list"></i> 课程内容</h6>
                <ul>
                    <li>复习上节课内容</li>
                    <li>讲解新知识点</li>
                    <li>课堂练习</li>
                    <li>布置作业</li>
                </ul>
            `;
            
            new bootstrap.Modal(document.getElementById('courseDetailModal')).show();
        }

        function startClass() {
            if (currentCourse) {
                alert(`开始${currentCourse}课程，系统将记录出勤情况`);
                bootstrap.Modal.getInstance(document.getElementById('courseDetailModal')).hide();
            }
        }
    </script>
</body>
</html>
