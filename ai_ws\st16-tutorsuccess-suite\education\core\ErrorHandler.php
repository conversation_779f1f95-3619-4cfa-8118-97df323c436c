<?php
/**
 * 错误处理类
 */
class ErrorHandler {
    /**
     * 注册错误处理器
     * 
     * @return void
     */
    public static function register() {
        // 设置错误处理函数
        set_error_handler([self::class, 'handleError']);
        
        // 设置异常处理函数
        set_exception_handler([self::class, 'handleException']);
        
        // 设置致命错误处理
        register_shutdown_function([self::class, 'handleFatalError']);
    }
    
    /**
     * 处理PHP错误
     * 
     * @param int $errno 错误级别
     * @param string $errstr 错误信息
     * @param string $errfile 发生错误的文件
     * @param int $errline 发生错误的行号
     * @return bool 是否继续执行PHP内部错误处理器
     */
    public static function handleError($errno, $errstr, $errfile, $errline) {
        // 如果错误抑制操作符（@）被使用，则忽略该错误
        if (error_reporting() === 0) {
            return false;
        }
        
        // 根据错误类型获取错误级别名称
        $errorTypes = [
            E_ERROR => 'Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_STRICT => 'Strict Standards',
            E_RECOVERABLE_ERROR => 'Recoverable Error',
            E_DEPRECATED => 'Deprecated',
            E_USER_DEPRECATED => 'User Deprecated'
        ];
        
        $errorType = isset($errorTypes[$errno]) ? $errorTypes[$errno] : 'Unknown Error';
        
        // 记录错误日志
        self::logError("[$errorType] $errstr in $errfile on line $errline");
        
        // 对于严重错误，显示错误页面
        if ($errno == E_USER_ERROR || $errno == E_ERROR || $errno == E_RECOVERABLE_ERROR) {
            self::displayError($errorType, $errstr, $errfile, $errline);
            exit(1);
        }
        
        // 返回false以允许PHP内部错误处理器继续执行
        return false;
    }
    
    /**
     * 处理未捕获的异常
     * 
     * @param Throwable $exception 异常对象
     * @return void
     */
    public static function handleException($exception) {
        // 记录异常日志
        self::logError(
            '[Exception] ' . $exception->getMessage() . 
            ' in ' . $exception->getFile() . 
            ' on line ' . $exception->getLine() . 
            "\nStack Trace: " . $exception->getTraceAsString()
        );
        
        // 显示错误页面
        self::displayError(
            get_class($exception),
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            $exception->getTraceAsString()
        );
        
        exit(1);
    }
    
    /**
     * 处理致命错误
     * 
     * @return void
     */
    public static function handleFatalError() {
        $error = error_get_last();
        
        // 检查是否为致命错误
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            // 记录错误日志
            self::logError(
                '[Fatal Error] ' . $error['message'] . 
                ' in ' . $error['file'] . 
                ' on line ' . $error['line']
            );
            
            // 显示错误页面
            self::displayError(
                'Fatal Error',
                $error['message'],
                $error['file'],
                $error['line']
            );
        }
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $message 错误信息
     * @return void
     */
    private static function logError($message) {
        global $config;
        
        // 检查是否启用日志记录
        if (!isset($config['error_log']) || !$config['error_log']['enabled']) {
            return;
        }
        
        // 日志文件路径
        $logFile = $config['error_log']['path'] ?? __DIR__ . '/../logs/error.log';
        
        // 确保日志目录存在
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0777, true);
        }
        
        // 写入日志
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        error_log($logMessage, 3, $logFile);
    }
    
    /**
     * 显示错误页面
     * 
     * @param string $type 错误类型
     * @param string $message 错误信息
     * @param string $file 发生错误的文件
     * @param int $line 发生错误的行号
     * @param string $trace 堆栈跟踪信息（可选）
     * @return void
     */
    private static function displayError($type, $message, $file, $line, $trace = '') {
        global $config;
        
        // 检查是否显示详细错误信息
        $displayErrors = isset($config['display_errors']) ? $config['display_errors'] : true;
        
        // 如果是AJAX请求，返回JSON格式的错误信息
        if (self::isAjaxRequest()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => $displayErrors ? [
                    'type' => $type,
                    'message' => $message,
                    'file' => $file,
                    'line' => $line
                ] : 'An error occurred.'
            ]);
            exit;
        }
        
        // 如果是普通请求，显示错误页面
        if ($displayErrors) {
            // 显示详细错误信息
            $errorData = [
                'title' => 'Application Error',
                'type' => $type,
                'message' => $message,
                'file' => $file,
                'line' => $line,
                'trace' => $trace
            ];
        } else {
            // 显示通用错误信息
            $errorData = [
                'title' => 'Application Error',
                'message' => 'An unexpected error occurred. Please try again later.'
            ];
        }
        
        // 尝试使用错误视图
        try {
            // 如果应用实例存在，使用视图渲染错误页面
            if (class_exists('Application') && Application::$app) {
                Application::$app->router->renderView('error/index', $errorData);
            } else {
                // 否则使用简单的HTML错误页面
                self::renderSimpleErrorPage($errorData);
            }
        } catch (Throwable $e) {
            // 如果渲染错误页面时出错，使用简单的HTML错误页面
            self::renderSimpleErrorPage($errorData);
        }
        
        exit;
    }
    
    /**
     * 渲染简单的HTML错误页面
     * 
     * @param array $data 错误数据
     * @return void
     */
    private static function renderSimpleErrorPage($data) {
        // 设置HTTP状态码
        http_response_code(500);
        
        // 输出简单的HTML错误页面
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($data['title']) . '</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 20px; }
        .error-container { max-width: 800px; margin: 40px auto; background: #fff; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); padding: 20px; }
        h1 { color: #e74c3c; margin-top: 0; }
        .error-type { font-weight: bold; margin-bottom: 10px; }
        .error-message { background: #f8f8f8; padding: 10px; border-left: 4px solid #e74c3c; margin-bottom: 20px; }
        .error-details { background: #f8f8f8; padding: 10px; border-left: 4px solid #3498db; margin-bottom: 20px; }
        .error-trace { background: #f8f8f8; padding: 10px; border-left: 4px solid #2ecc71; margin-bottom: 20px; white-space: pre-wrap; font-family: monospace; font-size: 12px; overflow-x: auto; }
        .back-link { display: inline-block; margin-top: 20px; color: #3498db; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>' . htmlspecialchars($data['title']) . '</h1>
        
        ' . (isset($data['type']) ? '<div class="error-type">' . htmlspecialchars($data['type']) . '</div>' : '') . '
        
        <div class="error-message">
            ' . htmlspecialchars($data['message']) . '
        </div>
        
        ' . (isset($data['file']) ? '<div class="error-details">
            File: ' . htmlspecialchars($data['file']) . ' (Line: ' . htmlspecialchars($data['line']) . ')
        </div>' : '') . '
        
        ' . (isset($data['trace']) && !empty($data['trace']) ? '<div class="error-trace">
            ' . htmlspecialchars($data['trace']) . '
        </div>' : '') . '
        
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html>';
    }
    
    /**
     * 检查是否为AJAX请求
     * 
     * @return bool
     */
    private static function isAjaxRequest() {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
}