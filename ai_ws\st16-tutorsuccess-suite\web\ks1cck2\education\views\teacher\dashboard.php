<?php
/**
 * 教师主页
 * 修订日期：2025-01-22
 */

define('IN_LOGIN', __METHOD__);
require_once '../../../_ks1.php';
require_once '../../../mysqlconn.php';
require_once '../../init.php';

// 检查是否已登录
if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
    header('Location: ../../login.php');
    exit;
}

$current_user = get_current_login_user();
$user_id = $current_user['id'];
$user_roles = get_user_roles($user_id);

// 检查是否有教师权限
if (!in_array(ROLE_TEACHER, $user_roles) && !in_array(ROLE_ADMIN, $user_roles)) {
    header('Location: ../index.php');
    exit;
}

// 获取教师信息
$teacher_info = null;
if (in_array(ROLE_TEACHER, $user_roles)) {
    $teacher_model = new Teacher();
    $teacher_info = $teacher_model->getByUserId($user_id);
}

// 模拟数据（实际应该从数据库获取）
$stats = [
    'week_courses' => 8,
    'total_students' => 25,
    'pending_leaves' => 3,
    'attendance_rate' => 92
];

$today_schedule = [
    [
        'time' => '09:00-10:30',
        'course' => '数学 - 代数基础',
        'students' => 12,
        'classroom' => '教室A101'
    ],
    [
        'time' => '14:00-15:30',
        'course' => '数学 - 几何基础',
        'students' => 8,
        'classroom' => '教室A102'
    ],
    [
        'time' => '16:00-17:30',
        'course' => '数学 - 综合练习',
        'students' => 15,
        'classroom' => '教室A101'
    ]
];

$pending_leaves = [
    [
        'id' => 1,
        'student_name' => '张同学',
        'course_name' => '数学课',
        'course_time' => '2025-01-22 09:00-10:30',
        'apply_time' => '2025-01-21 10:30',
        'reason' => '身体不适，需要看医生',
        'phone' => '138****5678'
    ],
    [
        'id' => 2,
        'student_name' => '王同学',
        'course_name' => '数学课',
        'course_time' => '2025-01-22 14:00-15:30',
        'apply_time' => '2025-01-21 09:15',
        'reason' => '家庭事务，需要陪同家长办事',
        'phone' => '139****1234'
    ]
];

$page_title = '教师主页 - 特靠谱教培系统';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <!-- Bootstrap CSS -->
    <link href="../../../bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../../css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .feature-card:hover {
            background-color: #f8f9fa;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #28a745;
        }
        .schedule-item {
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 8px 8px 0;
        }
        .schedule-time {
            font-weight: bold;
            color: #28a745;
        }
        .schedule-subject {
            font-size: 1.1rem;
            margin: 5px 0;
        }
        .schedule-students {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .quick-stats {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .leave-request {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s;
        }
        .leave-request:hover {
            border-color: #28a745;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
        }
        .leave-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .btn-approve {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.8rem;
        }
        .btn-reject {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="../../index.php">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.php">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave_approval.php">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="parent_binding.php">
                            <i class="fa fa-users"></i> 家长绑定
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> <?php echo safe_html($current_user['name'] ?? $current_user['email']); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="profile.php"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../../login.php?action=logout"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row">
            <div class="col-12">
                <div class="quick-stats">
                    <h4><i class="fa fa-chalkboard-teacher"></i> 欢迎回来，<?php echo safe_html($current_user['name'] ?? $current_user['email']); ?>！</h4>
                    <p class="mb-0">今天是 <span id="currentDate"></span>，祝您工作顺利！</p>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-success"><?php echo $stats['week_courses']; ?></div>
                        <div class="stat-label">本周课程</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-primary"><?php echo $stats['total_students']; ?></div>
                        <div class="stat-label">学生总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-warning"><?php echo $stats['pending_leaves']; ?></div>
                        <div class="stat-label">待审批请假</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-info"><?php echo $stats['attendance_rate']; ?>%</div>
                        <div class="stat-label">平均出勤率</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 今日课程 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-day"></i> 今日课程</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($today_schedule)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fa fa-calendar-times fa-3x mb-3"></i>
                                <p>今天没有安排课程</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($today_schedule as $item): ?>
                                <div class="schedule-item">
                                    <div class="schedule-time"><?php echo $item['time']; ?></div>
                                    <div class="schedule-subject"><?php echo safe_html($item['course']); ?></div>
                                    <div class="schedule-students">
                                        <i class="fa fa-users"></i> <?php echo $item['students']; ?>名学生 | 
                                        <i class="fa fa-map-marker"></i> <?php echo safe_html($item['classroom']); ?>
                                        <span class="float-end">
                                            <button class="btn btn-sm btn-outline-success" onclick="startClass('<?php echo safe_html($item['course']); ?>')">
                                                <i class="fa fa-play"></i> 开始上课
                                            </button>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 快速功能 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-bolt"></i> 快速功能</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="feature-card" onclick="location.href='schedule.php'">
                            <div class="feature-icon">
                                <i class="fa fa-calendar"></i>
                            </div>
                            <h6>查看课表</h6>
                            <p class="text-muted mb-0">查看完整教学安排</p>
                        </div>
                        <hr class="my-0">
                        <div class="feature-card" onclick="location.href='leave_approval.php'">
                            <div class="feature-icon">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <h6>请假审批</h6>
                            <p class="text-muted mb-0">处理学生请假申请</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待处理请假申请 -->
        <?php if (!empty($pending_leaves)): ?>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fa fa-clock"></i> 待处理请假申请</h5>
                        <a href="leave_approval.php" class="btn btn-sm btn-outline-light">
                            <i class="fa fa-list"></i> 查看全部
                        </a>
                    </div>
                    <div class="card-body">
                        <?php foreach ($pending_leaves as $leave): ?>
                            <div class="leave-request">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <strong><?php echo safe_html($leave['student_name']); ?> - <?php echo safe_html($leave['course_name']); ?>请假</strong>
                                            <span class="leave-status status-pending">待审批</span>
                                        </div>
                                        <div class="text-muted small">
                                            <div>课程时间：<?php echo $leave['course_time']; ?></div>
                                            <div>申请时间：<?php echo $leave['apply_time']; ?></div>
                                            <div>请假原因：<?php echo safe_html($leave['reason']); ?></div>
                                            <div>联系方式：<?php echo safe_html($leave['phone']); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-approve me-2" onclick="approveLeave(<?php echo $leave['id']; ?>, '<?php echo safe_html($leave['student_name']); ?>', '<?php echo safe_html($leave['course_name']); ?>')">
                                            <i class="fa fa-check"></i> 批准
                                        </button>
                                        <button class="btn btn-reject" onclick="rejectLeave(<?php echo $leave['id']; ?>, '<?php echo safe_html($leave['student_name']); ?>', '<?php echo safe_html($leave['course_name']); ?>')">
                                            <i class="fa fa-times"></i> 拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- 审批确认模态框 -->
    <div class="modal fade" id="approvalModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="approvalModalTitle">请假审批</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="approvalModalBody">
                    <!-- 审批内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="confirmApproval">确认</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../../js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../../bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });

        // 开始上课
        function startClass(subject) {
            alert(`开始${subject}课程，系统将记录出勤情况`);
        }

        // 批准请假
        function approveLeave(leaveId, student, subject) {
            document.getElementById('approvalModalTitle').textContent = '批准请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认批准 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">审批意见（可选）：</label>
                    <textarea class="form-control" id="approvalComment" rows="3" placeholder="请输入审批意见..."></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const comment = document.getElementById('approvalComment').value;
                
                // 这里应该调用实际的API
                alert('请假申请已批准');
                location.reload();
                
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }

        // 拒绝请假
        function rejectLeave(leaveId, student, subject) {
            document.getElementById('approvalModalTitle').textContent = '拒绝请假';
            document.getElementById('approvalModalBody').innerHTML = `
                <p>确认拒绝 <strong>${student}</strong> 的 <strong>${subject}</strong> 课请假申请吗？</p>
                <div class="mb-3">
                    <label class="form-label">拒绝原因：</label>
                    <textarea class="form-control" id="rejectReason" rows="3" placeholder="请输入拒绝原因..." required></textarea>
                </div>
            `;
            
            document.getElementById('confirmApproval').onclick = function() {
                const reason = document.getElementById('rejectReason').value;
                if (!reason.trim()) {
                    alert('请输入拒绝原因');
                    return;
                }
                
                // 这里应该调用实际的API
                alert('请假申请已拒绝');
                location.reload();
                
                bootstrap.Modal.getInstance(document.getElementById('approvalModal')).hide();
            };
            
            new bootstrap.Modal(document.getElementById('approvalModal')).show();
        }
    </script>
</body>
</html>
