<?php
namespace Core;

/**
 * 模型基类
 */
class Model
{
    protected $db;
    protected $table;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查找所有记录
     * 
     * @param array $conditions 条件
     * @param string $orderBy 排序
     * @param int $limit 限制
     * @param int $offset 偏移
     * @return array 记录列表
     */
    public function findAll($conditions = [], $orderBy = '', $limit = 0, $offset = 0)
    {
        return $this->db->select($this->table, '*', $conditions, $orderBy, $limit, $offset);
    }
    
    /**
     * 查找单条记录
     * 
     * @param array $conditions 条件
     * @return array|null 记录
     */
    public function findOne($conditions = [])
    {
        return $this->db->selectOne($this->table, '*', $conditions);
    }
    
    /**
     * 根据ID查找记录
     * 
     * @param int $id ID
     * @return array|null 记录
     */
    public function findById($id)
    {
        return $this->findOne(['id' => $id]);
    }
    
    /**
     * 插入记录
     * 
     * @param array $data 数据
     * @return int|bool 插入ID或失败
     */
    public function insert($data)
    {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新记录
     * 
     * @param array $data 数据
     * @param array $conditions 条件
     * @return int|bool 影响行数或失败
     */
    public function update($data, $conditions)
    {
        return $this->db->update($this->table, $data, $conditions);
    }
    
    /**
     * 删除记录
     * 
     * @param array $conditions 条件
     * @return int|bool 影响行数或失败
     */
    public function delete($conditions)
    {
        return $this->db->delete($this->table, $conditions);
    }
    
    /**
     * 计数
     * 
     * @param array $conditions 条件
     * @return int 数量
     */
    public function count($conditions = [])
    {
        return $this->db->count($this->table, $conditions);
    }
    
    /**
     * 执行自定义SQL
     * 
     * @param string $sql SQL语句
     * @param array $params 参数
     * @return array|bool 结果集或失败
     */
    public function query($sql, $params = [])
    {
        return $this->db->query($sql, $params);
    }
}