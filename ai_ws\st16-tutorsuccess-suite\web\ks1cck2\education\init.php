<?php
/**
 * 教培系统初始化文件
 * 创建时间：2025-01-22
 */

// 防止直接访问
if (!defined('IN_LOGIN')) {
    die('Access denied');
}

// 引入必要的文件
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';

/**
 * 初始化教培系统
 * @return bool
 */
function init_education_system() {
    // 检查数据库表是否存在
    if (!check_edu_tables()) {
        // 如果表不存在，尝试创建
        if (!init_edu_database()) {
            error_log('教培系统数据库初始化失败');
            return false;
        }
    }
    
    // 创建上传目录
    $upload_dir = edu_config('upload.upload_path');
    if (!is_dir($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            error_log('创建上传目录失败: ' . $upload_dir);
        }
    }
    
    return true;
}

/**
 * 获取当前用户的角色
 * @param int $user_id
 * @return array
 */
function get_user_roles($user_id) {
    global $conn;
    
    $sql = "SELECT role_type FROM " . TABLE_USER_ROLE . " WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $roles = [];
    while ($row = $result->fetch_assoc()) {
        $roles[] = $row['role_type'];
    }
    
    return $roles;
}

/**
 * 检查用户是否有指定角色
 * @param int $user_id
 * @param int $role_type
 * @return bool
 */
function user_has_role($user_id, $role_type) {
    $roles = get_user_roles($user_id);
    return in_array($role_type, $roles);
}

/**
 * 获取用户的主要角色（优先级：管理员 > 教师 > 学生）
 * @param int $user_id
 * @return int|null
 */
function get_user_primary_role($user_id) {
    $roles = get_user_roles($user_id);
    
    if (in_array(ROLE_ADMIN, $roles)) {
        return ROLE_ADMIN;
    } elseif (in_array(ROLE_TEACHER, $roles)) {
        return ROLE_TEACHER;
    } elseif (in_array(ROLE_STUDENT, $roles)) {
        return ROLE_STUDENT;
    }
    
    return null;
}

/**
 * 获取角色名称
 * @param int $role_type
 * @return string
 */
function get_role_name($role_type) {
    switch ($role_type) {
        case ROLE_ADMIN:
            return '管理员';
        case ROLE_TEACHER:
            return '教师';
        case ROLE_STUDENT:
            return '学生';
        default:
            return '未知';
    }
}

/**
 * 权限检查
 * @param int $required_role
 * @return bool
 */
function check_permission($required_role) {
    if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
        return false;
    }
    
    $user_id = $_SESSION[SESSION_KEY]['user']['id'];
    $user_roles = get_user_roles($user_id);
    
    // 管理员拥有所有权限
    if (in_array(ROLE_ADMIN, $user_roles)) {
        return true;
    }
    
    // 检查是否有指定角色
    return in_array($required_role, $user_roles);
}

/**
 * 权限检查中间件
 * @param int $required_role
 * @param string $redirect_url
 */
function require_permission($required_role, $redirect_url = 'login.php') {
    if (!check_permission($required_role)) {
        if (is_ajax_request()) {
            json_response([], 403, '权限不足');
        } else {
            header('Location: ' . $redirect_url);
            exit;
        }
    }
}

/**
 * 获取当前登录用户信息
 * @return array|null
 */
function get_current_login_user() {
    if (!isset($_SESSION[SESSION_KEY]['user'])) {
        return null;
    }

    return $_SESSION[SESSION_KEY]['user'];
}

/**
 * 获取当前用户ID
 * @return int|null
 */
function get_current_user_id() {
    $user = get_current_login_user();
    return $user ? $user['id'] : null;
}

/**
 * 记录操作日志
 * @param string $action 操作类型
 * @param string $description 操作描述
 * @param array $data 相关数据
 */
function log_operation($action, $description, $data = []) {
    $user_id = get_current_user_id();
    $log_data = [
        'user_id' => $user_id,
        'action' => $action,
        'description' => $description,
        'data' => json_encode($data, JSON_UNESCAPED_UNICODE),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'time' => date('Y-m-d H:i:s')
    ];
    
    error_log('教培系统操作日志: ' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
}

/**
 * 获取星期几的中文名称
 * @param int $day_of_week 1-7
 * @return string
 */
function get_weekday_name($day_of_week) {
    $weekdays = edu_config('time.week_days');
    return $weekdays[$day_of_week] ?? '';
}

/**
 * 获取请假类型名称
 * @param int $leave_type
 * @return string
 */
function get_leave_type_name($leave_type) {
    switch ($leave_type) {
        case LEAVE_TYPE_SICK:
            return '病假';
        case LEAVE_TYPE_PERSONAL:
            return '事假';
        case LEAVE_TYPE_OTHER:
            return '其他';
        default:
            return '未知';
    }
}

/**
 * 获取请假状态名称
 * @param int $status
 * @return string
 */
function get_leave_status_name($status) {
    switch ($status) {
        case LEAVE_STATUS_PENDING:
            return '待审批';
        case LEAVE_STATUS_APPROVED:
            return '已批准';
        case LEAVE_STATUS_REJECTED:
            return '已拒绝';
        default:
            return '未知';
    }
}

// 自动初始化系统
init_education_system();
