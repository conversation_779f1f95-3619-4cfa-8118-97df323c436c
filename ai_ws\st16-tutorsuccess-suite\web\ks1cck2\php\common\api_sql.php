<?php

function check_user_id($user_id,$method=''){
    if(empty($user_id) || intval($user_id)<1) {
        die($method.'() error! miss user_id');
    }
}

function sql_nav_menu_by_upid($upid){

    $tmp_sql = 'SELECT `navigation_id` as id, `title`,`link` FROM `config_navigation`  ' . " where `parent_id`=?i  order by `sort` limit 100 ";
    $tmp_sql=prepare($tmp_sql,array($upid ) );
    
    $sql_ret = get_data($tmp_sql);
    //if(DEBUG_MODE) html_log( $tmp_sql);
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}



function sql_demo_by_upid($upid){

    $tmp_sql = 'SELECT `navigation_id` as id, `title`,`title` as name,`level` FROM `config_navigation`  ' . " where `parent_id`=?i order by `sort` limit 100 ";
    $tmp_sql=prepare($tmp_sql,array($upid ) );
    
    $sql_ret = get_data($tmp_sql);
    //if(DEBUG_MODE) html_log( $tmp_sql);
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}

function sql_config_navigation_get_all_by_id($id){

    $tmp_sql = 'SELECT *,`navigation_id` as id FROM `config_navigation`  ' . " where `navigation_id`=?i limit 1 ";
    $tmp_sql=prepare($tmp_sql,array($id ) );
    
    $sql_ret = get_line($tmp_sql);
    //if(DEBUG_MODE) html_log( $tmp_sql);
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}



/**
     * 把unicode编码的字符串转为人眼可看的字符串
     * @param $unicode_str
     *
     * @return string
     */
    function unicodeDecode($unicode_str){
        $unicode_str = str_replace('"', '\"', $unicode_str);
        $unicode_str = str_replace("'", "\'", $unicode_str);
        $json = '{"str":"'.$unicode_str.'"}';
 
        $arr = json_decode($json,true);
 
        if(empty($arr)){
            return '';
        }
 
        return $arr['str'];
    }


    /**
     * 中文转换为JSON字符串
     * @param $chinese_str 中文：可以包含中文字母数字
     * @return string
     */
    function json2_encode($chinese_str)
    {
        $chinese_str = iconv('UTF-8', 'UCS-2', $chinese_str);
        $len = strlen($chinese_str) - 1;
        $str = '';
        for ($i = 0; $i < $len; $i = $i + 2)
        {
            $c1 = $chinese_str[$i];
            $c2 = $chinese_str[$i + 1];
            $unicode1=ord($c1);
            $unicode2=ord($c2);
            if ($unicode1)
            {    // 两个字节的文字
                $unicode1='\u'.base_convert($unicode1, 10, 16);
                $unicode2=base_convert($unicode2, 10, 16);
                if(in_array($unicode2,array('a','b','c','d','e','f'))){
                    $unicode2='0'.$unicode2;
                }
                $str.=$unicode1.$unicode2;
            }else{
                $str.=$c2;
            }
        }
        return $str;
    }
 
    /**
     * JSON字符串转换为中文
     * @param $json_str JSON字符串
     * @return string
     */
    function json2show_decode($json_str)
    {
        // 转换编码，将JSON字符串转换成可以浏览的utf-8编码
        $pattern = '/([\w]+)|(\\\u([\w]{4}))/i';
        preg_match_all($pattern, $json_str, $matches);
        $chinese_str = '';
        if (!empty($matches))
        {
            $count=count($matches[0]);
            for ($j = 0; $j < $count; $j++)
            {
                $str = $matches[0][$j];
                if (strpos($str, '\\u') === 0)
                {
                    $code = base_convert(substr($str, 2, 2), 16, 10);
                    $code2 = base_convert(substr($str, 4), 16, 10);
                    $c = chr($code).chr($code2);
                    $c = iconv('UCS-2', 'UTF-8', $c);
                    $chinese_str .= $c;
                }
                else
                {
                    $chinese_str .= $str;
                }
            }
        }
        return $chinese_str;
    }


function ks1debug($obj, $varName=null) {

    echo '<!-- '.PHP_EOL;

   
    // 检查是否有足够的信息来获取变量名
    if (!empty($varName)) {    
        echo 'varName='.$varName.PHP_EOL;
    }

    var_dump($obj);    
    
    echo ' -->'.PHP_EOL;
}



function hid_var_dump($obj){
    pre_var_dump($obj,true);
}

function pre_var_dump($obj,$html_hide=false,$ext_desc=null){
    if(! DEBUG_MODE){
        return false;
    }
    if($html_hide){
        echo '<!-- '.PHP_EOL;
    }else{
        echo '<pre>'.PHP_EOL;    
    }

    if($ext_desc){
        echo 'desc:'.$ext_desc.PHP_EOL;
    }
    
    var_dump($obj);

    if($html_hide){
        echo ' -->'.PHP_EOL;
    }else{
        echo '</pre>'.PHP_EOL;    
    }
}


function sql_get_conf_nav_by_upid($upid,$limit_max1=100){
    $limit_max=intval($limit_max1);
    if($limit_max<10){
        $limit_max=10;
    }
    $tmp_sql = 'SELECT `navigation_id` as id, `title` as name FROM `config_navigation`  ' . " where `parent_id`=?i limit ".intval($limit_max);
    $tmp_sql=prepare($tmp_sql,array($upid ) );
    
    $sql_ret = get_data($tmp_sql);
    //if(DEBUG_MODE) echo $tmp_sql.PHP_EOL;
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}


function sql_get_conf_nav_all_by_upid($upid,$limit_max1=100){
    $limit_max=intval($limit_max1);
    if($limit_max<10){
        $limit_max=10;
    }
    if(IS_ADMIN){
        $where ='';
    }else{
        $where =' and `level`<=3 ';
    }
    $tmp_sql = 'SELECT *,`navigation_id` as id, `title` as name FROM `config_navigation`  ' . " where `parent_id`=?i $where order by sort limit ".intval($limit_max);
    $tmp_sql=prepare($tmp_sql,array($upid ) );
    
    $sql_ret = get_data($tmp_sql);
    //if(DEBUG_MODE) echo $tmp_sql.PHP_EOL;
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}

function sql_get_conf_nav_all_by_id($id){
    $tmp_sql = 'SELECT *,`navigation_id` as id, `title` as name FROM `config_navigation`  ' . " where `navigation_id`=?i limit 1 ";
    $tmp_sql=prepare($tmp_sql,array($id ) );
    
    $sql_ret = get_line($tmp_sql);
    //if(DEBUG_MODE) echo $tmp_sql.PHP_EOL;
    //if(DEBUG_MODE) var_dump($sql_ret);
    return $sql_ret;
}

function sql_get_total_count($table_name,$where=''){

    $tmp_sql = 'SELECT COUNT(*) `total` FROM `'.$table_name.'`  ' .$where;
    $sql_ret = get_var($tmp_sql);
    return $sql_ret;
}



