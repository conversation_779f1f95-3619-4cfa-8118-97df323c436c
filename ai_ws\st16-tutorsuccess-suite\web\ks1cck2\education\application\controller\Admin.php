<?php
/**
 * 管理员控制器
 * 修订日期：2025-01-22
 */

namespace app\controller;

use app\model\Teacher as TeacherModel;
use app\model\Student as StudentModel;
use app\model\Course as CourseModel;

class Admin
{
    /**
     * 管理员主页
     */
    public function index()
    {
        // 检查权限
        $this->checkAdminAuth();
        
        $current_user = get_current_login_user();
        
        // 获取统计数据
        $stats = $this->getSystemStats();
        
        // 获取最近活动
        $recent_activities = $this->getRecentActivities();

        // 渲染视图
        include '../application/view/admin/index.php';
    }

    /**
     * 用户管理
     */
    public function users()
    {
        // 检查权限
        $this->checkAdminAuth();
        
        // 获取用户列表
        $teachers = $this->getTeachers();
        $students = $this->getStudents();
        
        // 渲染视图
        include '../application/view/admin/users.php';
    }

    /**
     * 课程管理
     */
    public function courses()
    {
        // 检查权限
        $this->checkAdminAuth();
        
        // 获取课程列表
        $courses = $this->getCourses();
        
        // 渲染视图
        include '../application/view/admin/courses.php';
    }

    /**
     * 检查管理员权限
     */
    private function checkAdminAuth()
    {
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            header('Location: ../login.php');
            exit;
        }

        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        $user_roles = get_user_roles($user_id);

        if (!in_array(ROLE_ADMIN, $user_roles)) {
            header('Location: ../index.php');
            exit;
        }
    }

    /**
     * 获取系统统计数据
     */
    private function getSystemStats()
    {
        // 这里应该从数据库获取真实数据
        return [
            'total_teachers' => 5,
            'total_students' => 120,
            'total_courses' => 15,
            'active_classes' => 8
        ];
    }

    /**
     * 获取最近活动
     */
    private function getRecentActivities()
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'time' => '2025-01-22 10:30',
                'type' => 'user_register',
                'description' => '新学生张同学注册'
            ],
            [
                'time' => '2025-01-22 09:15',
                'type' => 'course_create',
                'description' => '李老师创建了新课程：高等数学'
            ],
            [
                'time' => '2025-01-21 16:45',
                'type' => 'leave_approve',
                'description' => '李老师批准了王同学的请假申请'
            ]
        ];
    }

    /**
     * 获取教师列表
     */
    private function getTeachers()
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'id' => 1,
                'name' => '李老师',
                'subject' => '数学',
                'phone' => '138****1234',
                'email' => '<EMAIL>',
                'status' => 1
            ],
            [
                'id' => 2,
                'name' => '王老师',
                'subject' => '英语',
                'phone' => '139****5678',
                'email' => '<EMAIL>',
                'status' => 1
            ]
        ];
    }

    /**
     * 获取学生列表
     */
    private function getStudents()
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'id' => 1,
                'name' => '张同学',
                'grade' => '高一',
                'parent_name' => '张家长',
                'parent_phone' => '138****9999',
                'status' => 1
            ],
            [
                'id' => 2,
                'name' => '王同学',
                'grade' => '高二',
                'parent_name' => '王家长',
                'parent_phone' => '139****8888',
                'status' => 1
            ]
        ];
    }

    /**
     * 获取课程列表
     */
    private function getCourses()
    {
        // 这里应该从数据库获取真实数据
        return [
            [
                'id' => 1,
                'name' => '数学 - 代数基础',
                'teacher' => '李老师',
                'students' => 12,
                'status' => 1
            ],
            [
                'id' => 2,
                'name' => '英语 - 综合课程',
                'teacher' => '王老师',
                'students' => 15,
                'status' => 1
            ]
        ];
    }
}
