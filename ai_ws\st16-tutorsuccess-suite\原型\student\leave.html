<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请假申请 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: border-color 0.3s;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: 500;
            transition: transform 0.3s;
        }
        .btn-submit:hover {
            transform: translateY(-2px);
            color: white;
        }
        .leave-record {
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            border-radius: 0 10px 10px 0;
            transition: transform 0.2s;
        }
        .leave-record:hover {
            transform: translateX(5px);
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 5px 10px;
            border-radius: 15px;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-approved {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .quick-reason {
            cursor: pointer;
            transition: all 0.3s;
        }
        .quick-reason:hover {
            background-color: #667eea;
            color: white;
        }
        .course-selector {
            max-height: 200px;
            overflow-y: auto;
        }
        .course-item {
            padding: 10px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .course-item:hover {
            background-color: #f8f9fa;
            border-color: #667eea;
        }
        .course-item.selected {
            background-color: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="leave.html">
                            <i class="fa fa-file-text"></i> 请假
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 张同学
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-file-text"></i> 请假申请</h2>
                <p class="text-muted">提交请假申请，老师会及时处理</p>
            </div>
        </div>

        <div class="row">
            <!-- 请假申请表单 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-plus"></i> 新建请假申请</h5>
                    </div>
                    <div class="card-body">
                        <form id="leaveForm">
                            <!-- 选择课程 -->
                            <div class="mb-4">
                                <label class="form-label"><strong>选择请假课程</strong></label>
                                <div class="course-selector">
                                    <div class="course-item" onclick="selectCourse(this, '数学', '李老师', '2025-01-21 09:00', 'A101')">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>数学 - 代数基础</strong><br>
                                                <small class="text-muted">李老师 | 2025-01-21 09:00-10:30 | 教室A101</small>
                                            </div>
                                            <i class="fa fa-check" style="display: none;"></i>
                                        </div>
                                    </div>
                                    <div class="course-item" onclick="selectCourse(this, '英语', '王老师', '2025-01-21 14:00', 'B203')">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>英语 - 语法练习</strong><br>
                                                <small class="text-muted">王老师 | 2025-01-21 14:00-15:30 | 教室B203</small>
                                            </div>
                                            <i class="fa fa-check" style="display: none;"></i>
                                        </div>
                                    </div>
                                    <div class="course-item" onclick="selectCourse(this, '物理', '张老师', '2025-01-21 16:00', 'C301')">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>物理 - 力学实验</strong><br>
                                                <small class="text-muted">张老师 | 2025-01-21 16:00-17:30 | 实验室C301</small>
                                            </div>
                                            <i class="fa fa-check" style="display: none;"></i>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" id="selectedCourse" name="course" required>
                            </div>

                            <!-- 请假原因 -->
                            <div class="mb-4">
                                <label for="reason" class="form-label"><strong>请假原因</strong></label>
                                <div class="mb-3">
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="btn btn-outline-secondary quick-reason w-100" onclick="setReason('身体不适')">
                                                <i class="fa fa-thermometer-half"></i> 身体不适
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="btn btn-outline-secondary quick-reason w-100" onclick="setReason('家庭事务')">
                                                <i class="fa fa-home"></i> 家庭事务
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="btn btn-outline-secondary quick-reason w-100" onclick="setReason('其他安排')">
                                                <i class="fa fa-calendar-alt"></i> 其他安排
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <textarea class="form-control" id="reason" name="reason" rows="4" 
                                          placeholder="请详细说明请假原因..." required></textarea>
                            </div>

                            <!-- 联系方式 -->
                            <div class="mb-4">
                                <label for="contact" class="form-label"><strong>联系方式</strong></label>
                                <input type="text" class="form-control" id="contact" name="contact" 
                                       placeholder="请留下联系电话或微信号" required>
                            </div>

                            <!-- 提交按钮 -->
                            <div class="text-center">
                                <button type="submit" class="btn btn-submit">
                                    <i class="fa fa-paper-plane"></i> 提交申请
                                </button>
                                <button type="reset" class="btn btn-outline-secondary ms-3">
                                    <i class="fa fa-undo"></i> 重置
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 申请记录 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-history"></i> 申请记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="leave-record">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <strong>数学 - 代数基础</strong>
                                <span class="status-badge status-approved">已批准</span>
                            </div>
                            <div class="text-muted small">
                                <div>申请时间：2025-01-20 10:30</div>
                                <div>课程时间：2025-01-20 09:00</div>
                                <div>请假原因：身体不适</div>
                                <div>审批老师：李老师</div>
                            </div>
                        </div>

                        <div class="leave-record">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <strong>英语 - 语法练习</strong>
                                <span class="status-badge status-pending">待审批</span>
                            </div>
                            <div class="text-muted small">
                                <div>申请时间：2025-01-19 15:20</div>
                                <div>课程时间：2025-01-22 14:00</div>
                                <div>请假原因：家庭事务</div>
                                <div>审批老师：王老师</div>
                            </div>
                        </div>

                        <div class="leave-record">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <strong>物理 - 力学实验</strong>
                                <span class="status-badge status-rejected">已拒绝</span>
                            </div>
                            <div class="text-muted small">
                                <div>申请时间：2025-01-18 11:15</div>
                                <div>课程时间：2025-01-18 16:00</div>
                                <div>请假原因：其他安排</div>
                                <div>审批老师：张老师</div>
                                <div class="text-danger">拒绝原因：请假时间过晚</div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <button class="btn btn-outline-primary btn-sm" onclick="showAllRecords()">
                                <i class="fa fa-list"></i> 查看全部记录
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成功提示模态框 -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-success">
                        <i class="fa fa-check-circle"></i> 申请提交成功
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>您的请假申请已成功提交，老师会在24小时内处理您的申请。</p>
                    <p>您可以在申请记录中查看审批状态。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedCourseData = null;

        // 选择课程
        function selectCourse(element, subject, teacher, time, room) {
            // 清除之前的选择
            document.querySelectorAll('.course-item').forEach(item => {
                item.classList.remove('selected');
                item.querySelector('.fa-check').style.display = 'none';
            });
            
            // 选择当前课程
            element.classList.add('selected');
            element.querySelector('.fa-check').style.display = 'block';
            
            // 保存选择的课程数据
            selectedCourseData = { subject, teacher, time, room };
            document.getElementById('selectedCourse').value = JSON.stringify(selectedCourseData);
        }

        // 设置快速原因
        function setReason(reason) {
            document.getElementById('reason').value = reason;
            
            // 重置所有快速原因按钮
            document.querySelectorAll('.quick-reason').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-secondary');
            });
            
            // 高亮选中的按钮
            event.target.classList.remove('btn-outline-secondary');
            event.target.classList.add('btn-primary');
        }

        // 表单提交
        document.getElementById('leaveForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!selectedCourseData) {
                alert('请选择要请假的课程');
                return;
            }
            
            const formData = new FormData(this);
            const reason = formData.get('reason');
            const contact = formData.get('contact');
            
            if (!reason.trim()) {
                alert('请填写请假原因');
                return;
            }
            
            if (!contact.trim()) {
                alert('请填写联系方式');
                return;
            }
            
            // 这里应该调用实际的API提交申请
            console.log('提交请假申请:', {
                course: selectedCourseData,
                reason: reason,
                contact: contact
            });
            
            // 显示成功提示
            new bootstrap.Modal(document.getElementById('successModal')).show();
            
            // 重置表单
            this.reset();
            selectedCourseData = null;
            document.querySelectorAll('.course-item').forEach(item => {
                item.classList.remove('selected');
                item.querySelector('.fa-check').style.display = 'none';
            });
            document.querySelectorAll('.quick-reason').forEach(btn => {
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-outline-secondary');
            });
        });

        // 查看全部记录
        function showAllRecords() {
            alert('跳转到完整的申请记录页面');
        }
    </script>
</body>
</html>
