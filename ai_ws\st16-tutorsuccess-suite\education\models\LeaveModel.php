<?php
/**
 * 请假模型
 * 处理请假数据的操作
 * 修订日期：2025-01-22
 */
class LeaveModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'leaves';
    
    /**
     * 根据ID获取请假信息
     * @param int $id 请假ID
     * @return array|null 请假信息
     */
    public function getLeaveById($id) {
        $sql = "SELECT l.*, s.student_number, u.name as student_name, u.phone as student_phone,
                       c.name as course_name, t.subject, tu.name as teacher_name,
                       cr.name as classroom_name, sch.day_of_week, sch.start_time, sch.end_time,
                       au.name as approved_by_name
                FROM {$this->table} l 
                JOIN students s ON l.student_id = s.id 
                JOIN users u ON s.user_id = u.id 
                JOIN courses c ON l.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                JOIN users tu ON t.user_id = tu.id 
                LEFT JOIN schedules sch ON l.schedule_id = sch.id 
                LEFT JOIN classrooms cr ON sch.classroom_id = cr.id 
                LEFT JOIN users au ON l.approved_by = au.id 
                WHERE l.id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 获取请假列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @param array $filters 过滤条件
     * @return array 请假列表
     */
    public function getLeaves($page = 1, $perPage = 10, $filters = []) {
        $offset = ($page - 1) * $perPage;
        $where = "1=1";
        $params = [];
        
        // 添加过滤条件
        if (!empty($filters['status'])) {
            $where .= " AND l.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['teacher_id'])) {
            $where .= " AND c.teacher_id = ?";
            $params[] = $filters['teacher_id'];
        }
        
        if (!empty($filters['student_id'])) {
            $where .= " AND l.student_id = ?";
            $params[] = $filters['student_id'];
        }
        
        if (!empty($filters['course_id'])) {
            $where .= " AND l.course_id = ?";
            $params[] = $filters['course_id'];
        }
        
        if (!empty($filters['start_date'])) {
            $where .= " AND l.start_date >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where .= " AND l.end_date <= ?";
            $params[] = $filters['end_date'];
        }
        
        $sql = "SELECT l.*, s.student_number, u.name as student_name, u.phone as student_phone,
                       c.name as course_name, t.subject, tu.name as teacher_name,
                       cr.name as classroom_name, sch.day_of_week, sch.start_time, sch.end_time,
                       au.name as approved_by_name
                FROM {$this->table} l 
                JOIN students s ON l.student_id = s.id 
                JOIN users u ON s.user_id = u.id 
                JOIN courses c ON l.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                JOIN users tu ON t.user_id = tu.id 
                LEFT JOIN schedules sch ON l.schedule_id = sch.id 
                LEFT JOIN classrooms cr ON sch.classroom_id = cr.id 
                LEFT JOIN users au ON l.approved_by = au.id 
                WHERE {$where}
                ORDER BY l.created_at DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, $params);
    }
    
    /**
     * 获取请假总数
     * @param array $filters 过滤条件
     * @return int 请假总数
     */
    public function getLeaveCount($filters = []) {
        $where = "1=1";
        $params = [];
        
        // 添加过滤条件
        if (!empty($filters['status'])) {
            $where .= " AND l.status = ?";
            $params[] = $filters['status'];
        }
        
        if (!empty($filters['teacher_id'])) {
            $where .= " AND c.teacher_id = ?";
            $params[] = $filters['teacher_id'];
        }
        
        if (!empty($filters['student_id'])) {
            $where .= " AND l.student_id = ?";
            $params[] = $filters['student_id'];
        }
        
        if (!empty($filters['course_id'])) {
            $where .= " AND l.course_id = ?";
            $params[] = $filters['course_id'];
        }
        
        if (!empty($filters['start_date'])) {
            $where .= " AND l.start_date >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $where .= " AND l.end_date <= ?";
            $params[] = $filters['end_date'];
        }
        
        $sql = "SELECT COUNT(*) FROM {$this->table} l 
                JOIN courses c ON l.course_id = c.id 
                WHERE {$where}";
        return $this->db->count($sql, $params);
    }
    
    /**
     * 创建请假申请
     * @param array $data 请假数据
     * @return int 新请假申请的ID
     */
    public function createLeave($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新请假信息
     * @param int $id 请假ID
     * @param array $data 请假数据
     * @return bool 是否成功
     */
    public function updateLeave($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除请假申请
     * @param int $id 请假ID
     * @return bool 是否成功
     */
    public function deleteLeave($id) {
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 审批请假申请
     * @param int $id 请假ID
     * @param string $status 状态 (approved/rejected)
     * @param int $approvedBy 审批人ID
     * @param string $comment 审批意见
     * @return bool 是否成功
     */
    public function approveLeave($id, $status, $approvedBy, $comment = '') {
        $data = [
            'status' => $status,
            'approved_by' => $approvedBy,
            'approved_at' => date('Y-m-d H:i:s')
        ];
        
        // 如果有审批意见，添加到数据中
        if (!empty($comment)) {
            $data['comment'] = $comment;
        }
        
        return $this->updateLeave($id, $data);
    }
    
    /**
     * 获取教师的待审批请假申请
     * @param int $teacherId 教师ID
     * @param int $limit 限制数量
     * @return array 请假申请列表
     */
    public function getPendingLeavesByTeacher($teacherId, $limit = 10) {
        $sql = "SELECT l.*, s.student_number, u.name as student_name, u.phone as student_phone,
                       c.name as course_name, sch.day_of_week, sch.start_time, sch.end_time,
                       cr.name as classroom_name
                FROM {$this->table} l 
                JOIN students s ON l.student_id = s.id 
                JOIN users u ON s.user_id = u.id 
                JOIN courses c ON l.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                LEFT JOIN schedules sch ON l.schedule_id = sch.id 
                LEFT JOIN classrooms cr ON sch.classroom_id = cr.id 
                WHERE t.id = ? AND l.status = 'pending'
                ORDER BY l.created_at DESC 
                LIMIT {$limit}";
        return $this->db->select($sql, [$teacherId]);
    }
    
    /**
     * 获取学生的请假申请
     * @param int $studentId 学生ID
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 请假申请列表
     */
    public function getLeavesByStudent($studentId, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT l.*, c.name as course_name, t.subject, tu.name as teacher_name,
                       cr.name as classroom_name, sch.day_of_week, sch.start_time, sch.end_time,
                       au.name as approved_by_name
                FROM {$this->table} l 
                JOIN courses c ON l.course_id = c.id 
                JOIN teachers t ON c.teacher_id = t.id 
                JOIN users tu ON t.user_id = tu.id 
                LEFT JOIN schedules sch ON l.schedule_id = sch.id 
                LEFT JOIN classrooms cr ON sch.classroom_id = cr.id 
                LEFT JOIN users au ON l.approved_by = au.id 
                WHERE l.student_id = ?
                ORDER BY l.created_at DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$studentId]);
    }
    
    /**
     * 获取学生请假申请总数
     * @param int $studentId 学生ID
     * @return int 总数
     */
    public function getLeaveCountByStudent($studentId) {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table} WHERE student_id = ?", [$studentId]);
    }
    
    /**
     * 检查是否存在冲突的请假申请
     * @param int $studentId 学生ID
     * @param int $courseId 课程ID
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @param int $excludeId 排除的请假ID（用于编辑时）
     * @return bool 是否存在冲突
     */
    public function hasConflictingLeave($studentId, $courseId, $startDate, $endDate, $excludeId = null) {
        $where = "student_id = ? AND course_id = ? AND status != 'rejected' 
                  AND ((start_date <= ? AND end_date >= ?) OR (start_date <= ? AND end_date >= ?) 
                  OR (start_date >= ? AND end_date <= ?))";
        $params = [$studentId, $courseId, $startDate, $startDate, $endDate, $endDate, $startDate, $endDate];
        
        if ($excludeId) {
            $where .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $count = $this->db->count("SELECT COUNT(*) FROM {$this->table} WHERE {$where}", $params);
        return $count > 0;
    }
}
