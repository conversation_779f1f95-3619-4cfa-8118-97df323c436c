# ⚠️ 时间获取规则 - 重要提醒 ⚠️

## 🚨 绝对禁止硬编码日期！🚨

### 核心要求
**每次需要当前日期时，必须使用动态获取的方式，绝对禁止使用硬编码日期！**

### 正确的时间获取方法

#### PHP项目（推荐）
```bash
php -r "echo date('Y-m-d H:i:s');"
```

#### Python项目
```bash
python -c "import datetime; print(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))"
```

### 统一格式要求
- **标准格式**: `YYYY-MM-DD HH:MM:SS`
- **示例**: `2025-08-07 12:28:59`

### 必须使用动态时间的场景

1. **记录operateLog.md时**
   - 每次记录操作日志的时间戳
   - 章节标题中的时间

2. **代码注释中的修订日期**
   - 修改代码时标注的日期
   - 文件创建时间注释

3. **创建文件时的时间记录**
   - 新建文件的创建时间
   - 配置文件中的时间戳

4. **任何需要时间标记的场合**
   - 日志记录
   - 版本标记
   - 状态更新

### 错误示例（绝对禁止）
❌ `2025-01-08` （硬编码错误日期）
❌ `2025-01-01` （硬编码日期）
❌ 任何不是通过命令获取的日期

### 正确示例
✅ 先执行 `php -r "echo date('Y-m-d H:i:s');"` 获取 `2025-08-07 12:28:59`
✅ 然后使用获取到的准确时间

### 为什么这很重要？
1. **准确性**: 确保所有时间记录的准确性
2. **一致性**: 保持项目中时间格式的统一
3. **可追溯性**: 正确的时间戳有助于问题追踪
4. **专业性**: 体现开发过程的严谨性

### 检查清单
在每次操作前，请确认：
- [ ] 是否需要记录时间？
- [ ] 是否已经获取了当前准确时间？
- [ ] 时间格式是否正确？
- [ ] 是否避免了硬编码日期？

---

**创建时间**: 2025-08-07 12:28:59  
**创建者**: Augment Agent  
**目的**: 防止AI助手忘记使用准确的当前时间
