<?php

define('DYH', "'");
define('SYH', '"');

function is_mobile_request()
{
    $ui_mode=v('ui');
    if($ui_mode=='mobile'){
        return true;
    }
    $_SERVER['ALL_HTTP'] = isset($_SERVER['ALL_HTTP']) ? $_SERVER['ALL_HTTP'] : '';
 
    $mobile_browser = '0';
 
    if(preg_match('/(up.browser|up.link|mmp|symbian|smartphone|midp|wap|phone|iphone|ipad|ipod|android|xoom)/i', strtolower($_SERVER['HTTP_USER_AGENT'])))
        $mobile_browser++;
 
    if((isset($_SERVER['HTTP_ACCEPT'])) and (strpos(strtolower($_SERVER['HTTP_ACCEPT']),'application/vnd.wap.xhtml+xml') !== false))
        $mobile_browser++;
 
    if(isset($_SERVER['HTTP_X_WAP_PROFILE']))
        $mobile_browser++;
 
    if(isset($_SERVER['HTTP_PROFILE']))
        $mobile_browser++;
 
    $mobile_ua = strtolower(substr($_SERVER['HTTP_USER_AGENT'],0,4));
    $mobile_agents = array(
                        'w3c ','acs-','alav','alca','amoi','audi','avan','benq','bird','blac',
                        'blaz','brew','cell','cldc','cmd-','dang','doco','eric','hipt','inno',
                        'ipaq','java','jigs','kddi','keji','leno','lg-c','lg-d','lg-g','lge-',
                        'maui','maxo','midp','mits','mmef','mobi','mot-','moto','mwbp','nec-',
                        'newt','noki','oper','palm','pana','pant','phil','play','port','prox',
                        'qwap','sage','sams','sany','sch-','sec-','send','seri','sgh-','shar',
                        'sie-','siem','smal','smar','sony','sph-','symb','t-mo','teli','tim-',
                        'tosh','tsm-','upg1','upsi','vk-v','voda','wap-','wapa','wapi','wapp',
                        'wapr','webc','winw','winw','xda','xda-'
                        );
 
    if(in_array($mobile_ua, $mobile_agents))
        $mobile_browser++;
 
    if(strpos(strtolower($_SERVER['ALL_HTTP']), 'operamini') !== false)
        $mobile_browser++;
 
    // Pre-final check to reset everything if the user is on Windows
    if(strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows') !== false)
        $mobile_browser=0;
 
    // But WP7 is also Windows, with a slightly different characteristic
    if(strpos(strtolower($_SERVER['HTTP_USER_AGENT']), 'windows phone') !== false)
        $mobile_browser++;
 
    if($mobile_browser>0)
        return true;
    else
        return false;
}

function need_login($from_fn=''){
    //访问其他功能需要先登录
    if(IS_LOGIN){
        //skip
    }else{
        //redirect('./?c=login');
        if(DEBUG_MODE ) echo 'need login!';
        $pagePath='提示';
        $type='提示';
        show_msg($type,$from_fn,"需要登录才能继续！（可能因为之前的登录已经超时）");
        exit;
    }
}

function html_log($str){
	echo '<!-- '.$str.' -->'.PHP_EOL;
}

function guid2uuid($v1='') {
    if(empty($v1)){
        $v1=guid();
    }

    $uuid=str_replace(chr(45),'',$v1);
    $uuid=str_replace(chr(123),'',$uuid);
    $uuid=str_replace(chr(125),'',$uuid);

    return $uuid;
}

function guid() {
    if (function_exists('com_create_guid')) {
        return com_create_guid();
    } else {
        mt_srand((double)microtime() * 10000); //optional for php 4.2.0 and up.
        $charid = strtoupper(md5(uniqid(rand(), true)));
        $hyphen = chr(45); // "-"
        $uuid = chr(123) // "{"
            .substr($charid, 0, 8).$hyphen
            .substr($charid, 8, 4).$hyphen
            .substr($charid, 12, 4).$hyphen
            .substr($charid, 16, 4).$hyphen
            .substr($charid, 20, 12)
            .chr(125); // "}"
    }
    return $uuid;
}

function uuid($hyphen='',$prefix = '') {
//Example of using the function -
//Using without prefix.
//echo uuid(); //Returns like ‘1225c695-cfb8-4ebb-aaaa-80da344e8352′   
//Using with prefix
//echo uuid('-','urn:uuid:');//Returns like ‘urn:uuid:1225c695-cfb8-4ebb-aaaa-80da344e8352′
    $chars = md5(uniqid(mt_rand(), true));
    $uuid = substr($chars, 0, 8).$hyphen
    .substr($chars, 8, 4).$hyphen
    .substr($chars, 12, 4).$hyphen
    .substr($chars, 16, 4).$hyphen
    .substr($chars, 20, 12);
    return $prefix.$uuid;
}


function kset( $key , $value )
{
    $sql = "REPLACE INTO __KEYVALUE__ ( `key` , `value` ) VALUES ( '" . s($key) . "' , '" . s($value) . "' )";
    $sql=sql_table_alias($sql);
    //echo $sql;
    run_sql( $sql );
}

function kget( $key )
{
    $sql = "SELECT `value` FROM __KEYVALUE__ WHERE `key` = '" . s($key) . "' LIMIT 1" ;
    $sql=sql_table_alias($sql);
    return get_var($sql );
}

function kdel( $key )
{
    $sql = "DELETE FROM __KEYVALUE__ WHERE `key` = '" . s($key) . "' LIMIT 1" ;
    $sql=sql_table_alias($sql);
    run_sql($sql);
}


function get_new_obj_id(){
    $key='all_site_obj_id';
    $value=kget($key);
    if(!empty($value) && intval($value)>0){
        $max = intval($value)+1;  
    }else{
        $max=1;
    }
    kset( $key , $max );
    return $max;
}

function ext_log($log,$fix='()'){
    if (DEBUG_MODE){
        return $log.$fix;
    }
    return '';
}

function show_path($path){
    //$tmp=explode(DS.'WWW'.DS,$path);
    $tmp=explode(DS.'public'.DS,$path);
    return $tmp[count($tmp)-1];
}


function kis_safe_file_path($path){
    //只输出public后面的内容
    $tmpArr=explode(DS,$path);
    $isShow=false;
    $retHtml='';
    foreach ($tmpArr as $key => $value) {
        if($value=='public'){
            $isShow=true;
        }else if($isShow){
            if( empty($retHtml)){
                $retHtml=$value;
            }else{
                $retHtml.='.'.$value;
            }
        }
    }
    return $retHtml;
}

function debug_htm($msg){
    return '<!-- <div class="debug_msg" >DEBUG:'.$msg.'</div> -->'.PHP_EOL;
}


function std_fmt_today(){
    $time_stamp=strtotime("now");
    return date("Y-m-d",$time_stamp);
}

function std_fmt_time(){
    $time_stamp=strtotime("now");
    return date("H:i:s",$time_stamp);
}

function std_fmt_now(){
    $time_stamp=strtotime("now");
    return date("Y-m-d H:i:s",$time_stamp);
}


function sql_table_alias($sql){
    // 替换SQL语句中的表名
    $sql = preg_replace_callback('/__([A-Z0-9_-]+)__/sU', function($match) {
        return '`' . config('DB_PREFIX') . strtolower($match[1]) . '`';
    }, $sql);
    return $sql;
}



/////////////////////////



function is_nil_xx($obj,$key=null){
    if(is_array($obj) && !empty($key)){
        //echo 111;
        if(!isset($obj[$key]) ){
            return true;
        }
        $tmp=trim($obj[$key]);
    }else{
        $tmp=trim($obj);
    }
    //var_dump($tmp);

    if(empty($tmp) || strtolower($tmp)=='nil' || $tmp=="无"){
        return true;
    }
    return false;
}

function cut_key($str,$delimiter=1){
    if($delimiter==1){
        $delimiter_s='[';
        $delimiter_e=']';
    }else{
        $delimiter_s='{';
        $delimiter_e='}';
    }
    $tmpArr3=explode($delimiter_e,$str);
    //var_dump($tmpArr3);
    $tmpArr4=explode($delimiter_s,$tmpArr3[0]);
    //var_dump($tmpArr4);
    $ret=$tmpArr4[1];
    return $ret;
}

function cut_val($str,$key,$delimiter=1){
    if($delimiter==1){
        $delimiter_s='[';
        $delimiter_e=']';
    }else{
        $delimiter_s='{';
        $delimiter_e='}';
    }
    $tmpArr3=explode($key.$delimiter_e,$str);
    $ret=$tmpArr3[1];
    return $ret;
}


function html_redirected_in_x_seconds($url,$x=3){

    echo '<p>稍等 '.intval($x).' 秒后自动跳转</p>
                <script>
                    var timer = setTimeout(function() {
                        window.location="'.$url.'";
                    }, '.intval($x).'*1000);
                </script>';
    die('<!-- '.__METHOD__.'() exit. -->');
    return false;   
}

function randColor(){
    $colors = array();
    for($i = 0;$i<6;$i++){
        $colors[] = dechex(rand(0,15));
    }
    return implode('',$colors);
}


function html_msg_url($url,$msg=null,$x=3){
    /*
    echo '<!DOCTYPE html>
<html lang="en">
<head>
</head>
<body style="text-align:center;background-color:#'.randColor().'" >';
*/
    if($msg!=null){
        if(is_array($msg)){
            echo '<pre>';
            var_dump($msg);
            echo '</pre>';
        }else{
            echo '<p>'.$msg.'</p>';    
        }
        
        echo '<br/>'.PHP_EOL;
        //echo '<br/>'.PHP_EOL;
        echo '<br/>'.PHP_EOL;
    }

    echo '<p>请稍候，<span id="totalSecond" style="color:red">'.intval($x).'</span>秒后自动跳转…</p>';


    echo '<p><a href="'.$url.'" target="_self">立即跳转</p>';
                // '<script>
                //  var timer = setTimeout(function() {
                //      window.location="'.$url.'";
                //  }, '.intval($x).'*1000);
                // </script>';
    $js=<<<STRING
<script language="javascript" type="text/javascript">
    var second = document.getElementById('totalSecond').textContent;

    if (navigator.appName.indexOf("Explorer") > -1) {
        second = document.getElementById('totalSecond').innerText;
    } else {
        second = document.getElementById('totalSecond').textContent;
    }

    setInterval("redirect()", 1000);

    function redirect() {
        if (second < 0) {
            location.href = '{%goto%}';
        } else {
            if (navigator.appName.indexOf("Explorer") > -1) {
                document.getElementById('totalSecond').innerText = second--;
            } else {
                document.getElementById('totalSecond').textContent = second--;
            }
        }
    }
</script>
STRING;
    $js=str_replace('{%goto%}',$url,$js);
    echo $js;
    //echo '</body></html>';
    die('<!-- '.__METHOD__.'() exit. -->');
    return false;   
}


function kis_split($str1,$str2){
    $tmp_Arr=explode($str1,$str2);
    return $tmp_Arr;
}


/* 获得当前页面URL开始 */

function getCurPageFullURL() {
    $pageURL = 'http';
    if ($_SERVER["HTTPS"] == "on") {    // 如果是SSL加密则加上“s”
        $pageURL .= "s";
    }
    $pageURL .= "://";
    if ($_SERVER["SERVER_PORT"] != "80") {
        $pageURL .= $_SERVER["SERVER_NAME"].":".$_SERVER["SERVER_PORT"].$_SERVER["REQUEST_URI"];
    } else {
        $pageURL .= $_SERVER["SERVER_NAME"].$_SERVER["REQUEST_URI"];
    }

    return $pageURL;
}

function getCurPageScriptURL() {
    //var_dump($_SERVER);
    $protocol = ((!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') || $_SERVER['SERVER_PORT'] == 443) ? "https://": "http://";

    $pageURL = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['SCRIPT_NAME'];

    //echo $url;//输出完整的url

    return $pageURL;
}

function fn_arr2str($obj,$fgf=';'){

    if(is_array($obj)){
        return implode($fgf,$obj);
    }
    return $obj;
}

function fn_inStr($str_full,$str_sub){
    if(stripos($str_full,$str_sub)!==false){
        return true;
    }
    return false;
}


function make_input_button_html($title,$value,$op_class, $type='btn-danger'){

    //return '<input type="text" name="'.$title.'" class="btn '.$type.'" value="'.$value.'">';
    //<button type="button" class="btn btn-danger">Danger</button>

    return '<button type="button" alt="'.$title.'" class="btn '.$type.' '.$op_class.'" data_val="'.$value.'">'.$title.'</button>';

}

function ui_btn_class($op){
/*
<div class="card-body">
    <a class="btn btn-primary" href="#" role="button">Link</a>
    <button class="btn btn-danger" type="submit">Button</button>
    <input class="btn btn-info" type="button" value="Input">
    <input class="btn btn-success" type="submit" value="Submit">
    <input class="btn btn-warning" type="reset" value="Reset">
</div>
*/
    switch ($op) {
        case 'del':
        case 'danger':
            $op_class='btn-danger';        
            break;

        case 'ok':
        case 'success':
            $op_class='btn-success';
            break;

        case 'err':
        case 'error':
        case 'warning':
            $op_class='btn-warning';
            break;

        case 'info':
            $op_class='btn-info';
            break;

        default:
            $op_class='btn-primary';
            break;
    }

    return $op_class;
}

function make_op_button_html($op_title,$msg,$value,$op_class, $btn_class=''){
    //<button type="button" class="btn btn-danger">Danger</button>
    return '<button type="button" alt="'.$msg.'" class="btn '.ui_btn_class($btn_class).' '.$op_class.'" data_val="'.$value.'" data_msg="'.$msg.'">'.$op_title.'</button>';
}


function api_head(){
    date_default_timezone_set("Asia/Shanghai");

    $action = empty( $_REQUEST['ajax'] ) ? '' : strtolower( $_REQUEST['ajax'] );
    if($action){
        if($action=='js'){
            $ContentType ='application/x-javascript';
        }else{
            $ContentType ='text/plain';
        }
    }else{
        $ContentType ='text/html';
    }
    header("Content-Type:".$ContentType.";charset=utf-8");

}


function html_radio_var($ret_info,$key,$val){//($ret_info,'gender','男')
    if(isset($ret_info[$key])){
        $tmp_val=$ret_info[$key];
        if($tmp_val==$val){
            return ' checked="checked" ';
        }
        
    }
    return '';
}

function html_option_var($ret_info,$key,$val){//($ret_info,'dept_id','3')
    if(isset($ret_info[$key])){
        $tmp_val=$ret_info[$key];
        if($tmp_val==$val){
            return ' selected ';
        }
        
    }
    return '';
}



function getRandPass($length = 7){  
    $password = '';  
    //将你想要的字符添加到下面字符串中，默认是数字0-9和26个英文字母  
    //$chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"; 
    $chars = "23456789abcdefghjkmnpqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*"; 
    $char_len = strlen($chars);  
    for($i=0;$i<$length;$i++){  
        $loop = mt_rand(0, ($char_len-1));  
        //将这个字符串当作一个数组，随机取出一个字符，并循环拼接成你需要的位数  
        $password .= $chars[$loop];  
    } 
    $kk=chr(mt_rand(65,90));
    return $kk.$password;  
} 

/*
    [$a, $b]=ks_fn_config2list('hello|world|how|are|you');
    echo $a; // 输出 hello
    echo $b; // 输出 world|how|are|you
*/ 
function ks_fn_config2list($str){
    //$str = 'hello|world|how|are|you';
    $arr = explode('|', $str);

    if (count($arr) > 1) {
      $a = $arr[0];
      $b = implode('|', array_slice($arr, 1));
    } else {
      $a = $str;
      $b = '';
    }
      
    return [$a,$b];
}

function fn_remove_eol($ret_html){
    
    $ret_html=str_replace('\r',PHP_EOL, $ret_html);
    $ret_html=str_replace('\n',PHP_EOL, $ret_html);
    $ret_html=str_replace(PHP_EOL.PHP_EOL,PHP_EOL, $ret_html);

    $list=explode(PHP_EOL,$ret_html);
    $ret_html='';
    foreach ($list as $key => $value) {
        $ret_html.=trim($value);
    }

    return strval($ret_html);
}


function mergeUrl($website, $referralKey,$referralCode) {
    $url = rtrim($website, '?&/');
    if (strpos($url, '?') !== false) {
        $url .= '&';
    } else {
        $url .= '/?';
    }    
    $url .= $referralKey."=" . $referralCode;
    return $url;
}

function simple_safe(&$obj){
    if(is_array($obj)){
        foreach ($obj as $key => $value) {
            if(isset($value['password'])){
                $obj[$key]['password']='110';
            }
        }
    }
    if(isset($obj['password'])){
        $obj['password']='110';
    }
}
function html_dump_log($obj){
    echo '<!-- '.PHP_EOL;
    simple_safe($obj);
    var_dump($obj);
    echo PHP_EOL.' -->'.PHP_EOL;
}




function html_msg_url_xp($url,$msg=null,$x=3,$to_parent=true){
    /*
    echo '<!DOCTYPE html>
<html lang="en">
<head>
</head>
<body style="text-align:center;background-color:#'.randColor().'" >';
*/
    if($msg!=null){
        if(is_array($msg)){
            echo '<pre>';
            var_dump($msg);
            echo '</pre>';
        }else{
            echo '<p>'.$msg.'</p>';    
        }
        
        echo '<br/>'.PHP_EOL;
        //echo '<br/>'.PHP_EOL;
        echo '<br/>'.PHP_EOL;
    }

    echo '<p>请稍候，<span id="totalSecond" style="color:red">'.intval($x).'</span>秒后自动跳转…</p>';


    echo '<p><a href="'.$url.'" target="_self">立即跳转</p>';
                // '<script>
                //  var timer = setTimeout(function() {
                //      window.location="'.$url.'";
                //  }, '.intval($x).'*1000);
                // </script>';
    $js=<<<STRING
<script language="javascript" type="text/javascript">
    var second = document.getElementById('totalSecond').textContent;

    if (navigator.appName.indexOf("Explorer") > -1) {
        second = document.getElementById('totalSecond').innerText;
    } else {
        second = document.getElementById('totalSecond').textContent;
    }

    setInterval("redirect()", 1000);

    function redirect() {
        if (second < 0) {
            {%to_parent%}location.href = '{%goto%}';
        } else {
            if (navigator.appName.indexOf("Explorer") > -1) {
                document.getElementById('totalSecond').innerText = second--;
            } else {
                document.getElementById('totalSecond').textContent = second--;
            }
        }
    }
</script>
STRING;
    $js=str_replace('{%goto%}',$url,$js);
    if($to_parent){
        $js=str_replace('{%to_parent%}','parent.',$js);
    }else{
        $js=str_replace('{%to_parent%}','',$js);
    }
    echo $js;
    //echo '</body></html>';
    die('<!-- '.__METHOD__.'() exit. -->');
    return false;   
}
