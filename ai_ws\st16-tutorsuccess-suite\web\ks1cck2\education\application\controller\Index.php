<?php
/**
 * 首页控制器
 * 修订日期：2025-01-22
 */

namespace app\controller;

class Index
{
    /**
     * 首页
     */
    public function index()
    {
        // 检查是否已登录
        if (!isset($_SESSION[SESSION_KEY]['user']['id'])) {
            // 未登录，显示登录页面或跳转到登录
            $this->showLoginPage();
            return;
        }

        $current_user = get_current_login_user();
        $user_id = $current_user['id'];
        $user_roles = get_user_roles($user_id);

        // 根据用户角色跳转到对应页面
        if (in_array(ROLE_ADMIN, $user_roles)) {
            header('Location: admin/');
            exit;
        } elseif (in_array(ROLE_TEACHER, $user_roles)) {
            header('Location: teacher/');
            exit;
        } elseif (in_array(ROLE_STUDENT, $user_roles)) {
            header('Location: student/');
            exit;
        } else {
            // 没有角色，显示错误页面
            $this->showErrorPage('您还没有分配角色，请联系管理员');
        }
    }

    /**
     * 开发测试页面
     */
    public function dev()
    {
        include __DIR__ . '/../view/index/dev.php';
    }

    /**
     * 显示登录页面
     */
    private function showLoginPage()
    {
        include '../application/view/index/login.php';
    }

    /**
     * 显示错误页面
     */
    private function showErrorPage($message)
    {
        include '../application/view/index/error.php';
    }
}
