<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "sm")) {
    $conn->update('main',['smprovider'=>$_REQUEST["smprovider"],'smaddress'=>$_REQUEST["smaddress"],'smusername'=>$_REQUEST["smusername"],'smpassword'=>$_REQUEST["smpassword"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}
if (isset($_REQUEST["action"]) && ($_REQUEST["action"] == "mail")) {
    $conn->update('main',['mailaddress'=>$_REQUEST["mailaddress"],'mailsender'=>$_REQUEST["mailsender"],'mailaccount'=>$_REQUEST["mailaccount"],'mailpassword'=>$_REQUEST["mailpassword"]],['id'=>1]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('修改成功！');</script>";
    exit;
}

$row = $conn->get('main','*',['id'=>1]);
$smprovider = $row["smprovider"];
$smaddress = $row["smaddress"];
$smusername = $row["smusername"];
$smpassword = $row["smpassword"];
$mailaddress = $row["mailaddress"];
$mailsender = $row["mailsender"];
$mailaccount = $row["mailaccount"];
$mailpassword = $row["mailpassword"];

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>第三方接口设置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />


    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-print"></i> 系统管理</li>
                <li class="active">第三方接口设置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:10px">
            <div class="page-content-area" style="float:left;display:inline;margin:10px;width:450px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
                <div style="margin:10px;"><b>短信平台设置</b></div>
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" target=temp>
                            <input type=hidden name="action" value="sm">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">选择平台：</label>

                                <div class="col-lg-6" style="text-align:left;">
                                    <select name=smprovider style="width:100%;">
                                        <option value="未对接">未对接(测试)</option>
                                        <option value="创蓝" <?php if ($smprovider == '创蓝') echo "selected"; ?>>创蓝</option>
                                        <option value="翼客通" <?php if ($smprovider == '翼客通') echo "selected"; ?>>翼客通</option>
                                        <option value="中国移动云MAS" <?php if ($smprovider == '中国移动云MAS') echo "selected"; ?>>中国移动云MAS</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">接口地址：</label>

                                <div class="col-lg-6">
                                    <input type="text" name="smaddress" class="bg-focus form-control" value="<?php echo $smaddress; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">用户名：</label>

                                <div class="col-lg-6">
                                    <input type="text" name="smusername" class="bg-focus form-control" value="<?php echo $smusername; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">密码：</label>

                                <div class="col-lg-6">
                                    <input type="password" name="smpassword" class="bg-focus form-control" value="<?php echo $smpassword; ?>">
                                </div>
                            </div>
                            <div class="form-group" align="center" style="padding:10px;">
                                <button type="submit" class="btn btn-primary">确认设置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="page-content-area" style="float:left;display:inline;margin:10px;width:450px;background:#F5F5F5;border:1px solid silver;border-radius:10px;text-align:center;font-size:16px;padding:10px;">
                <div style="margin:10px;"><b>邮件服务设置</b></div>
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" target=temp>
                            <input type=hidden name="action" value="mail">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">邮件服务器地址：</label>
                                <div class="col-lg-6">
                                    <input type="text" name="mailaddress" class="bg-focus form-control" value="<?php echo $mailaddress; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">发件人邮箱：</label>
                                <div class="col-lg-6">
                                    <input type="text" name="mailsender" class="bg-focus form-control" value="<?php echo $mailsender; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">发件人账号：</label>
                                <div class="col-lg-6">
                                    <input type="text" name="mailaccount" class="bg-focus form-control" value="<?php echo $mailaccount; ?>">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-4 control-label">发件人密码：</label>
                                <div class="col-lg-6">
                                    <input type="password" name="mailpassword" class="bg-focus form-control" value="<?php echo $mailpassword; ?>">
                                </div>
                            </div>
                            <div class="form-group" align="center" style="padding:10px;">
                                <button type="submit" class="btn btn-primary">确认设置</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </div>
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>