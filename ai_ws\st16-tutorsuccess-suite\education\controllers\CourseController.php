<?php
/**
 * 课程控制器
 * 处理课程相关的请求
 */
class CourseController extends Controller {
    /**
     * 课程模型实例
     * @var CourseModel
     */
    private $courseModel;
    
    /**
     * 教师模型实例
     * @var TeacherModel
     */
    private $teacherModel;
    
    /**
     * 学生模型实例
     * @var StudentModel
     */
    private $studentModel;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        $this->courseModel = new CourseModel();
        $this->teacherModel = new TeacherModel();
        $this->studentModel = new StudentModel();
        
        // 检查用户是否登录，除了特定方法外
        $noAuthMethods = ['index', 'view', 'search'];
        if (!in_array($this->getMethod(), $noAuthMethods)) {
            $this->checkLogin();
        }
    }
    
    /**
     * 课程列表页
     */
    public function index() {
        // 获取分页参数
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // 获取课程列表和总数
        $courses = $this->courseModel->getCourses($page, $perPage);
        $totalCourses = $this->courseModel->getCourseCount();
        
        // 计算总页数
        $totalPages = ceil($totalCourses / $perPage);
        
        // 设置视图变量
        $this->setVar('courses', $courses);
        $this->setVar('currentPage', $page);
        $this->setVar('totalPages', $totalPages);
        $this->setVar('totalCourses', $totalCourses);
        
        // 渲染视图
        $this->render('course/index');
    }
    
    /**
     * 查看课程详情
     */
    public function view() {
        // 获取课程ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('course/index');
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($id);
        
        if (!$course) {
            $this->setVar('error', '课程不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取课程的教师信息
        $teacher = $this->teacherModel->getTeacherById($course['teacher_id']);
        
        // 获取课程的学生列表
        $students = $this->studentModel->getStudentsByCourse($id);
        
        // 获取课程的课表
        $schedules = $this->courseModel->getCourseSchedules($id);
        
        // 设置视图变量
        $this->setVar('course', $course);
        $this->setVar('teacher', $teacher);
        $this->setVar('students', $students);
        $this->setVar('schedules', $schedules);
        
        // 渲染视图
        $this->render('course/view');
    }
    
    /**
     * 添加课程页面
     */
    public function add() {
        // 检查权限
        $this->checkPermission();
        
        // 获取所有教师
        $teachers = $this->teacherModel->getTeachers();
        
        // 设置视图变量
        $this->setVar('teachers', $teachers);
        
        // 渲染视图
        $this->render('course/add');
    }
    
    /**
     * 处理添加课程请求
     */
    public function create() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('course/add');
        }
        
        // 获取表单数据
        $name = $this->getPost('name');
        $description = $this->getPost('description');
        $teacherId = (int)$this->getPost('teacher_id');
        $price = (float)$this->getPost('price');
        $capacity = (int)$this->getPost('capacity');
        $startDate = $this->getPost('start_date');
        $endDate = $this->getPost('end_date');
        
        // 验证数据
        if (empty($name) || empty($description) || empty($teacherId) || empty($startDate) || empty($endDate)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 检查教师是否存在
        $teacher = $this->teacherModel->getTeacherById($teacherId);
        if (!$teacher) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的教师不存在']);
            } else {
                $this->setVar('error', '选择的教师不存在');
                $this->setVar('formData', $_POST);
                $this->add();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'name' => $name,
            'description' => $description,
            'teacher_id' => $teacherId,
            'price' => $price,
            'capacity' => $capacity,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // 创建课程
        $courseId = $this->courseModel->createCourse($data);
        
        if ($courseId) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课程创建成功', 'id' => $courseId]);
            } else {
                redirect('course/view?id=' . $courseId);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程创建失败，请重试']);
            } else {
                $this->setVar('error', '课程创建失败，请重试');
                $this->setVar('formData', $_POST);
                $this->add();
            }
        }
    }
    
    /**
     * 编辑课程页面
     */
    public function edit() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课程ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('course/index');
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($id);
        
        if (!$course) {
            $this->setVar('error', '课程不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取所有教师
        $teachers = $this->teacherModel->getTeachers();
        
        // 设置视图变量
        $this->setVar('course', $course);
        $this->setVar('teachers', $teachers);
        
        // 渲染视图
        $this->render('course/edit');
    }
    
    /**
     * 处理更新课程请求
     */
    public function update() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('course/index');
        }
        
        // 获取课程ID
        $id = (int)$this->getPost('id');
        
        if (!$id) {
            redirect('course/index');
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($id);
        
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程不存在']);
            } else {
                $this->setVar('error', '课程不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 获取表单数据
        $name = $this->getPost('name');
        $description = $this->getPost('description');
        $teacherId = (int)$this->getPost('teacher_id');
        $price = (float)$this->getPost('price');
        $capacity = (int)$this->getPost('capacity');
        $startDate = $this->getPost('start_date');
        $endDate = $this->getPost('end_date');
        
        // 验证数据
        if (empty($name) || empty($description) || empty($teacherId) || empty($startDate) || empty($endDate)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '请填写所有必填字段']);
            } else {
                $this->setVar('error', '请填写所有必填字段');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 检查教师是否存在
        $teacher = $this->teacherModel->getTeacherById($teacherId);
        if (!$teacher) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '选择的教师不存在']);
            } else {
                $this->setVar('error', '选择的教师不存在');
                $this->setVar('formData', $_POST);
                $this->edit();
                return;
            }
        }
        
        // 准备数据
        $data = [
            'name' => $name,
            'description' => $description,
            'teacher_id' => $teacherId,
            'price' => $price,
            'capacity' => $capacity,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 更新课程
        $result = $this->courseModel->updateCourse($id, $data);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课程更新成功']);
            } else {
                redirect('course/view?id=' . $id);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程更新失败，请重试']);
            } else {
                $this->setVar('error', '课程更新失败，请重试');
                $this->setVar('formData', $_POST);
                $this->edit();
            }
        }
    }
    
    /**
     * 删除课程
     */
    public function delete() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课程ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的课程ID']);
            } else {
                redirect('course/index');
            }
            return;
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($id);
        
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程不存在']);
            } else {
                $this->setVar('error', '课程不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 检查是否有学生已报名该课程
        $students = $this->studentModel->getStudentsByCourse($id);
        if (count($students) > 0) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '该课程已有学生报名，无法删除']);
            } else {
                $this->setVar('error', '该课程已有学生报名，无法删除');
                $this->render('error/index');
            }
            return;
        }
        
        // 删除课程
        $result = $this->courseModel->deleteCourse($id);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '课程删除成功']);
            } else {
                redirect('course/index');
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程删除失败，请重试']);
            } else {
                $this->setVar('error', '课程删除失败，请重试');
                $this->render('error/index');
            }
        }
    }
    
    /**
     * 搜索课程
     */
    public function search() {
        // 获取搜索关键词
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        
        if (empty($keyword)) {
            redirect('course/index');
        }
        
        // 获取分页参数
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;
        
        // 搜索课程
        $courses = $this->courseModel->searchCourses($keyword, $page, $perPage);
        $totalCourses = $this->courseModel->getSearchCount($keyword);
        
        // 计算总页数
        $totalPages = ceil($totalCourses / $perPage);
        
        // 设置视图变量
        $this->setVar('courses', $courses);
        $this->setVar('keyword', $keyword);
        $this->setVar('currentPage', $page);
        $this->setVar('totalPages', $totalPages);
        $this->setVar('totalCourses', $totalCourses);
        
        // 渲染视图
        $this->render('course/search');
    }
    
    /**
     * 管理课程学生页面
     */
    public function students() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课程ID
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        
        if (!$id) {
            redirect('course/index');
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($id);
        
        if (!$course) {
            $this->setVar('error', '课程不存在');
            $this->render('error/index');
            return;
        }
        
        // 获取课程的学生列表
        $students = $this->studentModel->getStudentsByCourse($id);
        
        // 获取未报名该课程的学生
        $availableStudents = $this->studentModel->getStudentsNotInCourse($id);
        
        // 设置视图变量
        $this->setVar('course', $course);
        $this->setVar('students', $students);
        $this->setVar('availableStudents', $availableStudents);
        
        // 渲染视图
        $this->render('course/students');
    }
    
    /**
     * 添加学生到课程
     */
    public function addStudent() {
        // 检查权限
        $this->checkPermission();
        
        // 检查请求方法
        if (!$this->isPost()) {
            redirect('course/index');
        }
        
        // 获取课程ID和学生ID
        $courseId = (int)$this->getPost('course_id');
        $studentId = (int)$this->getPost('student_id');
        
        if (!$courseId || !$studentId) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的课程或学生ID']);
            } else {
                redirect('course/index');
            }
            return;
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($courseId);
        
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程不存在']);
            } else {
                $this->setVar('error', '课程不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($studentId);
        
        if (!$student) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '学生不存在']);
            } else {
                $this->setVar('error', '学生不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 检查学生是否已经报名该课程
        if ($this->courseModel->isStudentEnrolled($courseId, $studentId)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '该学生已报名此课程']);
            } else {
                redirect('course/students?id=' . $courseId);
            }
            return;
        }
        
        // 检查课程是否已满
        $enrolledCount = $this->courseModel->getEnrolledStudentCount($courseId);
        if ($enrolledCount >= $course['capacity']) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程已满，无法添加更多学生']);
            } else {
                $this->setVar('error', '课程已满，无法添加更多学生');
                $this->render('error/index');
            }
            return;
        }
        
        // 准备数据
        $data = [
            'course_id' => $courseId,
            'student_id' => $studentId,
            'enrollment_date' => date('Y-m-d'),
            'status' => 'active'
        ];
        
        // 添加学生到课程
        $result = $this->courseModel->addStudentToCourse($data);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '学生已成功添加到课程']);
            } else {
                redirect('course/students?id=' . $courseId);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '添加学生失败，请重试']);
            } else {
                $this->setVar('error', '添加学生失败，请重试');
                $this->render('error/index');
            }
        }
    }
    
    /**
     * 从课程中移除学生
     */
    public function removeStudent() {
        // 检查权限
        $this->checkPermission();
        
        // 获取课程ID和学生ID
        $courseId = isset($_GET['course_id']) ? (int)$_GET['course_id'] : 0;
        $studentId = isset($_GET['student_id']) ? (int)$_GET['student_id'] : 0;
        
        if (!$courseId || !$studentId) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '无效的课程或学生ID']);
            } else {
                redirect('course/index');
            }
            return;
        }
        
        // 获取课程信息
        $course = $this->courseModel->getCourseById($courseId);
        
        if (!$course) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '课程不存在']);
            } else {
                $this->setVar('error', '课程不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 获取学生信息
        $student = $this->studentModel->getStudentById($studentId);
        
        if (!$student) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '学生不存在']);
            } else {
                $this->setVar('error', '学生不存在');
                $this->render('error/index');
            }
            return;
        }
        
        // 检查学生是否已经报名该课程
        if (!$this->courseModel->isStudentEnrolled($courseId, $studentId)) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '该学生未报名此课程']);
            } else {
                redirect('course/students?id=' . $courseId);
            }
            return;
        }
        
        // 从课程中移除学生
        $result = $this->courseModel->removeStudentFromCourse($courseId, $studentId);
        
        if ($result) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'success', 'message' => '学生已成功从课程中移除']);
            } else {
                redirect('course/students?id=' . $courseId);
            }
        } else {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '移除学生失败，请重试']);
            } else {
                $this->setVar('error', '移除学生失败，请重试');
                $this->render('error/index');
            }
        }
    }
    
    /**
     * 检查用户权限
     * 只有管理员和教师可以管理课程
     */
    private function checkPermission() {
        if (!isset($_SESSION['user']) || ($_SESSION['user']['role'] != 'admin' && $_SESSION['user']['role'] != 'teacher')) {
            if ($this->isAjax()) {
                jsonResponse(['status' => 'error', 'message' => '您没有权限执行此操作']);
                exit;
            } else {
                $this->setVar('error', '您没有权限执行此操作');
                $this->render('error/index');
                exit;
            }
        }
    }
}