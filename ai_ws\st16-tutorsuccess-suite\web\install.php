<?php
set_time_limit(0);

echo "<html><head><meta charset=utf-8></head><body style='font-size:18px;margin:0;padding:0;'><p>脚本执行中……</p>";
flush();
require_once('admin/mysqlconn.php');
echo "正在添加和调整字段<br><br>";

$conn->query("SET FOREIGN_KEY_CHECKS=0");

$conn->query("ALTER TABLE `apikey` CHANGE `apikey` `apikey` VARCHAR(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL;");
$conn->query("UPDATE main SET version='6.6.2434'");
echo "已完成数据库结构更新。<br><br>";

echo "本次主要更新了对国内厂商最新模型的支持，增加了对若干国内新大模型厂商的支持，并修复一些已知的bug。<br><br>";

echo "新支持的模型请到后台“模型配置”页面查看。<br><br>";

echo "<a target=_blank href='https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=132'>点击这里</a>查看版本发布说明。<br><br>";


$conn->query("SET FOREIGN_KEY_CHECKS=1");

$filename = $_SERVER['DOCUMENT_ROOT'] . '/install.php';
if (file_exists($filename)) {
    unlink($filename);
}
echo "<p>安装脚本执行完成</p>";
echo "<hr><p style='color:darkgreen'>已完成全部更新动作，更新成功！</p>";
