<?php


define('IN_LOGIN', __METHOD__);

require_once '_ks1.php';
require_once('mysqlconn.php');

// 引入教培系统
require_once 'education/init.php';


// 接收参数
$userrndstr = input('get', 'userrndstr', 's');
$referral_code = input('get', 'referral_code', 's');
if($userrndstr){
    if($referral_code) {
        $sql = "SELECT * FROM user WHERE rndstr = ?s AND DATE(registertime) = CURDATE()"; //判断是否是当天新注册用户
        $sql = prepare($sql, array($userrndstr));
        $result = get_line($sql);
        if($result && isset($result['userid'])){
            $user_id=$result['userid'];
            if($dist_uid= get_user_id_by_ref_code($referral_code)){
                if($user_id==$dist_uid){
                    echo 'dist only';
                    exit;
                }
                if($dist_id=is_distributor($dist_uid)){

                    $sql1 = "SELECT count(*)  FROM ks1_distributor_user WHERE distributor_id = ?s AND user_id = ?s";
                    $sql1 = prepare($sql1, array($dist_id, $user_id));
                    $result1 = get_var($sql1);

                    if (!$result1) {                        
                        $sql2='INSERT INTO `ks1_distributor_user`( `distributor_id`, `user_id`, `memo`) VALUES (?s,?s,?s);';
                        $sql2 = prepare($sql2, array($dist_id,$user_id,$result['registertime']));
                        run_sql($sql2);
                        echo 'add dist='.$dist_id.' user='.$user_id;

                    } else {
                        echo "已存在相同记录";                        
                    }                    
                    exit;
                }else{
                    echo 'dist error';
                }
            }else{
                echo 'referral_code error';
            }

        }else{
            echo 'not new register user ,skip';
        }
    }else{
        echo 'no referral_code ,skip';    
    }    
    exit;
}

$userrndstr = input('get', 'rndstr', 's');
if($userrndstr){
    $sql = "select * from user where rndstr='" . $userrndstr . "' and not isforbidden";
    $data=get_line($sql);
    if($data && isset($data['id'])){
        login_success1($data['userid'], $data['username'], 'user',$data['email']);
    }
}

$action = input('get', 'action', 's');
// 退出登录
if ($action == 'logout') {
    unset($_SESSION['ks1web2']['user']);
    setcookie('fun_auto_name', '', -1);
    setcookie('fun_auto_pass', '', -1);
    redirect('./');
}
// 检查是否已经记住密码
//define('IS_AUTOLOGIN', isset($_COOKIE['fun_auto_name']));
// 处理表单
if (IS_POST) {
    //var_dump($_SESSION);    exit;

    if(1) {
        // 通过表单登录
        $name = input('post', 'name', 's');
        $password = input('post', 'password', 's');

        $current_domain =strtolower($_SERVER['HTTP_HOST']);
        //echo $current_domain;
        
        //var_dump($name);
        //var_dump($password);        

        if ($current_domain=='ai01webdev.shdic.com'){
            //dev mode Not captcha_check
        }else
        if (!captcha_check(input('post', 'captcha', 's'))) {
            display('登录失败，验证码输入有误。', $name);
        }
        if (!input_check('user_email', $name, $error)) {
            display("登录失败，用户邮箱格式有误，要求：$error", $name);
        }
        if (!input_check('aa', $password, $error)) {
            display("登录失败，密码格式有误，要求：$error", $name);
        }
        if (!login_form($name, $password, $error)) {
            display("登录失败，$error");
        }

        $error="未知错误！";
        display("登录失败，$error");
    }
}
// 显示页面
//display(null,'请输入邮箱');

// 通过表单登录
function login_form($name, $password, &$error = '')
{
    // 根据用户名取出密码//and password='" . md5(md5($password) . "chatgpt@2023") . "'
    $sql="SELECT * FROM user where email='" . $name . "' ";
    $data = get_line($sql);

    $password2=md5(md5($password) . "chatgpt@2023");
    //var_dump($password2);
    
    // 判断密码是否正确
    if ($data && isset($data['password']) && ($data['password']==$password || $data['password']==$password2)) {
        // 保存自动登录
        if (input('post', 'auto', 's')) {
            $expires = time() + config('AUTOLOGIN_EXPIRES');
            setcookie('fun_auto_name', $data['email'], $expires);
            setcookie('fun_auto_pass', autologin_cookie($password), $expires);
        }
        login_success1($data['userid'], $data['username'], 'user',$data['email']);
    }
    $error = '用户名或密码错误。';
}


// 登录成功
function login_success1($id, $name, $group,$email)
{
    if(user_login_xp($id)){
        // 检查用户在教培系统中的角色
        $primary_role = get_user_primary_role($id);

        if ($primary_role) {
            // 根据角色跳转到对应的页面
            switch ($primary_role) {
                case ROLE_ADMIN:
                    html_msg_url('education/views/admin/dashboard.php','登录成功，正在跳转到管理员页面...',1);
                    break;
                case ROLE_TEACHER:
                    html_msg_url('education/views/teacher/dashboard.php','登录成功，正在跳转到教师页面...',1);
                    break;
                case ROLE_STUDENT:
                    html_msg_url('education/views/student/dashboard.php','登录成功，正在跳转到学生页面...',1);
                    break;
                default:
                    html_msg_url('index.php','登录成功',1);
                    break;
            }
        } else {
            // 如果没有教培系统角色，跳转到默认页面
            html_msg_url('index.php','登录成功',1);
        }
        exit;
    }else{
        return ;
    }

    // 记住登录状态
    $_SESSION['ks1web2']['user'] = ['id' => $id, 'name' => $name, 'group' => $group, 'email' => $email];
    // 跳转首页
    html_msg_url('index.php','登录成功',1);
}

// 显示页面并退出
function display($tips = null, $name1 = '')
{
    //global $category,$web_hot_link;

    if(!empty($tips)){
        html_msg_url('login.php',$tips,600) ;
    }else{
        if(empty($tips) && empty($name1)){
            html_msg_url('login.php','some error !?!',3000) ;
        }
        require V_ROOT.'login.html';
    }
    
    exit;
}
$css_file='res/wx_style.css';
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录</title>
    <link rel="stylesheet" href="<?php echo $css_file; ?>"> <!-- 动态引用CSS -->
</head>
<body>
    <h1>登录</h1>
    <?php if (!empty($error)): ?>
        <p style="color: red;"><?php echo $error; ?></p>
    <?php endif; ?>
    <?php if (!empty($error_log)): ?>
        <p style="color: red;"><?php echo $error_log; ?></p>
    <?php endif; ?>
    <form method="post">
        <label for="username">用户名：</label>
        <input type="text" name="username" id="username" required><br>
        <label for="password">密码：</label>
        <input type="password" name="password" id="password" required><br>
        <button type="submit">登录</button>
    </form> 

    <p>推荐从首页 <a href="http://pubhelper.shdic.com">pubhelper.shdic.com</a> 登录</p>

    <div>
        <?php echo $nav_html; ?>
    </div>
</body>
</html>