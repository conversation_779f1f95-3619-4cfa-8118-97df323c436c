  /* 基本表格样式 */
  table {
    width: 100%; /* 表格宽度 */
    border-collapse: collapse; /* 合并边框 */
    margin: 20px 0; /* 外边距 */
    font-size: 0.9em; /* 字体大小 */
    font-family: sans-serif; /* 字体 */
    min-width: 400px; /* 最小宽度 */
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.15); /* 阴影效果 */
  }

  thead tr {
    background-color: #009879; /* 表头背景色 */
    color: #ffffff; /* 表头文字颜色 */
    text-align: left; /* 文字对齐 */
  }

  th,
  td {
    padding: 12px 15px; /* 内边距 */
  }

  tbody tr {
    border-bottom: 1px solid #dddddd; /* 行与行之间的边框 */
  }

  tbody tr:nth-of-type(even) {
    background-color: #f3f3f3; /* 偶数行的背景色 */
  }

  tbody tr:last-of-type {
    border-bottom: 2px solid #009879; /* 最后一行的边框 */
  }

  tbody tr.active-row {
    font-weight: bold; /* 活动行的字体加粗 */
    background-color: #FFFFCC; /* 活动行的文字颜色 */
  }

  /* 鼠标悬停样式 */
  tbody tr:hover {
    background-color: #f1f1f1;
  }