.mylayui-layer-imgbar,
.mylayui-layer-imgtit a,
.mylayui-layer-tab .mylayui-layer-title span,
.mylayui-layer-title {
    text-overflow: ellipsis;
    white-space: nowrap
}

html #layuicss-layer {
    display: none;
    position: absolute;
    width: 1989px
}

.mylayui-layer,
.mylayui-layer-shade {
    position: fixed;
    _position: absolute;
    pointer-events: auto
}

.mylayui-layer-shade {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    _height: expression(document.body.offsetHeight+"px")
}

.mylayui-layer {
    -webkit-overflow-scrolling: touch;
    top: 150px;
    left: 0;
    margin: 0;
    padding: 0;
    background-color: #fff;
    -webkit-background-clip: content;
    border-radius: 2px;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, .3)
}

.mylayui-layer2 {
    -webkit-overflow-scrolling: touch;
    top: 150px;
    left: 0;
    margin: 0;
    padding: 0;
    background-color: #fff;
    -webkit-background-clip: content;
    border-radius: 2px;
    box-shadow: 1px 1px 50px rgba(0, 0, 0, .3)
}

.mylayui-layer-close {
    position: absolute
}

.mylayui-layer-content {
    position: relative
}

.mylayui-layer-border {
    border: 1px solid #B2B2B2;
    border: 1px solid rgba(0, 0, 0, .1);
    box-shadow: 1px 1px 5px rgba(0, 0, 0, .2)
}

.mylayui-layer-load {
    background: url(loading-1.gif) center center no-repeat #eee
}

.mylayui-layer-ico {
    background: url(icon.png) no-repeat
}

.mylayui-layer-btn a,
.mylayui-layer-dialog .mylayui-layer-ico,
.mylayui-layer-setwin a {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top
}

.mylayui-layer-move {
    display: none;
    position: fixed;
    *position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: move;
    opacity: 0;
    filter: alpha(opacity=0);
    background-color: #fff;
    z-index: 2147483647
}

.mylayui-layer-resize {
    position: absolute;
    width: 15px;
    height: 15px;
    right: 0;
    bottom: 0;
    cursor: se-resize
}

.layer-anim {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: .3s;
    animation-duration: .3s
}

@-webkit-keyframes layer-bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.5);
        transform: scale(.5)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes layer-bounceIn {
    0% {
        opacity: 0;
        -webkit-transform: scale(.5);
        -ms-transform: scale(.5);
        transform: scale(.5)
    }

    100% {
        opacity: 1;
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1)
    }
}

.layer-anim-00 {
    -webkit-animation-name: layer-bounceIn;
    animation-name: layer-bounceIn
}

@-webkit-keyframes layer-zoomInDown {
    0% {
        opacity: 0;
        -webkit-transform: scale(.1) translateY(-2000px);
        transform: scale(.1) translateY(-2000px);
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(.475) translateY(60px);
        transform: scale(.475) translateY(60px);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

@keyframes layer-zoomInDown {
    0% {
        opacity: 0;
        -webkit-transform: scale(.1) translateY(-2000px);
        -ms-transform: scale(.1) translateY(-2000px);
        transform: scale(.1) translateY(-2000px);
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(.475) translateY(60px);
        -ms-transform: scale(.475) translateY(60px);
        transform: scale(.475) translateY(60px);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

.layer-anim-01 {
    -webkit-animation-name: layer-zoomInDown;
    animation-name: layer-zoomInDown
}

@-webkit-keyframes layer-fadeInUpBig {
    0% {
        opacity: 0;
        -webkit-transform: translateY(2000px);
        transform: translateY(2000px)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }
}

@keyframes layer-fadeInUpBig {
    0% {
        opacity: 0;
        -webkit-transform: translateY(2000px);
        -ms-transform: translateY(2000px);
        transform: translateY(2000px)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0)
    }
}

.layer-anim-02 {
    -webkit-animation-name: layer-fadeInUpBig;
    animation-name: layer-fadeInUpBig
}

@-webkit-keyframes layer-zoomInLeft {
    0% {
        opacity: 0;
        -webkit-transform: scale(.1) translateX(-2000px);
        transform: scale(.1) translateX(-2000px);
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(.475) translateX(48px);
        transform: scale(.475) translateX(48px);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

@keyframes layer-zoomInLeft {
    0% {
        opacity: 0;
        -webkit-transform: scale(.1) translateX(-2000px);
        -ms-transform: scale(.1) translateX(-2000px);
        transform: scale(.1) translateX(-2000px);
        -webkit-animation-timing-function: ease-in-out;
        animation-timing-function: ease-in-out
    }

    60% {
        opacity: 1;
        -webkit-transform: scale(.475) translateX(48px);
        -ms-transform: scale(.475) translateX(48px);
        transform: scale(.475) translateX(48px);
        -webkit-animation-timing-function: ease-out;
        animation-timing-function: ease-out
    }
}

.layer-anim-03 {
    -webkit-animation-name: layer-zoomInLeft;
    animation-name: layer-zoomInLeft
}

@-webkit-keyframes layer-rollIn {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100%) rotate(-120deg);
        transform: translateX(-100%) rotate(-120deg)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(0);
        transform: translateX(0) rotate(0)
    }
}

@keyframes layer-rollIn {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-100%) rotate(-120deg);
        -ms-transform: translateX(-100%) rotate(-120deg);
        transform: translateX(-100%) rotate(-120deg)
    }

    100% {
        opacity: 1;
        -webkit-transform: translateX(0) rotate(0);
        -ms-transform: translateX(0) rotate(0);
        transform: translateX(0) rotate(0)
    }
}

.layer-anim-04 {
    -webkit-animation-name: layer-rollIn;
    animation-name: layer-rollIn
}

@keyframes layer-fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

.layer-anim-05 {
    -webkit-animation-name: layer-fadeIn;
    animation-name: layer-fadeIn
}

@-webkit-keyframes layer-shake {

    0%,
    100% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translateX(-10px);
        transform: translateX(-10px)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translateX(10px);
        transform: translateX(10px)
    }
}

@keyframes layer-shake {

    0%,
    100% {
        -webkit-transform: translateX(0);
        -ms-transform: translateX(0);
        transform: translateX(0)
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        -webkit-transform: translateX(-10px);
        -ms-transform: translateX(-10px);
        transform: translateX(-10px)
    }

    20%,
    40%,
    60%,
    80% {
        -webkit-transform: translateX(10px);
        -ms-transform: translateX(10px);
        transform: translateX(10px)
    }
}

.layer-anim-06 {
    -webkit-animation-name: layer-shake;
    animation-name: layer-shake
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0
    }

    100% {
        opacity: 1
    }
}

.mylayui-layer-title {
    padding: 0 20px 0 20px;
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #F0F0F0;
    font-size: 14px;
    color: #333;
    overflow: hidden;
    border-radius: 2px 2px 0 0
}

.mylayui-layer-setwin {
    position: absolute;
    right: 15px;
    *right: 0;
    top: 17px;
    font-size: 0;
    line-height: initial
}

.mylayui-layer-setwin a {
    position: relative;
    width: 16px;
    height: 16px;
    margin-left: 10px;
    font-size: 12px;
    _overflow: hidden
}

.mylayui-layer-setwin .mylayui-layer-min cite {
    position: absolute;
    width: 14px;
    height: 2px;
    left: 0;
    top: 50%;
    margin-top: -1px;
    background-color: #2E2D3C;
    cursor: pointer;
    _overflow: hidden
}

.mylayui-layer-setwin .mylayui-layer-min:hover cite {
    background-color: #2D93CA
}

.mylayui-layer-setwin .mylayui-layer-max {
    background-position: -32px -40px
}

.mylayui-layer-setwin .mylayui-layer-max:hover {
    background-position: -16px -40px
}

.mylayui-layer-setwin .mylayui-layer-maxmin {
    background-position: -65px -40px
}

.mylayui-layer-setwin .mylayui-layer-maxmin:hover {
    background-position: -49px -40px
}

.mylayui-layer-setwin .mylayui-layer-close1 {
    background-position: 1px -40px;
    cursor: pointer
}

.mylayui-layer-setwin .mylayui-layer-close1:hover {
    opacity: .7
}

.mylayui-layer-setwin .mylayui-layer-close2 {
    position: absolute;
    right: -28px;
    top: -28px;
    width: 30px;
    height: 30px;
    margin-left: 0;
    background-position: -149px -31px;
    *right: -18px;
    _display: none
}

.mylayui-layer-setwin .mylayui-layer-close2:hover {
    background-position: -180px -31px
}

.mylayui-layer-btn {
    text-align: right;
    padding: 0 15px 12px;
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none
}

.mylayui-layer-btn a {
    height: 28px;
    line-height: 28px;
    margin: 5px 5px 0;
    padding: 0 15px;
    border: 1px solid #dedede;
    background-color: #fff;
    color: #333;
    border-radius: 2px;
    font-weight: 400;
    cursor: pointer;
    text-decoration: none
}

.mylayui-layer-btn a:hover {
    opacity: .9;
    text-decoration: none
}

.mylayui-layer-btn a:active {
    opacity: .8
}

.mylayui-layer-btn .mylayui-layer-btn0 {
    border-color: #1E9FFF;
    background-color: #1E9FFF;
    color: #fff
}

.mylayui-layer-btn-l {
    text-align: left
}

.mylayui-layer-btn-c {
    text-align: center
}

.mylayui-layer-dialog {
    min-width: 300px
}

.mylayui-layer-dialog .mylayui-layer-content {
    position: relative;
    padding: 20px;
    line-height: 24px;
    word-break: break-all;
    overflow: hidden;
    font-size: 14px;
    overflow-x: hidden;
    overflow-y: auto
}

.mylayui-layer-dialog .mylayui-layer-content .mylayui-layer-ico {
    position: absolute;
    top: 16px;
    left: 15px;
    _left: -40px;
    width: 30px;
    height: 30px
}

.mylayui-layer-ico1 {
    background-position: -30px 0
}

.mylayui-layer-ico2 {
    background-position: -60px 0
}

.mylayui-layer-ico3 {
    background-position: -90px 0
}

.mylayui-layer-ico4 {
    background-position: -120px 0
}

.mylayui-layer-ico5 {
    background-position: -150px 0
}

.mylayui-layer-ico6 {
    background-position: -180px 0
}

.mylayui-layer-rim {
    border: 6px solid #8D8D8D;
    border: 6px solid rgba(0, 0, 0, .3);
    border-radius: 5px;
    box-shadow: none
}

.mylayui-layer-msg {
    min-width: 180px;
    border: 1px solid #D3D4D3;
    box-shadow: none
}

.mylayui-layer-hui {
    min-width: 100px;
    background-color: #000;
    filter: alpha(opacity=60);
    background-color: rgba(0, 0, 0, .6);
    color: #fff;
    border: none
}

.mylayui-layer-hui .mylayui-layer-content {
    padding: 12px 25px;
    text-align: center
}

.mylayui-layer-dialog .mylayui-layer-padding {
    padding: 20px 20px 20px 55px;
    text-align: left
}

.mylayui-layer-page .mylayui-layer-content {
    position: relative;
    overflow: auto
}

.mylayui-layer-iframe .mylayui-layer-btn,
.mylayui-layer-page .mylayui-layer-btn {
    padding-top: 10px
}

.mylayui-layer-nobg {
    background: 0 0
}

.mylayui-layer-iframe iframe {
    display: block;
    width: 100%
}

.mylayui-layer-loading {
    border-radius: 100%;
    background: 0 0;
    box-shadow: none;
    border: none
}

.mylayui-layer-loading .mylayui-layer-content {
    width: 60px;
    height: 24px;
    background: url(loading-0.gif) no-repeat
}

.mylayui-layer-loading .mylayui-layer-loading1 {
    width: 37px;
    height: 37px;
    background: url(loading-1.gif) no-repeat
}

.mylayui-layer-ico16,
.mylayui-layer-loading .mylayui-layer-loading2 {
    width: 32px;
    height: 32px;
    background: url(loading-2.gif) no-repeat
}

.mylayui-layer-tips {
    background: 0 0;
    box-shadow: none;
    border: none
}

.mylayui-layer-tips .mylayui-layer-content {
    position: relative;
    line-height: 22px;
    min-width: 12px;
    padding: 8px 15px;
    font-size: 12px;
    _float: left;
    border-radius: 2px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, .2);
    background-color: #000;
    color: #fff
}

.mylayui-layer-tips .mylayui-layer-close {
    right: -2px;
    top: -1px
}

.mylayui-layer-tips i.mylayui-layer-TipsG {
    position: absolute;
    width: 0;
    height: 0;
    border-width: 8px;
    border-color: transparent;
    border-style: dashed;
    *overflow: hidden
}

.mylayui-layer-tips i.mylayui-layer-TipsB,
.mylayui-layer-tips i.mylayui-layer-TipsT {
    left: 5px;
    border-right-style: solid;
    border-right-color: #000
}

.mylayui-layer-tips i.mylayui-layer-TipsT {
    bottom: -8px
}

.mylayui-layer-tips i.mylayui-layer-TipsB {
    top: -8px
}

.mylayui-layer-tips i.mylayui-layer-TipsL,
.mylayui-layer-tips i.mylayui-layer-TipsR {
    top: 5px;
    border-bottom-style: solid;
    border-bottom-color: #000
}

.mylayui-layer-tips i.mylayui-layer-TipsR {
    left: -8px
}

.mylayui-layer-tips i.mylayui-layer-TipsL {
    right: -8px
}

.mylayui-layer-lan[type=dialog] {
    min-width: 280px
}

.mylayui-layer-lan .mylayui-layer-title {
    background: #4476A7;
    color: #fff;
    border: none
}

.mylayui-layer-lan .mylayui-layer-btn {
    padding: 5px 10px 10px;
    text-align: right;
    border-top: 1px solid #E9E7E7
}

.mylayui-layer-lan .mylayui-layer-btn a {
    background: #fff;
    border-color: #E9E7E7;
    color: #333
}

.mylayui-layer-lan .mylayui-layer-btn .mylayui-layer-btn1 {
    background: #C9C5C5
}

.mylayui-layer-molv .mylayui-layer-title {
    background: #009f95;
    color: #fff;
    border: none
}

.mylayui-layer-molv .mylayui-layer-btn a {
    background: #009f95;
    border-color: #009f95
}

.mylayui-layer-molv .mylayui-layer-btn .mylayui-layer-btn1 {
    background: #92B8B1
}

.mylayui-layer-iconext {
    background: url(icon-ext.png) no-repeat
}

.mylayui-layer-prompt .mylayui-layer-input {
    display: block;
    width: 260px;
    height: 36px;
    margin: 0 auto;
    line-height: 30px;
    padding-left: 10px;
    border: 1px solid #e6e6e6;
    color: #333
}

.mylayui-layer-prompt textarea.mylayui-layer-input {
    width: 300px;
    height: 100px;
    line-height: 20px;
    padding: 6px 10px
}

.mylayui-layer-prompt .mylayui-layer-content {
    padding: 20px
}

.mylayui-layer-prompt .mylayui-layer-btn {
    padding-top: 0
}

.mylayui-layer-tab {
    box-shadow: 1px 1px 50px rgba(0, 0, 0, .4)
}

.mylayui-layer-tab .mylayui-layer-title {
    padding-left: 0;
    overflow: visible
}

.mylayui-layer-tab .mylayui-layer-title span {
    position: relative;
    float: left;
    min-width: 80px;
    max-width: 300px;
    padding: 0 20px;
    text-align: center;
    overflow: hidden;
    cursor: pointer
}

.mylayui-layer-tab .mylayui-layer-title span.mylayui-this {
    height: 51px;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    background-color: #fff;
    z-index: 10
}

.mylayui-layer-tab .mylayui-layer-title span:first-child {
    border-left: none
}

.mylayui-layer-tabmain {
    line-height: 24px;
    clear: both
}

.mylayui-layer-tabmain .mylayui-layer-tabli {
    display: none
}

.mylayui-layer-tabmain .mylayui-layer-tabli.mylayui-this {
    display: block
}

.mylayui-layer-photos {
    background: 0 0;
    box-shadow: none
}

.mylayui-layer-photos .mylayui-layer-content {
    overflow: hidden;
    text-align: center
}

.mylayui-layer-photos .mylayui-layer-phimg img {
    position: relative;
    width: 100%;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top
}

.mylayui-layer-imgnext,
.mylayui-layer-imgprev {
    position: fixed;
    top: 50%;
    width: 27px;
    _width: 44px;
    height: 44px;
    margin-top: -22px;
    outline: 0;
    blr: expression(this.onFocus=this.blur())
}

.mylayui-layer-imgprev {
    left: 30px;
    background-position: -5px -5px;
    _background-position: -70px -5px
}

.mylayui-layer-imgprev:hover {
    background-position: -33px -5px;
    _background-position: -120px -5px
}

.mylayui-layer-imgnext {
    right: 30px;
    _right: 8px;
    background-position: -5px -50px;
    _background-position: -70px -50px
}

.mylayui-layer-imgnext:hover {
    background-position: -33px -50px;
    _background-position: -120px -50px
}

.mylayui-layer-imgbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #000\9;
    filter: Alpha(opacity=60);
    background-color: rgba(2, 0, 0, .35);
    color: #fff;
    overflow: hidden;
    font-size: 0
}

.mylayui-layer-imgtit * {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    vertical-align: top;
    font-size: 12px
}

.mylayui-layer-imgtit a {
    max-width: 65%;
    overflow: hidden;
    color: #fff
}

.mylayui-layer-imgtit a:hover {
    color: #fff;
    text-decoration: underline
}

.mylayui-layer-imgtit em {
    padding-left: 10px;
    font-style: normal
}

@-webkit-keyframes layer-bounceOut {
    100% {
        opacity: 0;
        -webkit-transform: scale(.7);
        transform: scale(.7)
    }

    30% {
        -webkit-transform: scale(1.05);
        transform: scale(1.05)
    }

    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes layer-bounceOut {
    100% {
        opacity: 0;
        -webkit-transform: scale(.7);
        -ms-transform: scale(.7);
        transform: scale(.7)
    }

    30% {
        -webkit-transform: scale(1.05);
        -ms-transform: scale(1.05);
        transform: scale(1.05)
    }

    0% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1)
    }
}

.layer-anim-close {
    -webkit-animation-name: layer-bounceOut;
    animation-name: layer-bounceOut;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

@media screen and (max-width:1100px) {
    .mylayui-layer-iframe {
        overflow-y: auto;
        -webkit-overflow-scrolling: touch
    }
}