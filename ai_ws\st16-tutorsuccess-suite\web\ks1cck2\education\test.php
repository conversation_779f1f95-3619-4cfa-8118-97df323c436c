<?php
/**
 * 测试新URL结构的入口文件
 * 修订日期：2025-01-22
 */

// 定义应用目录
define('APP_PATH', __DIR__ . '/application/');

// 定义根目录
define('ROOT_PATH', __DIR__ . '/');

// 引入基础配置
require_once '../_ks1.php';
require_once '../mysqlconn.php';

// 定义角色常量
define('ROLE_ADMIN', 1);
define('ROLE_TEACHER', 2);
define('ROLE_STUDENT', 3);

// 定义请假状态常量
define('LEAVE_STATUS_PENDING', 0);
define('LEAVE_STATUS_APPROVED', 1);
define('LEAVE_STATUS_REJECTED', 2);

// 模拟登录状态（仅用于测试）
if (!isset($_SESSION)) {
    session_start();
}

// 模拟用户登录
$_SESSION[SESSION_KEY]['user'] = [
    'id' => 1,
    'name' => '系统管理员',
    'email' => '<EMAIL>'
];

// 模拟用户角色
function get_user_roles($user_id) {
    // 根据路径返回不同角色
    $path = $_GET['path'] ?? '';
    if (strpos($path, 'admin') === 0) {
        return [ROLE_ADMIN]; // 管理员角色
    } elseif (strpos($path, 'teacher') === 0) {
        return [ROLE_TEACHER]; // 教师角色
    } elseif (strpos($path, 'student') === 0) {
        return [ROLE_STUDENT]; // 学生角色
    }
    return [ROLE_ADMIN]; // 默认管理员角色
}

function get_current_login_user() {
    return $_SESSION[SESSION_KEY]['user'];
}

// 简单的路由处理
class TestRouter
{
    private $routes = [];
    
    public function __construct()
    {
        // 定义路由规则
        $this->routes = [
            'teacher' => 'Teacher@index',
            'teacher/schedule' => 'Teacher@schedule',
            'teacher/leave' => 'Teacher@leave',
            'teacher/approve' => 'Teacher@approveLeave',
            'teacher/start' => 'Teacher@startClass',
            'student' => 'Student@index',
            'student/schedule' => 'Student@schedule',
            'student/leave' => 'Student@leave',
            'admin' => 'Admin@index',
            'admin/users' => 'Admin@users',
            'admin/courses' => 'Admin@courses',
            '' => 'Index@dev'
        ];
    }
    
    public function dispatch()
    {
        // 获取请求路径
        $path = $_GET['path'] ?? '';
        
        // 查找路由
        if (isset($this->routes[$path])) {
            $route = $this->routes[$path];
        } else {
            // 默认路由
            $route = $this->routes[''];
        }
        
        // 解析控制器和方法
        list($controller, $method) = explode('@', $route);
        
        // 加载控制器
        $controller_file = APP_PATH . 'controller/' . $controller . '.php';
        if (!file_exists($controller_file)) {
            $this->show404();
            return;
        }
        
        require_once $controller_file;
        
        $controller_class = 'app\\controller\\' . $controller;
        if (!class_exists($controller_class)) {
            $this->show404();
            return;
        }
        
        $controller_instance = new $controller_class();
        if (!method_exists($controller_instance, $method)) {
            $this->show404();
            return;
        }
        
        // 执行方法
        $controller_instance->$method();
    }
    
    private function show404()
    {
        http_response_code(404);
        echo '404 Not Found';
    }
}

// 清除opcache（仅在开发环境）
if (function_exists('opcache_reset')) {
    opcache_reset();
}

// 启动应用
try {
    $router = new TestRouter();
    $router->dispatch();
} catch (Exception $e) {
    error_log('教培系统错误: ' . $e->getMessage());
    echo '系统错误: ' . $e->getMessage();
}
