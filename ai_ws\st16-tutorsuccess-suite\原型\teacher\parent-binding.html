<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家长绑定管理 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .student-card {
            border-left: 4px solid #28a745;
            margin-bottom: 15px;
        }
        .parent-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #20c997;
        }
        .relation-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .relation-father {
            background-color: #cce5ff;
            color: #004085;
        }
        .relation-mother {
            background-color: #ffe6f0;
            color: #6b1e3e;
        }
        .relation-grandparent {
            background-color: #fff3cd;
            color: #856404;
        }
        .relation-other {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .btn-bind {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            color: white;
            transition: all 0.3s;
        }
        .btn-bind:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
            color: white;
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        .search-box:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fa fa-graduation-cap me-2"></i>特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home me-1"></i>主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar me-1"></i>课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave-approval.html">
                            <i class="fa fa-check-circle me-1"></i>请假审批
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="parent-binding.html">
                            <i class="fa fa-users me-1"></i>家长绑定
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user me-1"></i>张老师
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-users text-success me-2"></i>家长绑定管理</h2>
                <p class="text-muted">管理学生与家长微信账号的绑定关系</p>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control search-box" placeholder="搜索学生姓名或学号..." id="searchStudent">
                    <button class="btn btn-outline-success" type="button">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="classFilter">
                    <option value="">选择班级</option>
                    <option value="class1">高一(1)班</option>
                    <option value="class2">高一(2)班</option>
                    <option value="class3">高二(1)班</option>
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-bind w-100" data-bs-toggle="modal" data-bs-target="#addBindingModal">
                    <i class="fa fa-plus me-2"></i>新增绑定
                </button>
            </div>
        </div>

        <!-- 学生列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list me-2"></i>学生家长绑定列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 学生1 -->
                        <div class="student-card card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">李小明</h6>
                                        <small class="text-muted">学号：2024001 | 高一(1)班</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>李爸爸</strong>
                                                    <span class="relation-badge relation-father ms-2">父亲</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>李妈妈</strong>
                                                    <span class="relation-badge relation-mother ms-2">母亲</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('李小明')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 学生2 -->
                        <div class="student-card card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">王小红</h6>
                                        <small class="text-muted">学号：2024002 | 高一(1)班</small>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>王奶奶</strong>
                                                    <span class="relation-badge relation-grandparent ms-2">奶奶</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('王小红')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 学生3 -->
                        <div class="student-card card">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">张小华</h6>
                                        <small class="text-muted">学号：2024003 | 高一(2)班</small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">暂无家长绑定</small>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('张小华')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增绑定模态框 -->
    <div class="modal fade" id="addBindingModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                    <h5 class="modal-title">新增家长绑定</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="bindingForm">
                        <div class="mb-3">
                            <label class="form-label">选择学生</label>
                            <select class="form-select" id="studentSelect" required>
                                <option value="">请选择学生</option>
                                <option value="2024001">李小明 (2024001)</option>
                                <option value="2024002">王小红 (2024002)</option>
                                <option value="2024003">张小华 (2024003)</option>
                                <option value="2024004">刘小刚 (2024004)</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">家长姓名</label>
                            <input type="text" class="form-control" id="parentName" placeholder="请输入家长姓名" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">家长关系</label>
                            <select class="form-select" id="parentRelation" required>
                                <option value="">请选择关系</option>
                                <option value="father">父亲</option>
                                <option value="mother">母亲</option>
                                <option value="grandfather">爷爷</option>
                                <option value="grandmother">奶奶</option>
                                <option value="external_grandfather">外公</option>
                                <option value="external_grandmother">外婆</option>
                                <option value="uncle">叔叔</option>
                                <option value="aunt">阿姨</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">联系电话</label>
                            <input type="tel" class="form-control" id="parentPhone" placeholder="请输入联系电话">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">微信绑定方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="bindMethod" id="qrCode" value="qr" checked>
                                <label class="form-check-label" for="qrCode">
                                    生成二维码供家长扫描绑定
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="bindMethod" id="inviteCode" value="code">
                                <label class="form-check-label" for="inviteCode">
                                    生成邀请码供家长输入绑定
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-bind" onclick="submitBinding()">生成绑定码</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 绑定码显示模态框 -->
    <div class="modal fade" id="bindingCodeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">
                    <h5 class="modal-title">家长绑定码</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <div id="qrCodeSection" style="display: none;">
                        <h6>请家长使用微信扫描以下二维码</h6>
                        <div class="my-4">
                            <div style="width: 200px; height: 200px; background: #f8f9fa; border: 2px dashed #dee2e6; margin: 0 auto; display: flex; align-items: center; justify-content: center;">
                                <i class="fa fa-qrcode" style="font-size: 4rem; color: #6c757d;"></i>
                            </div>
                        </div>
                        <p class="text-muted">二维码有效期：24小时</p>
                    </div>
                    <div id="inviteCodeSection" style="display: none;">
                        <h6>请家长在微信中输入以下邀请码</h6>
                        <div class="my-4">
                            <div class="alert alert-success" style="font-size: 1.5rem; font-weight: bold; letter-spacing: 2px;">
                                ABC123
                            </div>
                        </div>
                        <p class="text-muted">邀请码有效期：24小时</p>
                    </div>
                    <div class="mt-3">
                        <button class="btn btn-outline-success" onclick="copyBindingInfo()">
                            <i class="fa fa-copy me-2"></i>复制分享
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>

    <script>
        // 添加家长绑定 - 修订时间：2025-08-21 23:45:48
        function addParent(studentName) {
            // 设置学生选择框的值
            const studentSelect = document.getElementById('studentSelect');
            for (let option of studentSelect.options) {
                if (option.text.includes(studentName)) {
                    option.selected = true;
                    break;
                }
            }
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('addBindingModal'));
            modal.show();
        }

        // 提交绑定信息 - 修订时间：2025-08-21 23:45:48
        function submitBinding() {
            const form = document.getElementById('bindingForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const bindMethod = document.querySelector('input[name="bindMethod"]:checked').value;

            // 隐藏第一个模态框
            const addModal = bootstrap.Modal.getInstance(document.getElementById('addBindingModal'));
            addModal.hide();

            // 显示绑定码模态框
            setTimeout(() => {
                const codeModal = new bootstrap.Modal(document.getElementById('bindingCodeModal'));

                if (bindMethod === 'qr') {
                    document.getElementById('qrCodeSection').style.display = 'block';
                    document.getElementById('inviteCodeSection').style.display = 'none';
                } else {
                    document.getElementById('qrCodeSection').style.display = 'none';
                    document.getElementById('inviteCodeSection').style.display = 'block';
                }

                codeModal.show();
            }, 300);
        }

        // 复制绑定信息 - 修订时间：2025-08-21 23:45:48
        function copyBindingInfo() {
            const bindMethod = document.querySelector('input[name="bindMethod"]:checked').value;
            let copyText = '';

            if (bindMethod === 'qr') {
                copyText = '请使用微信扫描二维码绑定学生账号，二维码有效期24小时。';
            } else {
                copyText = '请在微信中输入邀请码：ABC123 绑定学生账号，邀请码有效期24小时。';
            }

            navigator.clipboard.writeText(copyText).then(() => {
                alert('绑定信息已复制到剪贴板');
            });
        }

        // 搜索功能 - 修订时间：2025-08-21 23:45:48
        document.getElementById('searchStudent').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const studentCards = document.querySelectorAll('.student-card');

            studentCards.forEach(card => {
                const studentName = card.querySelector('h6').textContent.toLowerCase();
                const studentId = card.querySelector('small').textContent.toLowerCase();

                if (studentName.includes(searchTerm) || studentId.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 班级筛选 - 修订时间：2025-08-21 23:45:48
        document.getElementById('classFilter').addEventListener('change', function() {
            const selectedClass = this.value;
            const studentCards = document.querySelectorAll('.student-card');

            studentCards.forEach(card => {
                const classInfo = card.querySelector('small').textContent;

                if (!selectedClass || classInfo.includes(selectedClass.replace('class', '高一(').replace('1', '1)班'))) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
