<?php
/**
 * 学生模型
 * 处理学生数据的操作
 */
class StudentModel extends Model {
    /**
     * 表名
     * @var string
     */
    protected $table = 'students';
    
    /**
     * 根据ID获取学生信息
     * @param int $id 学生ID
     * @return array|null 学生信息
     */
    public function getStudentById($id) {
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.id = ?";
        return $this->db->selectOne($sql, [$id]);
    }
    
    /**
     * 根据用户ID获取学生信息
     * @param int $userId 用户ID
     * @return array|null 学生信息
     */
    public function getStudentByUserId($userId) {
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.user_id = ?";
        return $this->db->selectOne($sql, [$userId]);
    }
    
    /**
     * 获取学生列表
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 学生列表
     */
    public function getStudents($page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                ORDER BY s.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql);
    }
    
    /**
     * 获取学生总数
     * @return int 学生总数
     */
    public function getStudentCount() {
        return $this->db->count("SELECT COUNT(*) FROM {$this->table}");
    }
    
    /**
     * 创建学生
     * @param array $data 学生数据
     * @return int 新学生的ID
     */
    public function createStudent($data) {
        return $this->db->insert($this->table, $data);
    }
    
    /**
     * 更新学生信息
     * @param int $id 学生ID
     * @param array $data 学生数据
     * @return bool 是否成功
     */
    public function updateStudent($id, $data) {
        return $this->db->update($this->table, $data, "id = ?", [$id]);
    }
    
    /**
     * 删除学生
     * @param int $id 学生ID
     * @return bool 是否成功
     */
    public function deleteStudent($id) {
        return $this->db->delete($this->table, "id = ?", [$id]);
    }
    
    /**
     * 根据年级获取学生列表
     * @param string $grade 年级
     * @return array 学生列表
     */
    public function getStudentsByGrade($grade) {
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.grade = ? 
                ORDER BY s.id DESC";
        return $this->db->select($sql, [$grade]);
    }
    
    /**
     * 搜索学生
     * @param string $keyword 关键词
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return array 学生列表
     */
    public function searchStudents($keyword, $page = 1, $perPage = 10) {
        $offset = ($page - 1) * $perPage;
        $keyword = "%{$keyword}%";
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE u.name LIKE ? OR s.student_number LIKE ? OR s.grade LIKE ? 
                ORDER BY s.id DESC 
                LIMIT {$offset}, {$perPage}";
        return $this->db->select($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取搜索结果总数
     * @param string $keyword 关键词
     * @return int 结果总数
     */
    public function getSearchCount($keyword) {
        $keyword = "%{$keyword}%";
        $sql = "SELECT COUNT(*) FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE u.name LIKE ? OR s.student_number LIKE ? OR s.grade LIKE ?";
        return $this->db->count($sql, [$keyword, $keyword, $keyword]);
    }
    
    /**
     * 获取课程的学生列表
     * @param int $courseId 课程ID
     * @return array 学生列表
     */
    public function getStudentsByCourse($courseId) {
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status, cs.enrollment_date, cs.status as enrollment_status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                JOIN course_student cs ON s.id = cs.student_id 
                WHERE cs.course_id = ? 
                ORDER BY s.id DESC";
        return $this->db->select($sql, [$courseId]);
    }
    
    /**
     * 获取未报名指定课程的学生列表
     * @param int $courseId 课程ID
     * @return array 学生列表
     */
    public function getStudentsNotInCourse($courseId) {
        $sql = "SELECT s.*, u.name, u.email, u.phone, u.avatar, u.status 
                FROM {$this->table} s 
                JOIN users u ON s.user_id = u.id 
                WHERE s.id NOT IN (
                    SELECT student_id FROM course_student WHERE course_id = ?
                ) 
                ORDER BY s.id DESC";
        return $this->db->select($sql, [$courseId]);
    }
}