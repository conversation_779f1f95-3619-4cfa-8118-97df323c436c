<?php
/**
 * 教室数据模型
 * 创建时间：2025-01-22
 */

require_once 'BaseModel.php';

class Classroom extends BaseModel {
    
    public function __construct($conn) {
        parent::__construct($conn);
        $this->table = TABLE_CLASSROOM;
    }
    
    /**
     * 获取可用的教室列表
     * @param array $conditions
     * @param string $order_by
     * @return array
     */
    public function getAvailableClassrooms($conditions = [], $order_by = 'name ASC') {
        $conditions['status'] = STATUS_ACTIVE;
        return $this->findAll($conditions, $order_by);
    }
    
    /**
     * 获取教室的使用情况
     * @param int $classroom_id
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getUsageSchedule($classroom_id, $start_date = null, $end_date = null) {
        $sql = "SELECT cs.*, c.name as course_name, t.name as teacher_name
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
                JOIN " . TABLE_TEACHER . " t ON c.teacher_id = t.id
                WHERE cs.classroom_id = ? AND cs.status = ? AND c.status = ?";
        
        $params = [$classroom_id, STATUS_ACTIVE, STATUS_ACTIVE];
        $types = 'iii';
        
        if ($start_date) {
            $sql .= " AND cs.start_date >= ?";
            $params[] = $start_date;
            $types .= 's';
        }
        
        if ($end_date) {
            $sql .= " AND (cs.end_date IS NULL OR cs.end_date <= ?)";
            $params[] = $end_date;
            $types .= 's';
        }
        
        $sql .= " ORDER BY cs.day_of_week, cs.start_time";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $schedules = [];
        while ($row = $result->fetch_assoc()) {
            $schedules[] = $row;
        }
        
        return $schedules;
    }
    
    /**
     * 检查教室在指定时间是否可用
     * @param int $classroom_id
     * @param int $day_of_week
     * @param string $start_time
     * @param string $end_time
     * @param int $exclude_schedule_id
     * @return bool
     */
    public function isAvailable($classroom_id, $day_of_week, $start_time, $end_time, $exclude_schedule_id = 0) {
        $sql = "SELECT COUNT(*) as count
                FROM " . TABLE_COURSE_SCHEDULE . " cs
                WHERE cs.classroom_id = ? AND cs.day_of_week = ? AND cs.status = ?
                AND ((cs.start_time <= ? AND cs.end_time > ?) OR (cs.start_time < ? AND cs.end_time >= ?))";
        
        $params = [$classroom_id, $day_of_week, STATUS_ACTIVE, $start_time, $start_time, $end_time, $end_time];
        $types = 'iissss';
        
        if ($exclude_schedule_id > 0) {
            $sql .= " AND cs.id != ?";
            $params[] = $exclude_schedule_id;
            $types .= 'i';
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] == 0;
    }
    
    /**
     * 获取教室的统计信息
     * @param int $classroom_id
     * @return array
     */
    public function getStatistics($classroom_id) {
        // 使用该教室的课程数量
        $course_count = $this->query(
            "SELECT COUNT(DISTINCT cs.course_id) as count 
             FROM " . TABLE_COURSE_SCHEDULE . " cs
             JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
             WHERE cs.classroom_id = ? AND cs.status = ? AND c.status = ?",
            [$classroom_id, STATUS_ACTIVE, STATUS_ACTIVE]
        )[0]['count'];
        
        // 课程安排数量
        $schedule_count = $this->query(
            "SELECT COUNT(*) as count FROM " . TABLE_COURSE_SCHEDULE . " WHERE classroom_id = ? AND status = ?",
            [$classroom_id, STATUS_ACTIVE]
        )[0]['count'];
        
        // 本周使用次数
        $week_usage = $this->query(
            "SELECT COUNT(*) as count 
             FROM " . TABLE_COURSE_SCHEDULE . " cs
             JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id
             WHERE cs.classroom_id = ? AND cs.status = ? AND c.status = ?
             AND cs.start_date <= CURDATE() 
             AND (cs.end_date IS NULL OR cs.end_date >= CURDATE())",
            [$classroom_id, STATUS_ACTIVE, STATUS_ACTIVE]
        )[0]['count'];
        
        // 使用率计算（基于一周7天，每天最多可安排的时间段）
        $max_slots_per_week = 7 * count(edu_config('time.class_periods'));
        $usage_rate = $max_slots_per_week > 0 ? round(($week_usage / $max_slots_per_week) * 100, 2) : 0;
        
        return [
            'course_count' => $course_count,
            'schedule_count' => $schedule_count,
            'week_usage' => $week_usage,
            'usage_rate' => $usage_rate
        ];
    }
    
    /**
     * 获取教室列表（包含使用统计）
     * @param array $conditions
     * @param string $order_by
     * @return array
     */
    public function getClassroomsWithStats($conditions = [], $order_by = 'name ASC') {
        $classrooms = $this->getAvailableClassrooms($conditions, $order_by);
        
        foreach ($classrooms as &$classroom) {
            $stats = $this->getStatistics($classroom['id']);
            $classroom = array_merge($classroom, $stats);
        }
        
        return $classrooms;
    }
    
    /**
     * 获取指定时间段可用的教室
     * @param int $day_of_week
     * @param string $start_time
     * @param string $end_time
     * @param int $exclude_schedule_id
     * @return array
     */
    public function getAvailableForTime($day_of_week, $start_time, $end_time, $exclude_schedule_id = 0) {
        $all_classrooms = $this->getAvailableClassrooms();
        $available_classrooms = [];
        
        foreach ($all_classrooms as $classroom) {
            if ($this->isAvailable($classroom['id'], $day_of_week, $start_time, $end_time, $exclude_schedule_id)) {
                $available_classrooms[] = $classroom;
            }
        }
        
        return $available_classrooms;
    }
    
    /**
     * 获取教室的周课表
     * @param int $classroom_id
     * @return array
     */
    public function getWeeklySchedule($classroom_id) {
        $schedules = $this->getUsageSchedule($classroom_id);
        
        // 按星期几分组
        $weekly_schedule = [];
        for ($i = 1; $i <= 7; $i++) {
            $weekly_schedule[$i] = [];
        }
        
        foreach ($schedules as $schedule) {
            $day = $schedule['day_of_week'];
            $weekly_schedule[$day][] = $schedule;
        }
        
        // 每天的课程按时间排序
        foreach ($weekly_schedule as &$day_schedules) {
            usort($day_schedules, function($a, $b) {
                return strcmp($a['start_time'], $b['start_time']);
            });
        }
        
        return $weekly_schedule;
    }
    
    /**
     * 检查教室名称是否已存在
     * @param string $name
     * @param int $exclude_id
     * @return bool
     */
    public function nameExists($name, $exclude_id = 0) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE name = ? AND status = ?";
        $params = [$name, STATUS_ACTIVE];
        $types = 'si';
        
        if ($exclude_id > 0) {
            $sql .= " AND id != ?";
            $params[] = $exclude_id;
            $types .= 'i';
        }
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        
        return $row['count'] > 0;
    }
    
    /**
     * 获取教室使用报告
     * @param string $start_date
     * @param string $end_date
     * @return array
     */
    public function getUsageReport($start_date = null, $end_date = null) {
        $sql = "SELECT cr.id, cr.name, cr.capacity, cr.location,
                COUNT(DISTINCT cs.course_id) as course_count,
                COUNT(cs.id) as schedule_count,
                SUM(TIMESTAMPDIFF(MINUTE, cs.start_time, cs.end_time)) as total_minutes
                FROM {$this->table} cr
                LEFT JOIN " . TABLE_COURSE_SCHEDULE . " cs ON cr.id = cs.classroom_id AND cs.status = ?
                LEFT JOIN " . TABLE_COURSE . " c ON cs.course_id = c.id AND c.status = ?
                WHERE cr.status = ?";
        
        $params = [STATUS_ACTIVE, STATUS_ACTIVE, STATUS_ACTIVE];
        $types = 'iii';
        
        if ($start_date) {
            $sql .= " AND cs.start_date >= ?";
            $params[] = $start_date;
            $types .= 's';
        }
        
        if ($end_date) {
            $sql .= " AND (cs.end_date IS NULL OR cs.end_date <= ?)";
            $params[] = $end_date;
            $types .= 's';
        }
        
        $sql .= " GROUP BY cr.id ORDER BY schedule_count DESC, cr.name";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param($types, ...$params);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $report = [];
        while ($row = $result->fetch_assoc()) {
            $row['total_hours'] = round($row['total_minutes'] / 60, 2);
            $report[] = $row;
        }
        
        return $report;
    }
}
