<?php
require_once('check_admin.php');
require_once('mysqlconn.php');

use Medoo\Medoo;

if (isset($_REQUEST["userid"])) {
    $row = $conn->get('user', '*', ['userid' => $_REQUEST["userid"]]);
    if (strtotime($row["expiretime"]) < strtotime(date("Y-m-d H:i:s"))) {
        // $sql = "UPDATE user SET quota = quota + " . $_REQUEST["quota"] . ",expiretime = DATE_ADD(CURDATE(), INTERVAL " . $_REQUEST["extenddays"] . " DAY) WHERE userid = '" . $_REQUEST["userid"] . "'";
        $data = ['quota[+]' => $_REQUEST["quota"], 'expiretime' => Medoo::raw("DATE_ADD(CURDATE(), INTERVAL " . $_REQUEST['extenddays'] . " DAY)")];
    } else {
        // $sql = "UPDATE user SET quota = quota + " . $_REQUEST["quota"] . ",expiretime = DATE_ADD(expiretime, INTERVAL " . $_REQUEST["extenddays"] . " DAY) WHERE userid = '" . $_REQUEST["userid"] . "'";
        $data = ['quota[+]' => $_REQUEST["quota"], 'expiretime' => Medoo::raw("DATE_ADD(expiretime, INTERVAL " . $_REQUEST['extenddays'] . " DAY)")];
    }
    $conn->update('user', $data, ['userid' => $_REQUEST["userid"]]);
    $conn->insert('rechargelog', ['userid' => $_REQUEST["userid"], 'quota' => $_REQUEST["quota"], 'extenddays' => $_REQUEST["extenddays"], 'rechargetime' => date('Y-m-d H:i:s'), 'operatorid' => $_SESSION['operatorid'], 'memo' => $_REQUEST["memo"]]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('充值成功！');location.href='manualrecharge.php';</script>";
    exit;
}
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>参数配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />
    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>
    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: center;
        }

        input {
            text-align: center;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>
    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-users"></i> 用户管理</li>
                <li class="active">手动充值</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <div class="page-content-area">
                <div class="row">
                    <div class="space-6"></div>
                    <div class="col-xs-12">
                        <form class="form-horizontal" name="recharge" onsubmit="return checkform();">

                            <div class="form-group">
                                <label class="col-lg-4 control-label">充值用户编号：</label>

                                <div class="col-lg-2">
                                    <input type="text" style="text-align:left;" id="userid" name="userid" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">充值额度：</label>

                                <div class="col-lg-2">
                                    <input type="text" style="text-align:left;" id="quota" name="quota" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">延长天数：</label>

                                <div class="col-lg-2">
                                    <input type="text" style="text-align:left;" id="extenddays" name="extenddays" class="bg-focus form-control" autoComplete="off">
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-4 control-label">备注：</label>

                                <div class="col-lg-2">
                                    <textarea style="text-align:left;" id="memo" name="memo" class="bg-focus form-control"></textarea>
                                </div>
                            </div>

                            <div class="form-group" align="center" style="margin-right:150px;margin-top:35px">
                                <div class="col-lg-4 col-lg-offset-3">
                                    <button type="submit" class="btn btn-primary">确认充值</submit>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function checkform() {
            if ((window.recharge.userid.value == "") || (window.recharge.quota.value == "") || (window.recharge.extenddays.value == "")) {
                alert("用户编号、额度、天数不能为空！");
                return false;
            } else {
                return true;
            }
        }
    </script>
</body>

</html>