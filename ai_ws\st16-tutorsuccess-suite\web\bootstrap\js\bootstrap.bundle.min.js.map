{"version": 3, "names": ["TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "triggerTransitionEnd", "element", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "keys", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "key", "value", "_unused", "defineProperty", "configurable", "get", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "getSelector", "hrefAttribute", "trim", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "for<PERSON>ach", "styles", "assign", "effect", "_ref2", "initialStyles", "position", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "getUAString", "uaData", "userAgentData", "brands", "item", "brand", "version", "userAgent", "isLayoutViewport", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "_ref4", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "computeStyles$1", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "updatedList", "rectToClientRect", "rect", "getClientRectFromMixedType", "clippingParent", "html", "layoutViewport", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "v", "withinMaxClamp", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "display", "popperConfig", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "<PERSON><PERSON><PERSON>", "blur", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "attributeRegex", "regex", "DefaultAllowlist", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "allowList", "content", "extraClass", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "click", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "scrollTo", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "activeNodes", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/userAgent.js", "../../node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // todo: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    let evt = new Event(event, { bubbles, cancelable: true })\n    evt = hydrateObj(evt, args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index.js'\nimport Manipulator from '../dom/manipulator.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.0-alpha1'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return parseSelector(selector)\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport { isDisabled } from './index.js'\nimport SelectorEngine from '../dom/selector-engine.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config.js'\nimport EventHandler from '../dom/event-handler.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Swipe from './util/swipe.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport { execute, executeAfterTransition, getElement, reflow } from './index.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, isRTL, isVisible, reflow } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport ScrollBarHelper from './util/scrollbar.js'\nimport BaseComponent from './base-component.js'\nimport Backdrop from './util/backdrop.js'\nimport FocusTrap from './util/focustrap.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport FocusTrap from './util/focustrap.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport BaseComponent from './base-component.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 0],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // todo v6 remove this OR make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index.js'\nimport Tooltip from './tooltip.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport BaseComponent from './base-component.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index.js'\nimport EventHandler from './dom/event-handler.js'\nimport BaseComponent from './base-component.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.3.0-alpha1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Alert from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;0OAOA,MAEMA,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,iBAAiB,CAACC,EAAOC,IAAQ,IAAGJ,IAAIC,OAAOG,QAGtEN,GA+CHO,EAAuBC,IAC3BA,EAAQC,cAAc,IAAIC,MAAMZ,GAAgB,EAG5Ca,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCC,SAASC,cAAcnB,EAAca,IAGvC,KAGHO,EAAYX,IAChB,IAAKG,EAAUH,IAAgD,IAApCA,EAAQY,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiBd,GAASe,iBAAiB,cAE9DC,EAAgBhB,EAAQiB,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkBhB,EAAS,CAC7B,MAAMkB,EAAUlB,EAAQiB,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,CAAgB,EAGnBO,EAAapB,IACZA,GAAWA,EAAQM,WAAae,KAAKC,gBAItCtB,EAAQuB,UAAUC,SAAS,mBAIC,IAArBxB,EAAQyB,SACVzB,EAAQyB,SAGVzB,EAAQ0B,aAAa,aAAoD,UAArC1B,EAAQ2B,aAAa,aAG5DC,EAAiB5B,IACrB,IAAKS,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxB9B,EAAQ+B,YAA4B,CAC7C,MAAMC,EAAOhC,EAAQ+B,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAIhC,aAAmBiC,WACdjC,EAIJA,EAAQmB,WAINS,EAAe5B,EAAQmB,YAHrB,IAGgC,EAGrCe,EAAO,OAUPC,EAASnC,IACbA,EAAQoC,YAAY,EAGhBC,EAAY,IACZ5C,OAAO6C,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxCjC,OAAO6C,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQ,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,GACF,IAIJL,EAA0BgB,KAAKX,IAE/BA,GAoBA,EAGEY,EAAU,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,KAAoBC,GAAQC,EAGxEC,EAAyB,CAAChB,EAAUiB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAN,EAAQZ,GAIV,MACMmB,EA7LiChE,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIiE,mBAAEA,EAAkBC,gBAAEA,GAAoBzE,OAAOqB,iBAAiBd,GAEtE,MAAMmE,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,CAOoG,EAyKpFM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAU,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoBtF,EAAgBoF,GACtDjB,EAAQZ,GAAS,EAGnBiB,EAAkBP,iBAAiBjE,EAAgBoF,GACnDG,YAAW,KACJJ,GACH1E,EAAqB+D,EACvB,GACCE,EAAiB,EAYhBc,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKvE,OACxB,IAAI4E,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,KAAI,EC7QrDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAanG,EAASoG,GAC7B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiB7F,EAAQ6F,UAAYA,GACjE,CAEA,SAASQ,EAAiBrG,GACxB,MAAMoG,EAAMD,EAAanG,GAKzB,OAHAA,EAAQ6F,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC/E,CAEA,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAamB,IAAIF,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EACjC,CAEA,SAASG,EAAWrH,EAAS+G,EAAmBrC,EAASsC,EAAoBM,GAC3E,GAAiC,iBAAtBP,IAAmC/G,EAC5C,OAGF,IAAKiH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMyB,EAAerE,GACZ,SAAU2D,GACf,IAAKA,EAAMW,eAAkBX,EAAMW,gBAAkBX,EAAMY,iBAAmBZ,EAAMY,eAAejG,SAASqF,EAAMW,eAChH,OAAOtE,EAAGwE,KAAKC,KAAMd,E,EAK3BL,EAAWe,EAAaf,EAC1B,CAEA,MAAMD,EAASF,EAAiBrG,GAC1B4H,EAAWrB,EAAOW,KAAeX,EAAOW,GAAa,IACrDW,EAAmBvB,EAAYsB,EAAUpB,EAAUS,EAAcvC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMlB,EAAMD,EAAaK,EAAUO,EAAkBnH,QAAQ6F,EAAgB,KACvEvC,EAAK+D,EAxEb,SAAoCjH,EAASR,EAAU0D,GACrD,OAAO,SAASwB,EAAQmC,GACtB,MAAMiB,EAAc9H,EAAQ+H,iBAAiBvI,GAE7C,IAAK,IAAImF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOxD,WACtE,IAAK,MAAM6G,KAAcF,EACvB,GAAIE,IAAerD,EAUnB,OANAsD,EAAWpB,EAAO,CAAEY,eAAgB9C,IAEhCD,EAAQ4C,QACVY,EAAaC,IAAInI,EAAS6G,EAAMuB,KAAM5I,EAAU0D,GAG3CA,EAAGmF,MAAM1D,EAAQ,CAACkC,G,CAIjC,CAqDIyB,CAA2BtI,EAAS0E,EAAS8B,GArFjD,SAA0BxG,EAASkD,GACjC,OAAO,SAASwB,EAAQmC,GAOtB,OANAoB,EAAWpB,EAAO,CAAEY,eAAgBzH,IAEhC0E,EAAQ4C,QACVY,EAAaC,IAAInI,EAAS6G,EAAMuB,KAAMlF,GAGjCA,EAAGmF,MAAMrI,EAAS,CAAC6G,G,CAE9B,CA4EI0B,CAAiBvI,EAASwG,GAE5BtD,EAAGuD,mBAAqBQ,EAAcvC,EAAU,KAChDxB,EAAGsD,SAAWA,EACdtD,EAAGoE,OAASA,EACZpE,EAAG2C,SAAWO,EACdwB,EAASxB,GAAOlD,EAEhBlD,EAAQuD,iBAAiB2D,EAAWhE,EAAI+D,EAC1C,CAEA,SAASuB,EAAcxI,EAASuG,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMvD,EAAKoD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CvD,IAILlD,EAAQ4E,oBAAoBsC,EAAWhE,EAAIuF,QAAQhC,WAC5CF,EAAOW,GAAWhE,EAAG2C,UAC9B,CAEA,SAAS6C,EAAyB1I,EAASuG,EAAQW,EAAWyB,GAC5D,MAAMC,EAAoBrC,EAAOW,IAAc,GAE/C,IAAK,MAAO2B,EAAYhC,KAAUH,OAAOoC,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAcxI,EAASuG,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAGtE,CAEA,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMjH,QAAQ8F,EAAgB,IAC/BI,EAAae,IAAUA,CAChC,CAEA,MAAMqB,EAAe,CACnBc,GAAGhJ,EAAS6G,EAAOnC,EAASsC,GAC1BK,EAAWrH,EAAS6G,EAAOnC,EAASsC,GAAoB,E,EAG1DiC,IAAIjJ,EAAS6G,EAAOnC,EAASsC,GAC3BK,EAAWrH,EAAS6G,EAAOnC,EAASsC,GAAoB,E,EAG1DmB,IAAInI,EAAS+G,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmC/G,EAC5C,OAGF,MAAOiH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFkC,EAAchC,IAAcH,EAC5BR,EAASF,EAAiBrG,GAC1B4I,EAAoBrC,EAAOW,IAAc,GACzCiC,EAAcpC,EAAkBqC,WAAW,KAEjD,QAAwB,IAAb5C,EAAX,CAUA,GAAI2C,EACF,IAAK,MAAME,KAAgB3C,OAAO4C,KAAK/C,GACrCmC,EAAyB1I,EAASuG,EAAQ8C,EAActC,EAAkBwC,MAAM,IAIpF,IAAK,MAAOC,EAAa3C,KAAUH,OAAOoC,QAAQF,GAAoB,CACpE,MAAMC,EAAaW,EAAY5J,QAAQ+F,EAAe,IAEjDuD,IAAenC,EAAkBgC,SAASF,IAC7CL,EAAcxI,EAASuG,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBAEpE,CAdA,KARA,CAEE,IAAKC,OAAO4C,KAAKV,GAAmBpI,OAClC,OAGFgI,EAAcxI,EAASuG,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE9E,C,EAiBF+E,QAAQzJ,EAAS6G,EAAOlD,GACtB,GAAqB,iBAAVkD,IAAuB7G,EAChC,OAAO,KAGT,MAAM8C,EAAIT,IAIV,IAAIqH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALHhD,IADFM,EAAaN,IAQZ/D,IACjB4G,EAAc5G,EAAE5C,MAAM2G,EAAOlD,GAE7Bb,EAAE9C,GAASyJ,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,IAAIC,EAAM,IAAI/J,MAAM2G,EAAO,CAAE8C,UAASO,YAAY,IAelD,OAdAD,EAAMhC,EAAWgC,EAAKtG,GAElBkG,GACFI,EAAIE,iBAGFP,GACF5J,EAAQC,cAAcgK,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAShC,EAAWmC,EAAKC,EAAO,IAC9B,IAAK,MAAOC,EAAKC,KAAU7D,OAAOoC,QAAQuB,GACxC,IACED,EAAIE,GAAOC,CAQb,CAPE,MAAMC,GACN9D,OAAO+D,eAAeL,EAAKE,EAAK,CAC9BI,cAAc,EACdC,IAAG,IACMJ,GAGb,CAGF,OAAOH,CACT,CChTA,MAAMQ,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAI/K,EAASsK,EAAKU,GACXJ,EAAWxD,IAAIpH,IAClB4K,EAAWG,IAAI/K,EAAS,IAAI6K,KAG9B,MAAMI,EAAcL,EAAWD,IAAI3K,GAI9BiL,EAAY7D,IAAIkD,IAA6B,IAArBW,EAAYC,KAMzCD,EAAYF,IAAIT,EAAKU,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAY3B,QAAQ,M,EAOhIqB,IAAG,CAAC3K,EAASsK,IACPM,EAAWxD,IAAIpH,IACV4K,EAAWD,IAAI3K,GAAS2K,IAAIL,IAG9B,KAGTiB,OAAOvL,EAASsK,GACd,IAAKM,EAAWxD,IAAIpH,GAClB,OAGF,MAAMiL,EAAcL,EAAWD,IAAI3K,GAEnCiL,EAAYO,OAAOlB,GAGM,IAArBW,EAAYC,MACdN,EAAWY,OAAOxL,EAEtB,GC9CF,SAASyL,EAAclB,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAUnG,OAAOmG,GAAOmB,WAC1B,OAAOtH,OAAOmG,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOoB,KAAKC,MAAMC,mBAAmBtB,GAGvC,CAFE,MAAMC,GACN,OAAOD,CACT,CACF,CAEA,SAASuB,EAAiBxB,GACxB,OAAOA,EAAI1K,QAAQ,UAAUmM,GAAQ,IAAGA,EAAIC,iBAC9C,CAEA,MAAMC,EAAc,CAClBC,iBAAiBlM,EAASsK,EAAKC,GAC7BvK,EAAQmM,aAAc,WAAUL,EAAiBxB,KAAQC,E,EAG3D6B,oBAAoBpM,EAASsK,GAC3BtK,EAAQqM,gBAAiB,WAAUP,EAAiBxB,K,EAGtDgC,kBAAkBtM,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMuM,EAAa,GACbC,EAAS9F,OAAO4C,KAAKtJ,EAAQyM,SAASC,QAAOpC,GAAOA,EAAIlB,WAAW,QAAUkB,EAAIlB,WAAW,cAElG,IAAK,MAAMkB,KAAOkC,EAAQ,CACxB,IAAIG,EAAUrC,EAAI1K,QAAQ,MAAO,IACjC+M,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQpD,MAAM,EAAGoD,EAAQnM,QACrE+L,EAAWI,GAAWlB,EAAczL,EAAQyM,QAAQnC,GACtD,CAEA,OAAOiC,C,EAGTM,iBAAgB,CAAC7M,EAASsK,IACjBmB,EAAczL,EAAQ2B,aAAc,WAAUmK,EAAiBxB,QCpD1E,MAAMwC,EAEOC,qBACT,MAAO,EACT,CAEWC,yBACT,MAAO,EACT,CAEWhK,kBACT,MAAM,IAAIiK,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQnN,GACtB,MAAMuN,EAAapN,EAAUH,GAAWiM,EAAYY,iBAAiB7M,EAAS,UAAY,GAE1F,MAAO,IACF2H,KAAK6F,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CpN,EAAUH,GAAWiM,EAAYK,kBAAkBtM,GAAW,MAC5C,iBAAXmN,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc9F,KAAK6F,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkBjH,OAAOoC,QAAQ2E,GAAc,CACnE,MAAMlD,EAAQ4C,EAAOO,GACfE,EAAYzN,EAAUoK,GAAS,UJ1BrCnK,OADSA,EI2B+CmK,GJzBlD,GAAEnK,IAGLsG,OAAOmH,UAAUnC,SAAShE,KAAKtH,GAAQP,MAAM,eAAe,GAAGmM,cIwBlE,IAAK,IAAI8B,OAAOH,GAAeI,KAAKH,GAClC,MAAM,IAAII,UACP,GAAErG,KAAK6F,YAAYxK,KAAKiL,0BAA0BP,qBAA4BE,yBAAiCD,MAGtH,CJlCWvN,KImCb,ECvCF,MAAM8N,UAAsBpB,EAC1BU,YAAYxN,EAASmN,GACnBgB,SAEAnO,EAAUO,EAAWP,MAKrB2H,KAAKyG,SAAWpO,EAChB2H,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAE/BrC,EAAKC,IAAIpD,KAAKyG,SAAUzG,KAAK6F,YAAYc,SAAU3G,MACrD,CAGA4G,UACEzD,EAAKS,OAAO5D,KAAKyG,SAAUzG,KAAK6F,YAAYc,UAC5CpG,EAAaC,IAAIR,KAAKyG,SAAUzG,KAAK6F,YAAYgB,WAEjD,IAAK,MAAMC,KAAgB/H,OAAOgI,oBAAoB/G,MACpDA,KAAK8G,GAAgB,IAEzB,CAEAE,eAAe9L,EAAU7C,EAAS4O,GAAa,GAC7C/K,EAAuBhB,EAAU7C,EAAS4O,EAC5C,CAEA1B,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,EAAQxF,KAAKyG,UAC3CjB,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAGA0B,mBAAmB7O,GACjB,OAAO8K,EAAKH,IAAIpK,EAAWP,GAAU2H,KAAK2G,SAC5C,CAEAO,2BAA2B7O,EAASmN,EAAS,IAC3C,OAAOxF,KAAKmH,YAAY9O,IAAY,IAAI2H,KAAK3H,EAA2B,iBAAXmN,EAAsBA,EAAS,KAC9F,CAEW4B,qBACT,MApDY,cAqDd,CAEWT,sBACT,MAAQ,MAAK3G,KAAK3E,MACpB,CAEWwL,uBACT,MAAQ,IAAG7G,KAAK2G,UAClB,CAEAO,iBAAiB9L,GACf,MAAQ,GAAEA,IAAO4E,KAAK6G,WACxB,ECxEF,MAAMQ,EAAchP,IAClB,IAAIR,EAAWQ,EAAQ2B,aAAa,kBAEpC,IAAKnC,GAAyB,MAAbA,EAAkB,CACjC,IAAIyP,EAAgBjP,EAAQ2B,aAAa,QAMzC,IAAKsN,IAAmBA,EAAclG,SAAS,OAASkG,EAAc7F,WAAW,KAC/E,OAAO,KAIL6F,EAAclG,SAAS,OAASkG,EAAc7F,WAAW,OAC3D6F,EAAiB,IAAGA,EAAc1K,MAAM,KAAK,MAG/C/E,EAAWyP,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAO3P,EAAcC,EAAS,EAG1B2P,EAAiB,CACrBvI,KAAI,CAACpH,EAAUQ,EAAUS,SAASoB,kBACzB,GAAGuN,UAAUC,QAAQxB,UAAU9F,iBAAiBL,KAAK1H,EAASR,IAGvE8P,QAAO,CAAC9P,EAAUQ,EAAUS,SAASoB,kBAC5BwN,QAAQxB,UAAUnN,cAAcgH,KAAK1H,EAASR,GAGvD+P,SAAQ,CAACvP,EAASR,IACT,GAAG4P,UAAUpP,EAAQuP,UAAU7C,QAAO8C,GAASA,EAAMC,QAAQjQ,KAGtEkQ,QAAQ1P,EAASR,GACf,MAAMkQ,EAAU,GAChB,IAAIC,EAAW3P,EAAQmB,WAAWF,QAAQzB,GAE1C,KAAOmQ,GACLD,EAAQlM,KAAKmM,GACbA,EAAWA,EAASxO,WAAWF,QAAQzB,GAGzC,OAAOkQ,C,EAGTE,KAAK5P,EAASR,GACZ,IAAIqQ,EAAW7P,EAAQ8P,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQjQ,GACnB,MAAO,CAACqQ,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,E,EAGTC,KAAK/P,EAASR,GACZ,IAAIuQ,EAAO/P,EAAQgQ,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQjQ,GACf,MAAO,CAACuQ,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,E,EAGTC,kBAAkBjQ,GAChB,MAAMkQ,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAI3Q,GAAa,GAAEA,2BAAiC4Q,KAAK,KAE3D,OAAOzI,KAAKf,KAAKsJ,EAAYlQ,GAAS0M,QAAO2D,IAAOjP,EAAWiP,IAAO1P,EAAU0P,I,EAGlFC,uBAAuBtQ,GACrB,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAIR,GACK2P,EAAeG,QAAQ9P,GAAYA,EAGrC,I,EAGT+Q,uBAAuBvQ,GACrB,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAOR,EAAW2P,EAAeG,QAAQ9P,GAAY,I,EAGvDgR,gCAAgCxQ,GAC9B,MAAMR,EAAWwP,EAAYhP,GAE7B,OAAOR,EAAW2P,EAAevI,KAAKpH,GAAY,EACpD,GC/GIiR,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUlC,YACvCzL,EAAO2N,EAAU1N,KAEvBkF,EAAac,GAAGvI,SAAUmQ,EAAa,qBAAoB7N,OAAU,SAAU8D,GAK7E,GAJI,CAAC,IAAK,QAAQkC,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,MACb,OAGF,MAAMhD,EAASwK,EAAeoB,uBAAuB5I,OAASA,KAAK1G,QAAS,IAAG8B,KAC9D2N,EAAUI,oBAAoBnM,GAGtCgM,IACX,GAAE,ECAJ,MAAMI,UAAc7C,EAEPlL,kBACT,MAhBS,OAiBX,CAGAgO,QAGE,GAFmB9I,EAAauB,QAAQ9B,KAAKyG,SAjB5B,kBAmBFvE,iBACb,OAGFlC,KAAKyG,SAAS7M,UAAUgK,OApBJ,QAsBpB,MAAMqD,EAAajH,KAAKyG,SAAS7M,UAAUC,SAvBvB,QAwBpBmG,KAAKgH,gBAAe,IAAMhH,KAAKsJ,mBAAmBtJ,KAAKyG,SAAUQ,EACnE,CAGAqC,kBACEtJ,KAAKyG,SAAS7C,SACdrD,EAAauB,QAAQ9B,KAAKyG,SA/BR,mBAgClBzG,KAAK4G,SACP,CAGAM,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoBnJ,MAEvC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KANb,CAOF,GACF,EAOF8I,EAAqBM,EAAO,SAM5BpO,EAAmBoO,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAepD,EAERlL,kBACT,MAhBS,QAiBX,CAGAuO,SAEE5J,KAAKyG,SAASjC,aAAa,eAAgBxE,KAAKyG,SAAS7M,UAAUgQ,OAjB7C,UAkBxB,CAGA1C,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoBnJ,MAEzB,WAAXwF,GACFgE,EAAKhE,IAET,GACF,EAOFjF,EAAac,GAAGvI,SAlCc,2BAkCkB4Q,GAAsBxK,IACpEA,EAAMsD,iBAEN,MAAMqH,EAAS3K,EAAMlC,OAAO1D,QAAQoQ,GACvBC,EAAOR,oBAAoBU,GAEnCD,QAAQ,IAOf5O,EAAmB2O,GCtDnB,MAYMvE,EAAU,CACd0E,YAAa,KACbC,aAAc,KACdC,cAAe,MAGX3E,EAAc,CAClByE,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,UAAc9E,EAClBU,YAAYxN,EAASmN,GACnBgB,QACAxG,KAAKyG,SAAWpO,EAEXA,GAAY4R,EAAMC,gBAIvBlK,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAKmK,QAAU,EACfnK,KAAKoK,sBAAwBtJ,QAAQhJ,OAAOuS,cAC5CrK,KAAKsK,cACP,CAGWlF,qBACT,OAAOA,CACT,CAEWC,yBACT,OAAOA,CACT,CAEWhK,kBACT,MArDS,OAsDX,CAGAuL,UACErG,EAAaC,IAAIR,KAAKyG,SAzDR,YA0DhB,CAGA8D,OAAOrL,GACAc,KAAKoK,sBAMNpK,KAAKwK,wBAAwBtL,KAC/Bc,KAAKmK,QAAUjL,EAAMuL,SANrBzK,KAAKmK,QAAUjL,EAAMwL,QAAQ,GAAGD,OAQpC,CAEAE,KAAKzL,GACCc,KAAKwK,wBAAwBtL,KAC/Bc,KAAKmK,QAAUjL,EAAMuL,QAAUzK,KAAKmK,SAGtCnK,KAAK4K,eACL9O,EAAQkE,KAAK0G,QAAQoD,YACvB,CAEAe,MAAM3L,GACJc,KAAKmK,QAAUjL,EAAMwL,SAAWxL,EAAMwL,QAAQ7R,OAAS,EACrD,EACAqG,EAAMwL,QAAQ,GAAGD,QAAUzK,KAAKmK,OACpC,CAEAS,eACE,MAAME,EAAYnN,KAAKoN,IAAI/K,KAAKmK,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAY9K,KAAKmK,QAEnCnK,KAAKmK,QAAU,EAEVa,GAILlP,EAAQkP,EAAY,EAAIhL,KAAK0G,QAAQsD,cAAgBhK,KAAK0G,QAAQqD,aACpE,CAEAO,cACMtK,KAAKoK,uBACP7J,EAAac,GAAGrB,KAAKyG,SAxGA,wBAwG6BvH,GAASc,KAAKuK,OAAOrL,KACvEqB,EAAac,GAAGrB,KAAKyG,SAxGF,sBAwG6BvH,GAASc,KAAK2K,KAAKzL,KAEnEc,KAAKyG,SAAS7M,UAAUqR,IAvGG,mBAyG3B1K,EAAac,GAAGrB,KAAKyG,SAhHD,uBAgH6BvH,GAASc,KAAKuK,OAAOrL,KACtEqB,EAAac,GAAGrB,KAAKyG,SAhHF,sBAgH6BvH,GAASc,KAAK6K,MAAM3L,KACpEqB,EAAac,GAAGrB,KAAKyG,SAhHH,qBAgH6BvH,GAASc,KAAK2K,KAAKzL,KAEtE,CAEAsL,wBAAwBtL,GACtB,OAAOc,KAAKoK,wBAjHS,QAiHiBlL,EAAMgM,aAlHrB,UAkHyDhM,EAAMgM,YACxF,CAGAhE,qBACE,MAAO,iBAAkBpO,SAASoB,iBAAmBiR,UAAUC,eAAiB,CAClF,ECrHF,MASMC,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,EAClBM,WAAmBP,GAGfnG,GAAU,CACd2G,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF/G,GAAc,CAClB0G,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiB9F,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKsM,UAAY,KACjBtM,KAAKuM,eAAiB,KACtBvM,KAAKwM,YAAa,EAClBxM,KAAKyM,aAAe,KACpBzM,KAAK0M,aAAe,KAEpB1M,KAAK2M,mBAAqBnF,EAAeG,QAzCjB,uBAyC8C3H,KAAKyG,UAC3EzG,KAAK4M,qBAED5M,KAAK0G,QAAQwF,OAASR,IACxB1L,KAAK6M,OAET,CAGWzH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA9FS,UA+FX,CAGA+M,OACEpI,KAAK8M,OAAOzB,EACd,CAEA0B,mBAIOjU,SAASkU,QAAUhU,EAAUgH,KAAKyG,WACrCzG,KAAKoI,MAET,CAEAH,OACEjI,KAAK8M,OAAOxB,EACd,CAEAW,QACMjM,KAAKwM,YACPpU,EAAqB4H,KAAKyG,UAG5BzG,KAAKiN,gBACP,CAEAJ,QACE7M,KAAKiN,iBACLjN,KAAKkN,kBAELlN,KAAKsM,UAAYa,aAAY,IAAMnN,KAAK+M,mBAAmB/M,KAAK0G,QAAQqF,SAC1E,CAEAqB,oBACOpN,KAAK0G,QAAQwF,OAIdlM,KAAKwM,WACPjM,EAAae,IAAItB,KAAKyG,SAAUgF,IAAY,IAAMzL,KAAK6M,UAIzD7M,KAAK6M,QACP,CAEAQ,GAAG5P,GACD,MAAM6P,EAAQtN,KAAKuN,YACnB,GAAI9P,EAAQ6P,EAAMzU,OAAS,GAAK4E,EAAQ,EACtC,OAGF,GAAIuC,KAAKwM,WAEP,YADAjM,EAAae,IAAItB,KAAKyG,SAAUgF,IAAY,IAAMzL,KAAKqN,GAAG5P,KAI5D,MAAM+P,EAAcxN,KAAKyN,cAAczN,KAAK0N,cAC5C,GAAIF,IAAgB/P,EAClB,OAGF,MAAMkQ,EAAQlQ,EAAQ+P,EAAcnC,EAAaC,EAEjDtL,KAAK8M,OAAOa,EAAOL,EAAM7P,GAC3B,CAEAmJ,UACM5G,KAAK0M,cACP1M,KAAK0M,aAAa9F,UAGpBJ,MAAMI,SACR,CAGAlB,kBAAkBF,GAEhB,OADAA,EAAOoI,gBAAkBpI,EAAOuG,SACzBvG,CACT,CAEAoH,qBACM5M,KAAK0G,QAAQsF,UACfzL,EAAac,GAAGrB,KAAKyG,SApKJ,uBAoK6BvH,GAASc,KAAK6N,SAAS3O,KAG5C,UAAvBc,KAAK0G,QAAQuF,QACf1L,EAAac,GAAGrB,KAAKyG,SAvKD,0BAuK6B,IAAMzG,KAAKiM,UAC5D1L,EAAac,GAAGrB,KAAKyG,SAvKD,0BAuK6B,IAAMzG,KAAKoN,uBAG1DpN,KAAK0G,QAAQyF,OAASlC,EAAMC,eAC9BlK,KAAK8N,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOvG,EAAevI,KAhKX,qBAgKmCe,KAAKyG,UAC5DlG,EAAac,GAAG0M,EAhLI,yBAgLmB7O,GAASA,EAAMsD,mBAGxD,MAqBMwL,EAAc,CAClBjE,aAAc,IAAM/J,KAAK8M,OAAO9M,KAAKiO,kBAAkB1C,IACvDvB,cAAe,IAAMhK,KAAK8M,OAAO9M,KAAKiO,kBAAkBzC,IACxD1B,YAxBkB,KACS,UAAvB9J,KAAK0G,QAAQuF,QAYjBjM,KAAKiM,QACDjM,KAAKyM,cACPyB,aAAalO,KAAKyM,cAGpBzM,KAAKyM,aAAevP,YAAW,IAAM8C,KAAKoN,qBAjNjB,IAiN+DpN,KAAK0G,QAAQqF,UAAS,GAShH/L,KAAK0M,aAAe,IAAIzC,EAAMjK,KAAKyG,SAAUuH,EAC/C,CAEAH,SAAS3O,GACP,GAAI,kBAAkBkH,KAAKlH,EAAMlC,OAAOkM,SACtC,OAGF,MAAM8B,EAAYY,GAAiB1M,EAAMyD,KACrCqI,IACF9L,EAAMsD,iBACNxC,KAAK8M,OAAO9M,KAAKiO,kBAAkBjD,IAEvC,CAEAyC,cAAcpV,GACZ,OAAO2H,KAAKuN,YAAY7P,QAAQrF,EAClC,CAEA8V,2BAA2B1Q,GACzB,IAAKuC,KAAK2M,mBACR,OAGF,MAAMyB,EAAkB5G,EAAeG,QA1NnB,UA0N4C3H,KAAK2M,oBAErEyB,EAAgBxU,UAAUgK,OAAO+H,IACjCyC,EAAgB1J,gBAAgB,gBAEhC,MAAM2J,EAAqB7G,EAAeG,QAAS,sBAAqBlK,MAAWuC,KAAK2M,oBAEpF0B,IACFA,EAAmBzU,UAAUqR,IAAIU,IACjC0C,EAAmB7J,aAAa,eAAgB,QAEpD,CAEA0I,kBACE,MAAM7U,EAAU2H,KAAKuM,gBAAkBvM,KAAK0N,aAE5C,IAAKrV,EACH,OAGF,MAAMiW,EAAkB7R,OAAO8R,SAASlW,EAAQ2B,aAAa,oBAAqB,IAElFgG,KAAK0G,QAAQqF,SAAWuC,GAAmBtO,KAAK0G,QAAQkH,eAC1D,CAEAd,OAAOa,EAAOtV,EAAU,MACtB,GAAI2H,KAAKwM,WACP,OAGF,MAAMnP,EAAgB2C,KAAK0N,aACrBc,EAASb,IAAUtC,EACnBoD,EAAcpW,GAAW8E,EAAqB6C,KAAKuN,YAAalQ,EAAemR,EAAQxO,KAAK0G,QAAQ0F,MAE1G,GAAIqC,IAAgBpR,EAClB,OAGF,MAAMqR,EAAmB1O,KAAKyN,cAAcgB,GAEtCE,EAAeC,GACZrO,EAAauB,QAAQ9B,KAAKyG,SAAUmI,EAAW,CACpD/O,cAAe4O,EACfzD,UAAWhL,KAAK6O,kBAAkBlB,GAClChK,KAAM3D,KAAKyN,cAAcpQ,GACzBgQ,GAAIqB,IAMR,GAFmBC,EA5RF,qBA8RFzM,iBACb,OAGF,IAAK7E,IAAkBoR,EAGrB,OAGF,MAAMK,EAAYhO,QAAQd,KAAKsM,WAC/BtM,KAAKiM,QAELjM,KAAKwM,YAAa,EAElBxM,KAAKmO,2BAA2BO,GAChC1O,KAAKuM,eAAiBkC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAY7U,UAAUqR,IAAI+D,GAE1BxU,EAAOiU,GAEPpR,EAAczD,UAAUqR,IAAI8D,GAC5BN,EAAY7U,UAAUqR,IAAI8D,GAa1B/O,KAAKgH,gBAXoB,KACvByH,EAAY7U,UAAUgK,OAAOmL,EAAsBC,GACnDP,EAAY7U,UAAUqR,IAAIU,IAE1BtO,EAAczD,UAAUgK,OAAO+H,GAAmBqD,EAAgBD,GAElE/O,KAAKwM,YAAa,EAElBmC,EAAalD,GAAW,GAGYpO,EAAe2C,KAAKiP,eAEtDH,GACF9O,KAAK6M,OAET,CAEAoC,cACE,OAAOjP,KAAKyG,SAAS7M,UAAUC,SAlUV,QAmUvB,CAEA6T,aACE,OAAOlG,EAAeG,QA9TGuH,wBA8T2BlP,KAAKyG,SAC3D,CAEA8G,YACE,OAAO/F,EAAevI,KAnUJ,iBAmUwBe,KAAKyG,SACjD,CAEAwG,iBACMjN,KAAKsM,YACP6C,cAAcnP,KAAKsM,WACnBtM,KAAKsM,UAAY,KAErB,CAEA2B,kBAAkBjD,GAChB,OAAIlQ,IACKkQ,IAAcO,EAAiBD,EAAaD,EAG9CL,IAAcO,EAAiBF,EAAaC,CACrD,CAEAuD,kBAAkBlB,GAChB,OAAI7S,IACK6S,IAAUrC,EAAaC,EAAiBC,EAG1CmC,IAAUrC,EAAaE,EAAkBD,CAClD,CAGArE,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO6C,GAASlD,oBAAoBnJ,KAAMwF,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,OAVEgE,EAAK6D,GAAG7H,EAWZ,GACF,EAOFjF,EAAac,GAAGvI,SAjYc,6BAeF,uCAkXyC,SAAUoG,GAC7E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAErD,IAAKhD,IAAWA,EAAOpD,UAAUC,SAAS6R,IACxC,OAGFxM,EAAMsD,iBAEN,MAAM4M,EAAW/C,GAASlD,oBAAoBnM,GACxCqS,EAAarP,KAAKhG,aAAa,oBAErC,OAAIqV,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhD9I,EAAYY,iBAAiBlF,KAAM,UACrCoP,EAAShH,YACTgH,EAAShC,sBAIXgC,EAASnH,YACTmH,EAAShC,oBACX,IAEA7M,EAAac,GAAGvJ,OA9Za,6BA8ZgB,KAC3C,MAAMwX,EAAY9H,EAAevI,KA9YR,6BAgZzB,IAAK,MAAMmQ,KAAYE,EACrBjD,GAASlD,oBAAoBiG,EAC/B,IAOFpU,EAAmBqR,ICncnB,MAWMkD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxB/F,GAAuB,8BAEvBtE,GAAU,CACdsK,OAAQ,KACR9F,QAAQ,GAGJvE,GAAc,CAClBqK,OAAQ,iBACR9F,OAAQ,WAOV,MAAM+F,WAAiBpJ,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAK4P,kBAAmB,EACxB5P,KAAK6P,cAAgB,GAErB,MAAMC,EAAatI,EAAevI,KAAKyK,IAEvC,IAAK,MAAMqG,KAAQD,EAAY,CAC7B,MAAMjY,EAAW2P,EAAemB,uBAAuBoH,GACjDC,EAAgBxI,EAAevI,KAAKpH,GACvCkN,QAAOkL,GAAgBA,IAAiBjQ,KAAKyG,WAE/B,OAAb5O,GAAqBmY,EAAcnX,QACrCmH,KAAK6P,cAAchU,KAAKkU,EAE5B,CAEA/P,KAAKkQ,sBAEAlQ,KAAK0G,QAAQgJ,QAChB1P,KAAKmQ,0BAA0BnQ,KAAK6P,cAAe7P,KAAKoQ,YAGtDpQ,KAAK0G,QAAQkD,QACf5J,KAAK4J,QAET,CAGWxE,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA9ES,UA+EX,CAGAuO,SACM5J,KAAKoQ,WACPpQ,KAAKqQ,OAELrQ,KAAKsQ,MAET,CAEAA,OACE,GAAItQ,KAAK4P,kBAAoB5P,KAAKoQ,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANIvQ,KAAK0G,QAAQgJ,SACfa,EAAiBvQ,KAAKwQ,uBA9EH,wCA+EhBzL,QAAO1M,GAAWA,IAAY2H,KAAKyG,WACnC+B,KAAInQ,GAAWsX,GAASxG,oBAAoB9Q,EAAS,CAAEuR,QAAQ,OAGhE2G,EAAe1X,QAAU0X,EAAe,GAAGX,iBAC7C,OAIF,GADmBrP,EAAauB,QAAQ9B,KAAKyG,SAvG7B,oBAwGDvE,iBACb,OAGF,IAAK,MAAMuO,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAY1Q,KAAK2Q,gBAEvB3Q,KAAKyG,SAAS7M,UAAUgK,OAAO4L,IAC/BxP,KAAKyG,SAAS7M,UAAUqR,IAAIwE,IAE5BzP,KAAKyG,SAASmK,MAAMF,GAAa,EAEjC1Q,KAAKmQ,0BAA0BnQ,KAAK6P,eAAe,GACnD7P,KAAK4P,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAGpK,cAAgBoK,EAAU9O,MAAM,KAG1E5B,KAAKgH,gBAdY,KACfhH,KAAK4P,kBAAmB,EAExB5P,KAAKyG,SAAS7M,UAAUgK,OAAO6L,IAC/BzP,KAAKyG,SAAS7M,UAAUqR,IAAIuE,GAAqBD,IAEjDvP,KAAKyG,SAASmK,MAAMF,GAAa,GAEjCnQ,EAAauB,QAAQ9B,KAAKyG,SAjIX,oBAiIiC,GAMpBzG,KAAKyG,UAAU,GAC7CzG,KAAKyG,SAASmK,MAAMF,GAAc,GAAE1Q,KAAKyG,SAASoK,MACpD,CAEAR,OACE,GAAIrQ,KAAK4P,mBAAqB5P,KAAKoQ,WACjC,OAIF,GADmB7P,EAAauB,QAAQ9B,KAAKyG,SA/I7B,oBAgJDvE,iBACb,OAGF,MAAMwO,EAAY1Q,KAAK2Q,gBAEvB3Q,KAAKyG,SAASmK,MAAMF,GAAc,GAAE1Q,KAAKyG,SAASqK,wBAAwBJ,OAE1ElW,EAAOwF,KAAKyG,UAEZzG,KAAKyG,SAAS7M,UAAUqR,IAAIwE,IAC5BzP,KAAKyG,SAAS7M,UAAUgK,OAAO4L,GAAqBD,IAEpD,IAAK,MAAMzN,KAAW9B,KAAK6P,cAAe,CACxC,MAAMxX,EAAUmP,EAAeoB,uBAAuB9G,GAElDzJ,IAAY2H,KAAKoQ,SAAS/X,IAC5B2H,KAAKmQ,0BAA0B,CAACrO,IAAU,EAE9C,CAEA9B,KAAK4P,kBAAmB,EASxB5P,KAAKyG,SAASmK,MAAMF,GAAa,GAEjC1Q,KAAKgH,gBATY,KACfhH,KAAK4P,kBAAmB,EACxB5P,KAAKyG,SAAS7M,UAAUgK,OAAO6L,IAC/BzP,KAAKyG,SAAS7M,UAAUqR,IAAIuE,IAC5BjP,EAAauB,QAAQ9B,KAAKyG,SA1KV,qBA0KiC,GAKrBzG,KAAKyG,UAAU,EAC/C,CAEA2J,SAAS/X,EAAU2H,KAAKyG,UACtB,OAAOpO,EAAQuB,UAAUC,SAAS0V,GACpC,CAGA7J,kBAAkBF,GAGhB,OAFAA,EAAOoE,OAAS9I,QAAQ0E,EAAOoE,QAC/BpE,EAAOkK,OAAS9W,EAAW4M,EAAOkK,QAC3BlK,CACT,CAEAmL,gBACE,OAAO3Q,KAAKyG,SAAS7M,UAAUC,SAtLL,uBAEhB,QACC,QAoLb,CAEAqW,sBACE,IAAKlQ,KAAK0G,QAAQgJ,OAChB,OAGF,MAAM9H,EAAW5H,KAAKwQ,uBAAuB9G,IAE7C,IAAK,MAAMrR,KAAWuP,EAAU,CAC9B,MAAMmJ,EAAWvJ,EAAeoB,uBAAuBvQ,GAEnD0Y,GACF/Q,KAAKmQ,0BAA0B,CAAC9X,GAAU2H,KAAKoQ,SAASW,GAE5D,CACF,CAEAP,uBAAuB3Y,GACrB,MAAM+P,EAAWJ,EAAevI,KA3MA,6BA2MiCe,KAAK0G,QAAQgJ,QAE9E,OAAOlI,EAAevI,KAAKpH,EAAUmI,KAAK0G,QAAQgJ,QAAQ3K,QAAO1M,IAAYuP,EAASxG,SAAS/I,IACjG,CAEA8X,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAanY,OAIlB,IAAK,MAAMR,KAAW2Y,EACpB3Y,EAAQuB,UAAUgQ,OAvNK,aAuNyBqH,GAChD5Y,EAAQmM,aAAa,gBAAiByM,EAE1C,CAGA/J,uBAAuB1B,GACrB,MAAMkB,EAAU,GAKhB,MAJsB,iBAAXlB,GAAuB,YAAYY,KAAKZ,KACjDkB,EAAQkD,QAAS,GAGZ5J,KAAKuJ,MAAK,WACf,MAAMC,EAAOmG,GAASxG,oBAAoBnJ,KAAM0G,GAEhD,GAAsB,iBAAXlB,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IACP,CACF,GACF,EAOFjF,EAAac,GAAGvI,SA1Pc,6BA0PkB4Q,IAAsB,SAAUxK,IAEjD,MAAzBA,EAAMlC,OAAOkM,SAAoBhK,EAAMY,gBAAmD,MAAjCZ,EAAMY,eAAeoJ,UAChFhK,EAAMsD,iBAGR,IAAK,MAAMnK,KAAWmP,EAAeqB,gCAAgC7I,MACnE2P,GAASxG,oBAAoB9Q,EAAS,CAAEuR,QAAQ,IAASA,QAE7D,IAMA5O,EAAmB2U,ICtSZ,IAAIuB,GAAM,MACNC,GAAS,SACTC,GAAQ,QACRC,GAAO,OACPC,GAAO,OACPC,GAAiB,CAACL,GAAKC,GAAQC,GAAOC,IACtCG,GAAQ,QACRC,GAAM,MACNC,GAAkB,kBAClBC,GAAW,WACXC,GAAS,SACTC,GAAY,YACZC,GAAmCP,GAAeQ,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIvK,OAAO,CAACwK,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAChE,GAAG,IACQS,GAA0B,GAAGzK,OAAO8J,GAAgB,CAACD,KAAOS,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIvK,OAAO,CAACwK,EAAWA,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAC3E,GAAG,IAEQU,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,IC9B5F,SAASE,GAAYxa,GAClC,OAAOA,GAAWA,EAAQya,UAAY,IAAIzO,cAAgB,IAC5D,CCFe,SAAS0O,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOlb,OAGT,GAAwB,oBAApBkb,EAAKjP,WAAkC,CACzC,IAAIkP,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBpb,MACjE,CAEE,OAAOkb,CACT,CCTA,SAASxa,GAAUwa,GAEjB,OAAOA,aADUD,GAAUC,GAAMtL,SACIsL,aAAgBtL,OACvD,CAEA,SAASyL,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,WACvD,CAEA,SAASC,GAAaL,GAEpB,MAA0B,oBAAf1Y,aAKJ0Y,aADUD,GAAUC,GAAM1Y,YACI0Y,aAAgB1Y,WACvD,CCwDA,MAAAgZ,GAAe,CACblY,KAAM,cACNmY,SAAS,EACTC,MAAO,QACPjY,GA5EF,SAAqBkY,GACnB,IAAIC,EAAQD,EAAKC,MACjB3U,OAAO4C,KAAK+R,EAAMC,UAAUC,SAAQ,SAAUxY,GAC5C,IAAIwV,EAAQ8C,EAAMG,OAAOzY,IAAS,GAC9BwJ,EAAa8O,EAAM9O,WAAWxJ,IAAS,GACvC/C,EAAUqb,EAAMC,SAASvY,GAExB+X,GAAc9a,IAAawa,GAAYxa,KAO5C0G,OAAO+U,OAAOzb,EAAQuY,MAAOA,GAC7B7R,OAAO4C,KAAKiD,GAAYgP,SAAQ,SAAUxY,GACxC,IAAIwH,EAAQgC,EAAWxJ,IAET,IAAVwH,EACFvK,EAAQqM,gBAAgBtJ,GAExB/C,EAAQmM,aAAapJ,GAAgB,IAAVwH,EAAiB,GAAKA,EAEzD,IACA,GACA,EAoDEmR,OAlDF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MACdO,EAAgB,CAClBrC,OAAQ,CACNsC,SAAUR,EAAMS,QAAQC,SACxB/C,KAAM,IACNH,IAAK,IACLmD,OAAQ,KAEVC,MAAO,CACLJ,SAAU,YAEZrC,UAAW,IASb,OAPA9S,OAAO+U,OAAOJ,EAAMC,SAAS/B,OAAOhB,MAAOqD,EAAcrC,QACzD8B,EAAMG,OAASI,EAEXP,EAAMC,SAASW,OACjBvV,OAAO+U,OAAOJ,EAAMC,SAASW,MAAM1D,MAAOqD,EAAcK,OAGnD,WACLvV,OAAO4C,KAAK+R,EAAMC,UAAUC,SAAQ,SAAUxY,GAC5C,IAAI/C,EAAUqb,EAAMC,SAASvY,GACzBwJ,EAAa8O,EAAM9O,WAAWxJ,IAAS,GAGvCwV,EAFkB7R,OAAO4C,KAAK+R,EAAMG,OAAOU,eAAenZ,GAAQsY,EAAMG,OAAOzY,GAAQ6Y,EAAc7Y,IAE7E2W,QAAO,SAAUnB,EAAO7K,GAElD,OADA6K,EAAM7K,GAAY,GACX6K,CACf,GAAS,IAEEuC,GAAc9a,IAAawa,GAAYxa,KAI5C0G,OAAO+U,OAAOzb,EAAQuY,MAAOA,GAC7B7R,OAAO4C,KAAKiD,GAAYgP,SAAQ,SAAUY,GACxCnc,EAAQqM,gBAAgB8P,EAChC,IACA,GACA,CACA,EASEC,SAAU,CAAC,kBCjFE,SAASC,GAAiBzC,GACvC,OAAOA,EAAUrV,MAAM,KAAK,EAC9B,CCHO,IAAIgB,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACX8W,GAAQhX,KAAKgX,MCFT,SAASC,KACtB,IAAIC,EAAS1J,UAAU2J,cAEvB,OAAc,MAAVD,GAAkBA,EAAOE,OACpBF,EAAOE,OAAOvM,KAAI,SAAUwM,GACjC,OAAOA,EAAKC,MAAQ,IAAMD,EAAKE,OACrC,IAAOzM,KAAK,KAGH0C,UAAUgK,SACnB,CCTe,SAASC,KACtB,OAAQ,iCAAiChP,KAAKwO,KAChD,CCCe,SAAS9D,GAAsBzY,EAASgd,EAAcC,QAC9C,IAAjBD,IACFA,GAAe,QAGO,IAApBC,IACFA,GAAkB,GAGpB,IAAIC,EAAald,EAAQyY,wBACrB0E,EAAS,EACTC,EAAS,EAETJ,GAAgBlC,GAAc9a,KAChCmd,EAASnd,EAAQqd,YAAc,GAAIf,GAAMY,EAAWI,OAAStd,EAAQqd,aAAmB,EACxFD,EAASpd,EAAQoC,aAAe,GAAIka,GAAMY,EAAWK,QAAUvd,EAAQoC,cAAoB,GAG7F,IACIob,GADOrd,GAAUH,GAAW0a,GAAU1a,GAAWP,QAC3B+d,eAEtBC,GAAoBV,MAAsBE,EAC1CS,GAAKR,EAAWlE,MAAQyE,GAAoBD,EAAiBA,EAAeG,WAAa,IAAMR,EAC/FS,GAAKV,EAAWrE,KAAO4E,GAAoBD,EAAiBA,EAAeK,UAAY,IAAMT,EAC7FE,EAAQJ,EAAWI,MAAQH,EAC3BI,EAASL,EAAWK,OAASH,EACjC,MAAO,CACLE,MAAOA,EACPC,OAAQA,EACR1E,IAAK+E,EACL7E,MAAO2E,EAAIJ,EACXxE,OAAQ8E,EAAIL,EACZvE,KAAM0E,EACNA,EAAGA,EACHE,EAAGA,EAEP,CCrCe,SAASE,GAAc9d,GACpC,IAAIkd,EAAazE,GAAsBzY,GAGnCsd,EAAQtd,EAAQqd,YAChBE,EAASvd,EAAQoC,aAUrB,OARIkD,KAAKoN,IAAIwK,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjBhY,KAAKoN,IAAIwK,EAAWK,OAASA,IAAW,IAC1CA,EAASL,EAAWK,QAGf,CACLG,EAAG1d,EAAQ2d,WACXC,EAAG5d,EAAQ6d,UACXP,MAAOA,EACPC,OAAQA,EAEZ,CCvBe,SAAS/b,GAAS6V,EAAQ7H,GACvC,IAAIuO,EAAWvO,EAAMzN,aAAeyN,EAAMzN,cAE1C,GAAIsV,EAAO7V,SAASgO,GAClB,OAAO,EAEJ,GAAIuO,GAAY/C,GAAa+C,GAAW,CACzC,IAAIhO,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQsH,EAAO2G,WAAWjO,GAC5B,OAAO,EAITA,EAAOA,EAAK5O,YAAc4O,EAAKkO,IACvC,OAAelO,EACf,CAGE,OAAO,CACT,CCrBe,SAASjP,GAAiBd,GACvC,OAAO0a,GAAU1a,GAASc,iBAAiBd,EAC7C,CCFe,SAASke,GAAele,GACrC,MAAO,CAAC,QAAS,KAAM,MAAMqF,QAAQmV,GAAYxa,KAAa,CAChE,CCFe,SAASme,GAAmBne,GAEzC,QAASG,GAAUH,GAAWA,EAAQ4a,cACtC5a,EAAQS,WAAahB,OAAOgB,UAAUoB,eACxC,CCFe,SAASuc,GAAcpe,GACpC,MAA6B,SAAzBwa,GAAYxa,GACPA,EAMPA,EAAQqe,cACRre,EAAQmB,aACR6Z,GAAahb,GAAWA,EAAQie,KAAO,OAEvCE,GAAmBne,EAGvB,CCVA,SAASse,GAAoBte,GAC3B,OAAK8a,GAAc9a,IACoB,UAAvCc,GAAiBd,GAAS6b,SAInB7b,EAAQue,aAHN,IAIX,CAwCe,SAASC,GAAgBxe,GAItC,IAHA,IAAIP,EAASib,GAAU1a,GACnBue,EAAeD,GAAoBte,GAEhCue,GAAgBL,GAAeK,IAA6D,WAA5Czd,GAAiByd,GAAc1C,UACpF0C,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B/D,GAAY+D,IAA0D,SAA9B/D,GAAY+D,IAAwE,WAA5Czd,GAAiByd,GAAc1C,UAC3Hpc,EAGF8e,GAhDT,SAA4Bve,GAC1B,IAAIye,EAAY,WAAW1Q,KAAKwO,MAGhC,GAFW,WAAWxO,KAAKwO,OAEfzB,GAAc9a,IAII,UAFXc,GAAiBd,GAEnB6b,SACb,OAAO,KAIX,IAAI6C,EAAcN,GAAcpe,GAMhC,IAJIgb,GAAa0D,KACfA,EAAcA,EAAYT,MAGrBnD,GAAc4D,IAAgB,CAAC,OAAQ,QAAQrZ,QAAQmV,GAAYkE,IAAgB,GAAG,CAC3F,IAAIC,EAAM7d,GAAiB4d,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAezZ,QAAQsZ,EAAII,aAAsBN,GAAgC,WAAnBE,EAAII,YAA2BN,GAAaE,EAAIjS,QAAyB,SAAfiS,EAAIjS,OACjO,OAAOgS,EAEPA,EAAcA,EAAYvd,UAEhC,CAEE,OAAO,IACT,CAgByB6d,CAAmBhf,IAAYP,CACxD,CCpEe,SAASwf,GAAyBrF,GAC/C,MAAO,CAAC,MAAO,UAAUvU,QAAQuU,IAAc,EAAI,IAAM,GAC3D,CCDO,SAASsF,GAAO1Z,EAAK+E,EAAOhF,GACjC,OAAO4Z,GAAQ3Z,EAAK4Z,GAAQ7U,EAAOhF,GACrC,CCFe,SAAS8Z,GAAmBC,GACzC,OAAO5Y,OAAO+U,OAAO,GCDd,CACL5C,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GDHuCsG,EACjD,CEHe,SAASC,GAAgBhV,EAAOjB,GAC7C,OAAOA,EAAKoQ,QAAO,SAAU8F,EAASlV,GAEpC,OADAkV,EAAQlV,GAAOC,EACRiV,CACX,GAAK,GACL,CCuFA,MAAAC,GAAe,CACb1c,KAAM,QACNmY,SAAS,EACTC,MAAO,OACPjY,GA9EF,SAAekY,GACb,IAAIsE,EAEArE,EAAQD,EAAKC,MACbtY,EAAOqY,EAAKrY,KACZ+Y,EAAUV,EAAKU,QACf6D,EAAetE,EAAMC,SAASW,MAC9B2D,EAAgBvE,EAAMwE,cAAcD,cACpCE,EAAgBzD,GAAiBhB,EAAMzB,WACvCmG,EAAOd,GAAyBa,GAEhCE,EADa,CAAChH,GAAMD,IAAO1T,QAAQya,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBW,EAAS5E,GAItD,OAAOgE,GAAsC,iBAH7CY,EAA6B,mBAAZA,EAAyBA,EAAQvZ,OAAO+U,OAAO,GAAIJ,EAAM6E,MAAO,CAC/EtG,UAAWyB,EAAMzB,aACbqG,GACkDA,EAAUV,GAAgBU,EAAS/G,IAC7F,CAmBsBiH,CAAgBrE,EAAQmE,QAAS5E,GACjD+E,EAAYtC,GAAc6B,GAC1BU,EAAmB,MAATN,EAAelH,GAAMG,GAC/BsH,EAAmB,MAATP,EAAejH,GAASC,GAClCwH,EAAUlF,EAAM6E,MAAM1G,UAAUwG,GAAO3E,EAAM6E,MAAM1G,UAAUuG,GAAQH,EAAcG,GAAQ1E,EAAM6E,MAAM3G,OAAOyG,GAC9GQ,EAAYZ,EAAcG,GAAQ1E,EAAM6E,MAAM1G,UAAUuG,GACxDU,EAAoBjC,GAAgBmB,GACpCe,EAAaD,EAA6B,MAATV,EAAeU,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9Chb,EAAM8Z,EAAce,GACpB9a,EAAMmb,EAAaN,EAAUJ,GAAOV,EAAcgB,GAClDQ,EAASJ,EAAa,EAAIN,EAAUJ,GAAO,EAAIa,EAC/CE,EAAS7B,GAAO1Z,EAAKsb,EAAQvb,GAE7Byb,EAAWjB,EACf1E,EAAMwE,cAAc9c,KAAS2c,EAAwB,IAA0BsB,GAAYD,EAAQrB,EAAsBuB,aAAeF,EAASD,EAAQpB,EAnB3J,CAoBA,EA4CEhE,OA1CF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MAEd6F,EADUvF,EAAMG,QACW9b,QAC3B2f,OAAoC,IAArBuB,EAA8B,sBAAwBA,EAErD,MAAhBvB,IAKwB,iBAAjBA,IACTA,EAAetE,EAAMC,SAAS/B,OAAO7Y,cAAcif,MAahDne,GAAS6Z,EAAMC,SAAS/B,OAAQoG,KAQrCtE,EAAMC,SAASW,MAAQ0D,EACzB,EASEvD,SAAU,CAAC,iBACX+E,iBAAkB,CAAC,oBCnGN,SAASC,GAAaxH,GACnC,OAAOA,EAAUrV,MAAM,KAAK,EAC9B,CCOA,IAAI8c,GAAa,CACfxI,IAAK,OACLE,MAAO,OACPD,OAAQ,OACRE,KAAM,QAgBD,SAASsI,GAAY3F,GAC1B,IAAI4F,EAEAhI,EAASoC,EAAMpC,OACfiI,EAAa7F,EAAM6F,WACnB5H,EAAY+B,EAAM/B,UAClB6H,EAAY9F,EAAM8F,UAClBC,EAAU/F,EAAM+F,QAChB7F,EAAWF,EAAME,SACjB8F,EAAkBhG,EAAMgG,gBACxBC,EAAWjG,EAAMiG,SACjBC,EAAelG,EAAMkG,aACrBC,EAAUnG,EAAMmG,QAChBC,EAAaL,EAAQhE,EACrBA,OAAmB,IAAfqE,EAAwB,EAAIA,EAChCC,EAAaN,EAAQ9D,EACrBA,OAAmB,IAAfoE,EAAwB,EAAIA,EAEhCC,EAAgC,mBAAjBJ,EAA8BA,EAAa,CAC5DnE,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAGLF,EAAIuE,EAAMvE,EACVE,EAAIqE,EAAMrE,EACV,IAAIsE,EAAOR,EAAQxF,eAAe,KAC9BiG,EAAOT,EAAQxF,eAAe,KAC9BkG,EAAQpJ,GACRqJ,EAAQxJ,GACRyJ,EAAM7iB,OAEV,GAAImiB,EAAU,CACZ,IAAIrD,EAAeC,GAAgBjF,GAC/BgJ,EAAa,eACbC,EAAY,cAEZjE,IAAiB7D,GAAUnB,IAGmB,WAA5CzY,GAFJyd,EAAeJ,GAAmB5E,IAECsC,UAAsC,aAAbA,IAC1D0G,EAAa,eACbC,EAAY,gBAOZ5I,IAAcf,KAAQe,IAAcZ,IAAQY,IAAcb,KAAU0I,IAAcrI,MACpFiJ,EAAQvJ,GAGR8E,IAFckE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeD,OACzFgB,EAAagE,IACEf,EAAWjE,OAC1BK,GAAK+D,EAAkB,GAAK,GAG1B/H,IAAcZ,KAASY,IAAcf,IAAOe,IAAcd,IAAW2I,IAAcrI,MACrFgJ,EAAQrJ,GAGR2E,IAFcoE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeF,MACzFiB,EAAaiE,IACEhB,EAAWlE,MAC1BI,GAAKiE,EAAkB,GAAK,EAElC,CAEE,IAgBMc,EAhBFC,EAAehc,OAAO+U,OAAO,CAC/BI,SAAUA,GACT+F,GAAYP,IAEXsB,GAAyB,IAAjBd,EAnFd,SAA2BzG,GACzB,IAAIsC,EAAItC,EAAKsC,EACTE,EAAIxC,EAAKwC,EAETgF,EADMnjB,OACIojB,kBAAoB,EAClC,MAAO,CACLnF,EAAGpB,GAAMoB,EAAIkF,GAAOA,GAAO,EAC3BhF,EAAGtB,GAAMsB,EAAIgF,GAAOA,GAAO,EAE/B,CA0EsCE,CAAkB,CACpDpF,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAML,OAHAF,EAAIiF,EAAMjF,EACVE,EAAI+E,EAAM/E,EAEN+D,EAGKjb,OAAO+U,OAAO,GAAIiH,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe7D,WAAa0D,EAAIO,kBAAoB,IAAM,EAAI,aAAenF,EAAI,OAASE,EAAI,MAAQ,eAAiBF,EAAI,OAASE,EAAI,SAAU6E,IAG5R/b,OAAO+U,OAAO,GAAIiH,IAAenB,EAAkB,IAAoBc,GAASF,EAAOvE,EAAI,KAAO,GAAI2D,EAAgBa,GAASF,EAAOxE,EAAI,KAAO,GAAI6D,EAAgB3C,UAAY,GAAI2C,GAC9L,CAuDA,MAAAwB,GAAe,CACbhgB,KAAM,gBACNmY,SAAS,EACTC,MAAO,cACPjY,GAzDF,SAAuB8f,GACrB,IAAI3H,EAAQ2H,EAAM3H,MACdS,EAAUkH,EAAMlH,QAChBmH,EAAwBnH,EAAQ6F,gBAChCA,OAA4C,IAA1BsB,GAA0CA,EAC5DC,EAAoBpH,EAAQ8F,SAC5BA,OAAiC,IAAtBsB,GAAsCA,EACjDC,EAAwBrH,EAAQ+F,aAChCA,OAAyC,IAA1BsB,GAA0CA,EAYzDT,EAAe,CACjB9I,UAAWyC,GAAiBhB,EAAMzB,WAClC6H,UAAWL,GAAa/F,EAAMzB,WAC9BL,OAAQ8B,EAAMC,SAAS/B,OACvBiI,WAAYnG,EAAM6E,MAAM3G,OACxBoI,gBAAiBA,EACjBG,QAAoC,UAA3BzG,EAAMS,QAAQC,UAGgB,MAArCV,EAAMwE,cAAcD,gBACtBvE,EAAMG,OAAOjC,OAAS7S,OAAO+U,OAAO,GAAIJ,EAAMG,OAAOjC,OAAQ+H,GAAY5a,OAAO+U,OAAO,GAAIiH,EAAc,CACvGhB,QAASrG,EAAMwE,cAAcD,cAC7B/D,SAAUR,EAAMS,QAAQC,SACxB6F,SAAUA,EACVC,aAAcA,OAIe,MAA7BxG,EAAMwE,cAAc5D,QACtBZ,EAAMG,OAAOS,MAAQvV,OAAO+U,OAAO,GAAIJ,EAAMG,OAAOS,MAAOqF,GAAY5a,OAAO+U,OAAO,GAAIiH,EAAc,CACrGhB,QAASrG,EAAMwE,cAAc5D,MAC7BJ,SAAU,WACV+F,UAAU,EACVC,aAAcA,OAIlBxG,EAAM9O,WAAWgN,OAAS7S,OAAO+U,OAAO,GAAIJ,EAAM9O,WAAWgN,OAAQ,CACnE,wBAAyB8B,EAAMzB,WAEnC,EAQEzI,KAAM,ICjLR,IAAIiS,GAAU,CACZA,SAAS,GAsCX,MAAAC,GAAe,CACbtgB,KAAM,iBACNmY,SAAS,EACTC,MAAO,QACPjY,GAAI,WAAc,EAClBwY,OAxCF,SAAgBN,GACd,IAAIC,EAAQD,EAAKC,MACbrQ,EAAWoQ,EAAKpQ,SAChB8Q,EAAUV,EAAKU,QACfwH,EAAkBxH,EAAQyH,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB1H,EAAQ2H,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7C/jB,EAASib,GAAUW,EAAMC,SAAS/B,QAClCmK,EAAgB,GAAGtU,OAAOiM,EAAMqI,cAAclK,UAAW6B,EAAMqI,cAAcnK,QAYjF,OAVIgK,GACFG,EAAcnI,SAAQ,SAAUoI,GAC9BA,EAAapgB,iBAAiB,SAAUyH,EAAS4Y,OAAQR,GAC/D,IAGMK,GACFhkB,EAAO8D,iBAAiB,SAAUyH,EAAS4Y,OAAQR,IAG9C,WACDG,GACFG,EAAcnI,SAAQ,SAAUoI,GAC9BA,EAAa/e,oBAAoB,SAAUoG,EAAS4Y,OAAQR,GACpE,IAGQK,GACFhkB,EAAOmF,oBAAoB,SAAUoG,EAAS4Y,OAAQR,GAE5D,CACA,EASEjS,KAAM,IC/CR,IAAI0S,GAAO,CACT7K,KAAM,QACND,MAAO,OACPD,OAAQ,MACRD,IAAK,UAEQ,SAASiL,GAAqBlK,GAC3C,OAAOA,EAAUha,QAAQ,0BAA0B,SAAUmkB,GAC3D,OAAOF,GAAKE,EAChB,GACA,CCVA,IAAIF,GAAO,CACT1K,MAAO,MACPC,IAAK,SAEQ,SAAS4K,GAA8BpK,GACpD,OAAOA,EAAUha,QAAQ,cAAc,SAAUmkB,GAC/C,OAAOF,GAAKE,EAChB,GACA,CCPe,SAASE,GAAgBtJ,GACtC,IAAI2H,EAAM5H,GAAUC,GAGpB,MAAO,CACLuJ,WAHe5B,EAAI6B,YAInBC,UAHc9B,EAAI+B,YAKtB,CCNe,SAASC,GAAoBtkB,GAQ1C,OAAOyY,GAAsB0F,GAAmBne,IAAUgZ,KAAOiL,GAAgBjkB,GAASkkB,UAC5F,CCXe,SAASK,GAAevkB,GAErC,IAAIwkB,EAAoB1jB,GAAiBd,GACrCykB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6B5W,KAAK0W,EAAWE,EAAYD,EAClE,CCLe,SAASE,GAAgBjK,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAatV,QAAQmV,GAAYG,KAAU,EAEvDA,EAAKC,cAAcrY,KAGxBuY,GAAcH,IAAS4J,GAAe5J,GACjCA,EAGFiK,GAAgBxG,GAAczD,GACvC,CCJe,SAASkK,GAAkB7kB,EAAS+E,GACjD,IAAI+f,OAES,IAAT/f,IACFA,EAAO,IAGT,IAAI4e,EAAeiB,GAAgB5kB,GAC/B+kB,EAASpB,KAAqE,OAAlDmB,EAAwB9kB,EAAQ4a,oBAAyB,EAASkK,EAAsBviB,MACpH+f,EAAM5H,GAAUiJ,GAChBhf,EAASogB,EAAS,CAACzC,GAAKlT,OAAOkT,EAAI9E,gBAAkB,GAAI+G,GAAeZ,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAcjgB,EAAKqK,OAAOzK,GAC9B,OAAOogB,EAASC,EAChBA,EAAY5V,OAAOyV,GAAkBzG,GAAczZ,IACrD,CCzBe,SAASsgB,GAAiBC,GACvC,OAAOxe,OAAO+U,OAAO,GAAIyJ,EAAM,CAC7BlM,KAAMkM,EAAKxH,EACX7E,IAAKqM,EAAKtH,EACV7E,MAAOmM,EAAKxH,EAAIwH,EAAK5H,MACrBxE,OAAQoM,EAAKtH,EAAIsH,EAAK3H,QAE1B,CCqBA,SAAS4H,GAA2BnlB,EAASolB,EAAgBrJ,GAC3D,OAAOqJ,IAAmB9L,GAAW2L,GCzBxB,SAAyBjlB,EAAS+b,GAC/C,IAAIuG,EAAM5H,GAAU1a,GAChBqlB,EAAOlH,GAAmBne,GAC1Bwd,EAAiB8E,EAAI9E,eACrBF,EAAQ+H,EAAKzE,YACbrD,EAAS8H,EAAK1E,aACdjD,EAAI,EACJE,EAAI,EAER,GAAIJ,EAAgB,CAClBF,EAAQE,EAAeF,MACvBC,EAASC,EAAeD,OACxB,IAAI+H,EAAiBvI,MAEjBuI,IAAmBA,GAA+B,UAAbvJ,KACvC2B,EAAIF,EAAeG,WACnBC,EAAIJ,EAAeK,UAEzB,CAEE,MAAO,CACLP,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EAAI4G,GAAoBtkB,GAC3B4d,EAAGA,EAEP,CDDwD2H,CAAgBvlB,EAAS+b,IAAa5b,GAAUilB,GAdxG,SAAoCplB,EAAS+b,GAC3C,IAAImJ,EAAOzM,GAAsBzY,GAAS,EAAoB,UAAb+b,GASjD,OARAmJ,EAAKrM,IAAMqM,EAAKrM,IAAM7Y,EAAQwlB,UAC9BN,EAAKlM,KAAOkM,EAAKlM,KAAOhZ,EAAQylB,WAChCP,EAAKpM,OAASoM,EAAKrM,IAAM7Y,EAAQ2gB,aACjCuE,EAAKnM,MAAQmM,EAAKlM,KAAOhZ,EAAQ4gB,YACjCsE,EAAK5H,MAAQtd,EAAQ4gB,YACrBsE,EAAK3H,OAASvd,EAAQ2gB,aACtBuE,EAAKxH,EAAIwH,EAAKlM,KACdkM,EAAKtH,EAAIsH,EAAKrM,IACPqM,CACT,CAG0HQ,CAA2BN,EAAgBrJ,GAAYkJ,GEtBlK,SAAyBjlB,GACtC,IAAI8kB,EAEAO,EAAOlH,GAAmBne,GAC1B2lB,EAAY1B,GAAgBjkB,GAC5BuC,EAA0D,OAAlDuiB,EAAwB9kB,EAAQ4a,oBAAyB,EAASkK,EAAsBviB,KAChG+a,EAAQ/X,GAAI8f,EAAKO,YAAaP,EAAKzE,YAAare,EAAOA,EAAKqjB,YAAc,EAAGrjB,EAAOA,EAAKqe,YAAc,GACvGrD,EAAShY,GAAI8f,EAAKQ,aAAcR,EAAK1E,aAAcpe,EAAOA,EAAKsjB,aAAe,EAAGtjB,EAAOA,EAAKoe,aAAe,GAC5GjD,GAAKiI,EAAUzB,WAAaI,GAAoBtkB,GAChD4d,GAAK+H,EAAUvB,UAMnB,MAJiD,QAA7CtjB,GAAiByB,GAAQ8iB,GAAM1S,YACjC+K,GAAKnY,GAAI8f,EAAKzE,YAAare,EAAOA,EAAKqe,YAAc,GAAKtD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EACHE,EAAGA,EAEP,CFCkMkI,CAAgB3H,GAAmBne,IACrO,CG1Be,SAAS+lB,GAAe3K,GACrC,IAOIsG,EAPAlI,EAAY4B,EAAK5B,UACjBxZ,EAAUob,EAAKpb,QACf4Z,EAAYwB,EAAKxB,UACjBkG,EAAgBlG,EAAYyC,GAAiBzC,GAAa,KAC1D6H,EAAY7H,EAAYwH,GAAaxH,GAAa,KAClDoM,EAAUxM,EAAUkE,EAAIlE,EAAU8D,MAAQ,EAAItd,EAAQsd,MAAQ,EAC9D2I,EAAUzM,EAAUoE,EAAIpE,EAAU+D,OAAS,EAAIvd,EAAQud,OAAS,EAGpE,OAAQuC,GACN,KAAKjH,GACH6I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAI5d,EAAQud,QAE3B,MAEF,KAAKzE,GACH4I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAIpE,EAAU+D,QAE7B,MAEF,KAAKxE,GACH2I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAIlE,EAAU8D,MAC3BM,EAAGqI,GAEL,MAEF,KAAKjN,GACH0I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAI1d,EAAQsd,MACzBM,EAAGqI,GAEL,MAEF,QACEvE,EAAU,CACRhE,EAAGlE,EAAUkE,EACbE,EAAGpE,EAAUoE,GAInB,IAAIsI,EAAWpG,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZoG,EAAkB,CACpB,IAAIlG,EAAmB,MAAbkG,EAAmB,SAAW,QAExC,OAAQzE,GACN,KAAKtI,GACHuI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAIhgB,EAAQggB,GAAO,GAC7E,MAEF,KAAK5G,GACHsI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAIhgB,EAAQggB,GAAO,GAKrF,CAEE,OAAO0B,CACT,CC3De,SAASyE,GAAe9K,EAAOS,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIsK,EAAWtK,EACXuK,EAAqBD,EAASxM,UAC9BA,OAAmC,IAAvByM,EAAgChL,EAAMzB,UAAYyM,EAC9DC,EAAoBF,EAASrK,SAC7BA,OAAiC,IAAtBuK,EAA+BjL,EAAMU,SAAWuK,EAC3DC,EAAoBH,EAASI,SAC7BA,OAAiC,IAAtBD,EAA+BlN,GAAkBkN,EAC5DE,EAAwBL,EAASM,aACjCA,OAAyC,IAA1BD,EAAmCnN,GAAWmN,EAC7DE,EAAwBP,EAASQ,eACjCA,OAA2C,IAA1BD,EAAmCpN,GAASoN,EAC7DE,EAAuBT,EAASU,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBX,EAASnG,QAC5BA,OAA+B,IAArB8G,EAA8B,EAAIA,EAC5CzH,EAAgBD,GAAsC,iBAAZY,EAAuBA,EAAUV,GAAgBU,EAAS/G,KACpG8N,EAAaJ,IAAmBrN,GAASC,GAAYD,GACrDiI,EAAanG,EAAM6E,MAAM3G,OACzBvZ,EAAUqb,EAAMC,SAASwL,EAAcE,EAAaJ,GACpDK,EJkBS,SAAyBjnB,EAASwmB,EAAUE,EAAc3K,GACvE,IAAImL,EAAmC,oBAAbV,EAlB5B,SAA4BxmB,GAC1B,IAAIqZ,EAAkBwL,GAAkBzG,GAAcpe,IAElDmnB,EADoB,CAAC,WAAY,SAAS9hB,QAAQvE,GAAiBd,GAAS6b,WAAa,GACnDf,GAAc9a,GAAWwe,GAAgBxe,GAAWA,EAE9F,OAAKG,GAAUgnB,GAKR9N,EAAgB3M,QAAO,SAAU0Y,GACtC,OAAOjlB,GAAUilB,IAAmB5jB,GAAS4jB,EAAgB+B,IAAmD,SAAhC3M,GAAY4K,EAChG,IANW,EAOX,CAK6DgC,CAAmBpnB,GAAW,GAAGoP,OAAOoX,GAC/FnN,EAAkB,GAAGjK,OAAO8X,EAAqB,CAACR,IAClDW,EAAsBhO,EAAgB,GACtCiO,EAAejO,EAAgBK,QAAO,SAAU6N,EAASnC,GAC3D,IAAIF,EAAOC,GAA2BnlB,EAASolB,EAAgBrJ,GAK/D,OAJAwL,EAAQ1O,IAAMtT,GAAI2f,EAAKrM,IAAK0O,EAAQ1O,KACpC0O,EAAQxO,MAAQvT,GAAI0f,EAAKnM,MAAOwO,EAAQxO,OACxCwO,EAAQzO,OAAStT,GAAI0f,EAAKpM,OAAQyO,EAAQzO,QAC1CyO,EAAQvO,KAAOzT,GAAI2f,EAAKlM,KAAMuO,EAAQvO,MAC/BuO,CACX,GAAKpC,GAA2BnlB,EAASqnB,EAAqBtL,IAK5D,OAJAuL,EAAahK,MAAQgK,EAAavO,MAAQuO,EAAatO,KACvDsO,EAAa/J,OAAS+J,EAAaxO,OAASwO,EAAazO,IACzDyO,EAAa5J,EAAI4J,EAAatO,KAC9BsO,EAAa1J,EAAI0J,EAAazO,IACvByO,CACT,CInC2BE,CAAgBrnB,GAAUH,GAAWA,EAAUA,EAAQynB,gBAAkBtJ,GAAmB9C,EAAMC,SAAS/B,QAASiN,EAAUE,EAAc3K,GACjK2L,EAAsBjP,GAAsB4C,EAAMC,SAAS9B,WAC3DoG,EAAgBmG,GAAe,CACjCvM,UAAWkO,EACX1nB,QAASwhB,EACTzF,SAAU,WACVnC,UAAWA,IAET+N,EAAmB1C,GAAiBve,OAAO+U,OAAO,GAAI+F,EAAY5B,IAClEgI,EAAoBhB,IAAmBrN,GAASoO,EAAmBD,EAGnEG,EAAkB,CACpBhP,IAAKoO,EAAmBpO,IAAM+O,EAAkB/O,IAAMyG,EAAczG,IACpEC,OAAQ8O,EAAkB9O,OAASmO,EAAmBnO,OAASwG,EAAcxG,OAC7EE,KAAMiO,EAAmBjO,KAAO4O,EAAkB5O,KAAOsG,EAActG,KACvED,MAAO6O,EAAkB7O,MAAQkO,EAAmBlO,MAAQuG,EAAcvG,OAExE+O,EAAazM,EAAMwE,cAAckB,OAErC,GAAI6F,IAAmBrN,IAAUuO,EAAY,CAC3C,IAAI/G,EAAS+G,EAAWlO,GACxBlT,OAAO4C,KAAKue,GAAiBtM,SAAQ,SAAUjR,GAC7C,IAAIyd,EAAW,CAAChP,GAAOD,IAAQzT,QAAQiF,IAAQ,EAAI,GAAK,EACpDyV,EAAO,CAAClH,GAAKC,IAAQzT,QAAQiF,IAAQ,EAAI,IAAM,IACnDud,EAAgBvd,IAAQyW,EAAOhB,GAAQgI,CAC7C,GACA,CAEE,OAAOF,CACT,CC5De,SAASG,GAAqB3M,EAAOS,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIsK,EAAWtK,EACXlC,EAAYwM,EAASxM,UACrB4M,EAAWJ,EAASI,SACpBE,EAAeN,EAASM,aACxBzG,EAAUmG,EAASnG,QACnBgI,EAAiB7B,EAAS6B,eAC1BC,EAAwB9B,EAAS+B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EzG,EAAYL,GAAaxH,GACzBC,EAAa4H,EAAYwG,EAAiBxO,GAAsBA,GAAoB/M,QAAO,SAAUkN,GACvG,OAAOwH,GAAaxH,KAAe6H,CACvC,IAAOvI,GACDmP,EAAoBxO,EAAWnN,QAAO,SAAUkN,GAClD,OAAOuO,EAAsB9iB,QAAQuU,IAAc,CACvD,IAEmC,IAA7ByO,EAAkB7nB,SACpB6nB,EAAoBxO,GAQtB,IAAIyO,EAAYD,EAAkB3O,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAauM,GAAe9K,EAAO,CACrCzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,IACR5D,GAAiBzC,IACbD,CACX,GAAK,IACH,OAAOjT,OAAO4C,KAAKgf,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,EACpC,GACA,CC2FA,MAAAC,GAAe,CACb3lB,KAAM,OACNmY,SAAS,EACTC,MAAO,OACPjY,GA5HF,SAAckY,GACZ,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACf/Y,EAAOqY,EAAKrY,KAEhB,IAAIsY,EAAMwE,cAAc9c,GAAM4lB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB9M,EAAQoK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBhN,EAAQiN,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BnN,EAAQoN,mBACtCjJ,EAAUnE,EAAQmE,QAClBuG,EAAW1K,EAAQ0K,SACnBE,EAAe5K,EAAQ4K,aACvBI,EAAchL,EAAQgL,YACtBqC,EAAwBrN,EAAQmM,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBrM,EAAQqM,sBAChCiB,EAAqB/N,EAAMS,QAAQlC,UACnCkG,EAAgBzD,GAAiB+M,GAEjCF,EAAqBD,IADHnJ,IAAkBsJ,GACqCnB,EAjC/E,SAAuCrO,GACrC,GAAIyC,GAAiBzC,KAAeX,GAClC,MAAO,GAGT,IAAIoQ,EAAoBvF,GAAqBlK,GAC7C,MAAO,CAACoK,GAA8BpK,GAAYyP,EAAmBrF,GAA8BqF,GACrG,CA0B6IC,CAA8BF,GAA3E,CAACtF,GAAqBsF,KAChHvP,EAAa,CAACuP,GAAoBha,OAAO8Z,GAAoBxP,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIvK,OAAOiN,GAAiBzC,KAAeX,GAAO+O,GAAqB3M,EAAO,CACnFzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACTgI,eAAgBA,EAChBE,sBAAuBA,IACpBvO,EACT,GAAK,IACC2P,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzBiQ,EAAY,IAAI3e,IAChB4e,GAAqB,EACrBC,EAAwB7P,EAAW,GAE9B8P,EAAI,EAAGA,EAAI9P,EAAWrZ,OAAQmpB,IAAK,CAC1C,IAAI/P,EAAYC,EAAW8P,GAEvBC,EAAiBvN,GAAiBzC,GAElCiQ,EAAmBzI,GAAaxH,KAAeT,GAC/C2Q,EAAa,CAACjR,GAAKC,IAAQzT,QAAQukB,IAAmB,EACtD5J,EAAM8J,EAAa,QAAU,SAC7BrF,EAAW0B,GAAe9K,EAAO,CACnCzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACb7G,QAASA,IAEP8J,EAAoBD,EAAaD,EAAmB9Q,GAAQC,GAAO6Q,EAAmB/Q,GAASD,GAE/F0Q,EAAcvJ,GAAOwB,EAAWxB,KAClC+J,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARIpB,GACFoB,EAAOzmB,KAAKihB,EAASmF,IAAmB,GAGtCZ,GACFiB,EAAOzmB,KAAKihB,EAASsF,IAAsB,EAAGtF,EAASuF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,CACb,IAAQ,CACFT,EAAwB9P,EACxB6P,GAAqB,EACrB,KACN,CAEID,EAAUze,IAAI6O,EAAWqQ,EAC7B,CAEE,GAAIR,EAqBF,IAnBA,IAEIW,EAAQ,SAAeC,GACzB,IAAIC,EAAmBzQ,EAAWjT,MAAK,SAAUgT,GAC/C,IAAIqQ,EAAST,EAAU7e,IAAIiP,GAE3B,GAAIqQ,EACF,OAAOA,EAAO1gB,MAAM,EAAG8gB,GAAIH,OAAM,SAAUC,GACzC,OAAOA,CACnB,GAEA,IAEM,GAAIG,EAEF,OADAZ,EAAwBY,EACjB,OAEf,EAEaD,EAnBYpC,EAAiB,EAAI,EAmBZoC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpChP,EAAMzB,YAAc8P,IACtBrO,EAAMwE,cAAc9c,GAAM4lB,OAAQ,EAClCtN,EAAMzB,UAAY8P,EAClBrO,EAAMkP,OAAQ,EA5GlB,CA8GA,EAQEpJ,iBAAkB,CAAC,UACnBhQ,KAAM,CACJwX,OAAO,IC7IX,SAAS6B,GAAe/F,EAAUS,EAAMuF,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB/M,EAAG,EACHE,EAAG,IAIA,CACL/E,IAAK4L,EAAS5L,IAAMqM,EAAK3H,OAASkN,EAAiB7M,EACnD7E,MAAO0L,EAAS1L,MAAQmM,EAAK5H,MAAQmN,EAAiB/M,EACtD5E,OAAQ2L,EAAS3L,OAASoM,EAAK3H,OAASkN,EAAiB7M,EACzD5E,KAAMyL,EAASzL,KAAOkM,EAAK5H,MAAQmN,EAAiB/M,EAExD,CAEA,SAASgN,GAAsBjG,GAC7B,MAAO,CAAC5L,GAAKE,GAAOD,GAAQE,IAAM2R,MAAK,SAAUC,GAC/C,OAAOnG,EAASmG,IAAS,CAC7B,GACA,CA+BA,MAAAC,GAAe,CACb9nB,KAAM,OACNmY,SAAS,EACTC,MAAO,OACPgG,iBAAkB,CAAC,mBACnBje,GAlCF,SAAckY,GACZ,IAAIC,EAAQD,EAAKC,MACbtY,EAAOqY,EAAKrY,KACZwmB,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzBkR,EAAmBpP,EAAMwE,cAAciL,gBACvCC,EAAoB5E,GAAe9K,EAAO,CAC5CuL,eAAgB,cAEdoE,EAAoB7E,GAAe9K,EAAO,CAC5CyL,aAAa,IAEXmE,EAA2BT,GAAeO,EAAmBxB,GAC7D2B,EAAsBV,GAAeQ,EAAmBxJ,EAAYiJ,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C7P,EAAMwE,cAAc9c,GAAQ,CAC1BkoB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB/P,EAAM9O,WAAWgN,OAAS7S,OAAO+U,OAAO,GAAIJ,EAAM9O,WAAWgN,OAAQ,CACnE,+BAAgC4R,EAChC,sBAAuBC,GAE3B,GCJAC,GAAe,CACbtoB,KAAM,SACNmY,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXlZ,GA5BF,SAAgByY,GACd,IAAIN,EAAQM,EAAMN,MACdS,EAAUH,EAAMG,QAChB/Y,EAAO4Y,EAAM5Y,KACbuoB,EAAkBxP,EAAQiF,OAC1BA,OAA6B,IAApBuK,EAA6B,CAAC,EAAG,GAAKA,EAC/Cna,EAAO0I,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAWsG,EAAOa,GACxD,IAAIjB,EAAgBzD,GAAiBzC,GACjC2R,EAAiB,CAACvS,GAAMH,IAAKxT,QAAQya,IAAkB,GAAK,EAAI,EAEhE1E,EAAyB,mBAAX2F,EAAwBA,EAAOra,OAAO+U,OAAO,GAAIyE,EAAO,CACxEtG,UAAWA,KACPmH,EACFyK,EAAWpQ,EAAK,GAChBqQ,EAAWrQ,EAAK,GAIpB,OAFAoQ,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACvS,GAAMD,IAAO1T,QAAQya,IAAkB,EAAI,CACjDpC,EAAG+N,EACH7N,EAAG4N,GACD,CACF9N,EAAG8N,EACH5N,EAAG6N,EAEP,CASqBC,CAAwB9R,EAAWyB,EAAM6E,MAAOa,GAC1DpH,CACX,GAAK,IACCgS,EAAwBxa,EAAKkK,EAAMzB,WACnC8D,EAAIiO,EAAsBjO,EAC1BE,EAAI+N,EAAsB/N,EAEW,MAArCvC,EAAMwE,cAAcD,gBACtBvE,EAAMwE,cAAcD,cAAclC,GAAKA,EACvCrC,EAAMwE,cAAcD,cAAchC,GAAKA,GAGzCvC,EAAMwE,cAAc9c,GAAQoO,CAC9B,GC1BAya,GAAe,CACb7oB,KAAM,gBACNmY,SAAS,EACTC,MAAO,OACPjY,GApBF,SAAuBkY,GACrB,IAAIC,EAAQD,EAAKC,MACbtY,EAAOqY,EAAKrY,KAKhBsY,EAAMwE,cAAc9c,GAAQgjB,GAAe,CACzCvM,UAAW6B,EAAM6E,MAAM1G,UACvBxZ,QAASqb,EAAM6E,MAAM3G,OACrBwC,SAAU,WACVnC,UAAWyB,EAAMzB,WAErB,EAQEzI,KAAM,ICgHR0a,GAAe,CACb9oB,KAAM,kBACNmY,SAAS,EACTC,MAAO,OACPjY,GA/HF,SAAyBkY,GACvB,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACf/Y,EAAOqY,EAAKrY,KACZ6lB,EAAoB9M,EAAQoK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBhN,EAAQiN,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDtC,EAAW1K,EAAQ0K,SACnBE,EAAe5K,EAAQ4K,aACvBI,EAAchL,EAAQgL,YACtB7G,EAAUnE,EAAQmE,QAClB6L,EAAkBhQ,EAAQiQ,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlQ,EAAQmQ,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDvH,EAAW0B,GAAe9K,EAAO,CACnCmL,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACT6G,YAAaA,IAEXhH,EAAgBzD,GAAiBhB,EAAMzB,WACvC6H,EAAYL,GAAa/F,EAAMzB,WAC/BsS,GAAmBzK,EACnByE,EAAWjH,GAAyBa,GACpCiJ,ECrCY,MDqCS7C,ECrCH,IAAM,IDsCxBtG,EAAgBvE,EAAMwE,cAAcD,cACpC2J,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzB4S,EAA4C,mBAAjBF,EAA8BA,EAAavlB,OAAO+U,OAAO,GAAIJ,EAAM6E,MAAO,CACvGtG,UAAWyB,EAAMzB,aACbqS,EACFG,EAA2D,iBAAtBD,EAAiC,CACxEjG,SAAUiG,EACVpD,QAASoD,GACPzlB,OAAO+U,OAAO,CAChByK,SAAU,EACV6C,QAAS,GACRoD,GACCE,EAAsBhR,EAAMwE,cAAckB,OAAS1F,EAAMwE,cAAckB,OAAO1F,EAAMzB,WAAa,KACjGzI,EAAO,CACTuM,EAAG,EACHE,EAAG,GAGL,GAAKgC,EAAL,CAIA,GAAIiJ,EAAe,CACjB,IAAIyD,EAEAC,EAAwB,MAAbrG,EAAmBrN,GAAMG,GACpCwT,EAAuB,MAAbtG,EAAmBpN,GAASC,GACtCiH,EAAmB,MAAbkG,EAAmB,SAAW,QACpCnF,EAASnB,EAAcsG,GACvB1gB,EAAMub,EAAS0D,EAAS8H,GACxBhnB,EAAMwb,EAAS0D,EAAS+H,GACxBC,EAAWV,GAAUvK,EAAWxB,GAAO,EAAI,EAC3C0M,EAASjL,IAActI,GAAQoQ,EAAcvJ,GAAOwB,EAAWxB,GAC/D2M,EAASlL,IAActI,IAASqI,EAAWxB,IAAQuJ,EAAcvJ,GAGjEL,EAAetE,EAAMC,SAASW,MAC9BmE,EAAY2L,GAAUpM,EAAe7B,GAAc6B,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAENqP,EAAqBvR,EAAMwE,cAAc,oBAAsBxE,EAAMwE,cAAc,oBAAoBI,QxBhFtG,CACLpH,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GwB6EF6T,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAW7N,GAAO,EAAGqK,EAAcvJ,GAAMI,EAAUJ,IACnDgN,EAAYd,EAAkB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWF,EAAkBT,EAA4BlG,SAAWwG,EAASK,EAAWF,EAAkBT,EAA4BlG,SACxM+G,EAAYf,GAAmB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWD,EAAkBV,EAA4BlG,SAAWyG,EAASI,EAAWD,EAAkBV,EAA4BlG,SACzMzF,EAAoBpF,EAAMC,SAASW,OAASuC,GAAgBnD,EAAMC,SAASW,OAC3EiR,EAAezM,EAAiC,MAAbyF,EAAmBzF,EAAkB+E,WAAa,EAAI/E,EAAkBgF,YAAc,EAAI,EAC7H0H,EAAwH,OAAjGb,EAA+C,MAAvBD,OAA8B,EAASA,EAAoBnG,IAAqBoG,EAAwB,EAEvJc,EAAYrM,EAASkM,EAAYE,EACjCE,EAAkBnO,GAAO6M,EAAS3M,GAAQ5Z,EAF9Bub,EAASiM,EAAYG,EAAsBD,GAEK1nB,EAAKub,EAAQgL,EAAS5M,GAAQ5Z,EAAK6nB,GAAa7nB,GAChHqa,EAAcsG,GAAYmH,EAC1Blc,EAAK+U,GAAYmH,EAAkBtM,CACvC,CAEE,GAAIiI,EAAc,CAChB,IAAIsE,EAEAC,EAAyB,MAAbrH,EAAmBrN,GAAMG,GAErCwU,GAAwB,MAAbtH,EAAmBpN,GAASC,GAEvC0U,GAAU7N,EAAcmJ,GAExB2E,GAAmB,MAAZ3E,EAAkB,SAAW,QAEpC4E,GAAOF,GAAUhJ,EAAS8I,GAE1BK,GAAOH,GAAUhJ,EAAS+I,IAE1BK,IAAuD,IAAxC,CAAChV,GAAKG,IAAM3T,QAAQya,GAEnCgO,GAAyH,OAAjGR,EAAgD,MAAvBjB,OAA8B,EAASA,EAAoBtD,IAAoBuE,EAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAEzIiF,GAAaH,GAAeJ,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAAU6E,GAE5IK,GAAmBlC,GAAU8B,G1BzH9B,SAAwBroB,EAAK+E,EAAOhF,GACzC,IAAI2oB,EAAIhP,GAAO1Z,EAAK+E,EAAOhF,GAC3B,OAAO2oB,EAAI3oB,EAAMA,EAAM2oB,CACzB,C0BsHoDC,CAAeJ,GAAYN,GAASO,IAAc9O,GAAO6M,EAASgC,GAAaJ,GAAMF,GAAS1B,EAASiC,GAAaJ,IAEpKhO,EAAcmJ,GAAWkF,GACzB9c,EAAK4X,GAAWkF,GAAmBR,EACvC,CAEEpS,EAAMwE,cAAc9c,GAAQoO,CAvE9B,CAwEA,EAQEgQ,iBAAkB,CAAC,WE1HN,SAASiN,GAAiBC,EAAyB9P,EAAcuD,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICnBoCnH,ECJO3a,EFuBvCsuB,EAA0BxT,GAAcyD,GACxCgQ,EAAuBzT,GAAcyD,IAf3C,SAAyBve,GACvB,IAAIklB,EAAOllB,EAAQyY,wBACf0E,EAASb,GAAM4I,EAAK5H,OAAStd,EAAQqd,aAAe,EACpDD,EAASd,GAAM4I,EAAK3H,QAAUvd,EAAQoC,cAAgB,EAC1D,OAAkB,IAAX+a,GAA2B,IAAXC,CACzB,CAU4DoR,CAAgBjQ,GACtE1c,EAAkBsc,GAAmBI,GACrC2G,EAAOzM,GAAsB4V,EAAyBE,EAAsBzM,GAC5EyB,EAAS,CACXW,WAAY,EACZE,UAAW,GAET1C,EAAU,CACZhE,EAAG,EACHE,EAAG,GAkBL,OAfI0Q,IAA4BA,IAA4BxM,MACxB,SAA9BtH,GAAY+D,IAChBgG,GAAe1iB,MACb0hB,GCnCgC5I,EDmCT4D,KClCd7D,GAAUC,IAAUG,GAAcH,GCJxC,CACLuJ,YAFyClkB,EDQb2a,GCNRuJ,WACpBE,UAAWpkB,EAAQokB,WDGZH,GAAgBtJ,IDoCnBG,GAAcyD,KAChBmD,EAAUjJ,GAAsB8F,GAAc,IACtCb,GAAKa,EAAakH,WAC1B/D,EAAQ9D,GAAKW,EAAaiH,WACjB3jB,IACT6f,EAAQhE,EAAI4G,GAAoBziB,KAI7B,CACL6b,EAAGwH,EAAKlM,KAAOuK,EAAOW,WAAaxC,EAAQhE,EAC3CE,EAAGsH,EAAKrM,IAAM0K,EAAOa,UAAY1C,EAAQ9D,EACzCN,MAAO4H,EAAK5H,MACZC,OAAQ2H,EAAK3H,OAEjB,CGvDA,SAASjI,GAAMmZ,GACb,IAAIte,EAAM,IAAItF,IACV6jB,EAAU,IAAIxoB,IACdyoB,EAAS,GAKb,SAASpG,EAAKqG,GACZF,EAAQ9b,IAAIgc,EAAS7rB,MACN,GAAGqM,OAAOwf,EAASxS,UAAY,GAAIwS,EAASzN,kBAAoB,IACtE5F,SAAQ,SAAUsT,GACzB,IAAKH,EAAQtnB,IAAIynB,GAAM,CACrB,IAAIC,EAAc3e,EAAIxF,IAAIkkB,GAEtBC,GACFvG,EAAKuG,EAEf,CACA,IACIH,EAAOnrB,KAAKorB,EAChB,CAQE,OAzBAH,EAAUlT,SAAQ,SAAUqT,GAC1Bze,EAAIpF,IAAI6jB,EAAS7rB,KAAM6rB,EAC3B,IAiBEH,EAAUlT,SAAQ,SAAUqT,GACrBF,EAAQtnB,IAAIwnB,EAAS7rB,OAExBwlB,EAAKqG,EAEX,IACSD,CACT,CChBA,IAAII,GAAkB,CACpBnV,UAAW,SACX6U,UAAW,GACX1S,SAAU,YAGZ,SAASiT,KACP,IAAK,IAAItB,EAAOuB,UAAUzuB,OAAQmD,EAAO,IAAI0H,MAAMqiB,GAAOwB,EAAO,EAAGA,EAAOxB,EAAMwB,IAC/EvrB,EAAKurB,GAAQD,UAAUC,GAGzB,OAAQvrB,EAAKgnB,MAAK,SAAU3qB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQyY,sBACvC,GACA,CAEO,SAAS0W,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCT,GAAkBS,EAC3E,OAAO,SAAsBhW,EAAWD,EAAQuC,QAC9B,IAAZA,IACFA,EAAU2T,GAGZ,IC/C6BvsB,EAC3BwsB,ED8CErU,EAAQ,CACVzB,UAAW,SACX+V,iBAAkB,GAClB7T,QAASpV,OAAO+U,OAAO,GAAIsT,GAAiBU,GAC5C5P,cAAe,GACfvE,SAAU,CACR9B,UAAWA,EACXD,OAAQA,GAEVhN,WAAY,GACZiP,OAAQ,IAENoU,EAAmB,GACnBC,GAAc,EACd7kB,EAAW,CACbqQ,MAAOA,EACPyU,WAAY,SAAoBC,GAC9B,IAAIjU,EAAsC,mBAArBiU,EAAkCA,EAAiB1U,EAAMS,SAAWiU,EACzFC,IACA3U,EAAMS,QAAUpV,OAAO+U,OAAO,GAAIgU,EAAgBpU,EAAMS,QAASA,GACjET,EAAMqI,cAAgB,CACpBlK,UAAWrZ,GAAUqZ,GAAaqL,GAAkBrL,GAAaA,EAAUiO,eAAiB5C,GAAkBrL,EAAUiO,gBAAkB,GAC1IlO,OAAQsL,GAAkBtL,IAI5B,IEzE4BkV,EAC9BwB,EFwEMN,EDvCG,SAAwBlB,GAErC,IAAIkB,EAAmBra,GAAMmZ,GAE7B,OAAOlU,GAAeb,QAAO,SAAUC,EAAKwB,GAC1C,OAAOxB,EAAIvK,OAAOugB,EAAiBjjB,QAAO,SAAUkiB,GAClD,OAAOA,EAASzT,QAAUA,CAChC,IACA,GAAK,GACL,CC8B+B+U,EEzEKzB,EFyEsB,GAAGrf,OAAOmgB,EAAkBlU,EAAMS,QAAQ2S,WExE9FwB,EAASxB,EAAU/U,QAAO,SAAUuW,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQptB,MAK9B,OAJAktB,EAAOE,EAAQptB,MAAQqtB,EAAW1pB,OAAO+U,OAAO,GAAI2U,EAAUD,EAAS,CACrErU,QAASpV,OAAO+U,OAAO,GAAI2U,EAAStU,QAASqU,EAAQrU,SACrD3K,KAAMzK,OAAO+U,OAAO,GAAI2U,EAASjf,KAAMgf,EAAQhf,QAC5Cgf,EACEF,CACX,GAAK,IAEIvpB,OAAO4C,KAAK2mB,GAAQ9f,KAAI,SAAU7F,GACvC,OAAO2lB,EAAO3lB,EAClB,MFsGQ,OAvCA+Q,EAAMsU,iBAAmBA,EAAiBjjB,QAAO,SAAU2jB,GACzD,OAAOA,EAAEnV,OACnB,IAoJMG,EAAMsU,iBAAiBpU,SAAQ,SAAU0G,GACvC,IAAIlf,EAAOkf,EAAMlf,KACbutB,EAAgBrO,EAAMnG,QACtBA,OAA4B,IAAlBwU,EAA2B,GAAKA,EAC1C5U,EAASuG,EAAMvG,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAI6U,EAAY7U,EAAO,CACrBL,MAAOA,EACPtY,KAAMA,EACNiI,SAAUA,EACV8Q,QAASA,IAKX8T,EAAiBpsB,KAAK+sB,GAFT,WAAkB,EAGzC,CACA,IAjIevlB,EAAS4Y,QACxB,EAMM4M,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBpV,EAAMC,SACxB9B,EAAYiX,EAAgBjX,UAC5BD,EAASkX,EAAgBlX,OAG7B,GAAKyV,GAAiBxV,EAAWD,GAAjC,CASA8B,EAAM6E,MAAQ,CACZ1G,UAAW4U,GAAiB5U,EAAWgF,GAAgBjF,GAAoC,UAA3B8B,EAAMS,QAAQC,UAC9ExC,OAAQuE,GAAcvE,IAOxB8B,EAAMkP,OAAQ,EACdlP,EAAMzB,UAAYyB,EAAMS,QAAQlC,UAKhCyB,EAAMsU,iBAAiBpU,SAAQ,SAAUqT,GACvC,OAAOvT,EAAMwE,cAAc+O,EAAS7rB,MAAQ2D,OAAO+U,OAAO,GAAImT,EAASzd,KACjF,IAGQ,IAAK,IAAI/L,EAAQ,EAAGA,EAAQiW,EAAMsU,iBAAiBnvB,OAAQ4E,IAUzD,IAAoB,IAAhBiW,EAAMkP,MAAV,CAMA,IAAImG,EAAwBrV,EAAMsU,iBAAiBvqB,GAC/ClC,EAAKwtB,EAAsBxtB,GAC3BytB,EAAyBD,EAAsB5U,QAC/CsK,OAAsC,IAA3BuK,EAAoC,GAAKA,EACpD5tB,EAAO2tB,EAAsB3tB,KAEf,mBAAPG,IACTmY,EAAQnY,EAAG,CACTmY,MAAOA,EACPS,QAASsK,EACTrjB,KAAMA,EACNiI,SAAUA,KACNqQ,EAdlB,MAHYA,EAAMkP,OAAQ,EACdnlB,GAAS,CAnCrB,CAbA,CAmEA,EAGMwe,QClM2B1gB,EDkMV,WACf,OAAO,IAAI0tB,SAAQ,SAAUC,GAC3B7lB,EAASwlB,cACTK,EAAQxV,EAClB,GACA,ECrMS,WAUL,OATKqU,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAUte,EACVyf,EAAQ3tB,IAClB,GACA,KAGWwsB,CACX,GD2LMqB,QAAS,WACPf,IACAH,GAAc,CACtB,GAGI,IAAKb,GAAiBxV,EAAWD,GAK/B,OAAOvO,EAmCT,SAASglB,IACPJ,EAAiBrU,SAAQ,SAAUrY,GACjC,OAAOA,GACf,IACM0sB,EAAmB,EACzB,CAEI,OAvCA5kB,EAAS8kB,WAAWhU,GAASgV,MAAK,SAAUzV,IACrCwU,GAAe/T,EAAQkV,eAC1BlV,EAAQkV,cAAc3V,EAE9B,IAmCWrQ,CACX,CACA,CACO,IAAIimB,GAA4B9B,KG1PnC8B,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,MCMlEF,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,GAAapQ,GAAQqQ,GAAMtG,GAAiB7O,GAAOjE,M,+lBCkBnHhV,GAAO,WAOPquB,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1Bta,GAAkB,OAOlB7F,GAAuB,4DACvBogB,GAA8B,GAAEpgB,UAChCqgB,GAAgB,iBAKhBC,GAAgBlvB,IAAU,UAAY,YACtCmvB,GAAmBnvB,IAAU,YAAc,UAC3CovB,GAAmBpvB,IAAU,aAAe,eAC5CqvB,GAAsBrvB,IAAU,eAAiB,aACjDsvB,GAAkBtvB,IAAU,aAAe,cAC3CuvB,GAAiBvvB,IAAU,cAAgB,aAI3CsK,GAAU,CACdklB,WAAW,EACXzL,SAAU,kBACV0L,QAAS,UACTnR,OAAQ,CAAC,EAAG,GACZoR,aAAc,KACd3Y,UAAW,UAGPxM,GAAc,CAClBilB,UAAW,mBACXzL,SAAU,mBACV0L,QAAS,SACTnR,OAAQ,0BACRoR,aAAc,yBACd3Y,UAAW,2BAOb,MAAM4Y,WAAiBlkB,EACrBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAK0qB,QAAU,KACf1qB,KAAK2qB,QAAU3qB,KAAKyG,SAASjN,WAE7BwG,KAAK4qB,MAAQpjB,EAAeY,KAAKpI,KAAKyG,SAAUsjB,IAAe,IAC7DviB,EAAeS,KAAKjI,KAAKyG,SAAUsjB,IAAe,IAClDviB,EAAeG,QAAQoiB,GAAe/pB,KAAK2qB,SAC7C3qB,KAAK6qB,UAAY7qB,KAAK8qB,eACxB,CAGW1lB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,OAAOA,EACT,CAGAuO,SACE,OAAO5J,KAAKoQ,WAAapQ,KAAKqQ,OAASrQ,KAAKsQ,MAC9C,CAEAA,OACE,GAAI7W,EAAWuG,KAAKyG,WAAazG,KAAKoQ,WACpC,OAGF,MAAMvQ,EAAgB,CACpBA,cAAeG,KAAKyG,UAKtB,IAFkBlG,EAAauB,QAAQ9B,KAAKyG,SA3F5B,mBA2FkD5G,GAEpDqC,iBAAd,CAUA,GANAlC,KAAK+qB,gBAMD,iBAAkBjyB,SAASoB,kBAAoB8F,KAAK2qB,QAAQrxB,QAtFxC,eAuFtB,IAAK,MAAMjB,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAac,GAAGhJ,EAAS,YAAakC,GAI1CyF,KAAKyG,SAASukB,QACdhrB,KAAKyG,SAASjC,aAAa,iBAAiB,GAE5CxE,KAAK4qB,MAAMhxB,UAAUqR,IAAIsE,IACzBvP,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAC5BhP,EAAauB,QAAQ9B,KAAKyG,SAjHT,oBAiHgC5G,EAnBjD,CAoBF,CAEAwQ,OACE,GAAI5W,EAAWuG,KAAKyG,YAAczG,KAAKoQ,WACrC,OAGF,MAAMvQ,EAAgB,CACpBA,cAAeG,KAAKyG,UAGtBzG,KAAKirB,cAAcprB,EACrB,CAEA+G,UACM5G,KAAK0qB,SACP1qB,KAAK0qB,QAAQtB,UAGf5iB,MAAMI,SACR,CAEAqV,SACEjc,KAAK6qB,UAAY7qB,KAAK8qB,gBAClB9qB,KAAK0qB,SACP1qB,KAAK0qB,QAAQzO,QAEjB,CAGAgP,cAAcprB,GAEZ,IADkBU,EAAauB,QAAQ9B,KAAKyG,SApJ5B,mBAoJkD5G,GACpDqC,iBAAd,CAMA,GAAI,iBAAkBpJ,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAaC,IAAInI,EAAS,YAAakC,GAIvCyF,KAAK0qB,SACP1qB,KAAK0qB,QAAQtB,UAGfppB,KAAK4qB,MAAMhxB,UAAUgK,OAAO2L,IAC5BvP,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAC/BvP,KAAKyG,SAASjC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzE,KAAK4qB,MAAO,UAC5CrqB,EAAauB,QAAQ9B,KAAKyG,SAxKR,qBAwKgC5G,EAlBlD,CAmBF,CAEA0F,WAAWC,GAGT,GAAgC,iBAFhCA,EAASgB,MAAMjB,WAAWC,IAERqM,YAA2BrZ,EAAUgN,EAAOqM,YACV,mBAA3CrM,EAAOqM,UAAUf,sBAGxB,MAAM,IAAIzK,UAAW,GAAEhL,GAAKiL,+GAG9B,OAAOd,CACT,CAEAulB,gBACE,QAAsB,IAAXG,GACT,MAAM,IAAI7kB,UAAU,gEAGtB,IAAI8kB,EAAmBnrB,KAAKyG,SAEG,WAA3BzG,KAAK0G,QAAQmL,UACfsZ,EAAmBnrB,KAAK2qB,QACfnyB,EAAUwH,KAAK0G,QAAQmL,WAChCsZ,EAAmBvyB,EAAWoH,KAAK0G,QAAQmL,WACA,iBAA3B7R,KAAK0G,QAAQmL,YAC7BsZ,EAAmBnrB,KAAK0G,QAAQmL,WAGlC,MAAM2Y,EAAexqB,KAAKorB,mBAC1BprB,KAAK0qB,QAAUQ,GAAoBC,EAAkBnrB,KAAK4qB,MAAOJ,EACnE,CAEApa,WACE,OAAOpQ,KAAK4qB,MAAMhxB,UAAUC,SAAS0V,GACvC,CAEA8b,gBACE,MAAMC,EAAiBtrB,KAAK2qB,QAE5B,GAAIW,EAAe1xB,UAAUC,SAzMN,WA0MrB,OAAOuwB,GAGT,GAAIkB,EAAe1xB,UAAUC,SA5MJ,aA6MvB,OAAOwwB,GAGT,GAAIiB,EAAe1xB,UAAUC,SA/MA,iBAgN3B,MAhMsB,MAmMxB,GAAIyxB,EAAe1xB,UAAUC,SAlNE,mBAmN7B,MAnMyB,SAuM3B,MAAM0xB,EAAkF,QAA1EpyB,iBAAiB6G,KAAK4qB,OAAOxxB,iBAAiB,iBAAiBmO,OAE7E,OAAI+jB,EAAe1xB,UAAUC,SA7NP,UA8Nb0xB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,EACvC,CAEAY,gBACE,OAAkD,OAA3C9qB,KAAKyG,SAASnN,QA5ND,UA6NtB,CAEAkyB,aACE,MAAMpS,OAAEA,GAAWpZ,KAAK0G,QAExB,MAAsB,iBAAX0S,EACFA,EAAOxc,MAAM,KAAK4L,KAAI5F,GAASnG,OAAO8R,SAAS3L,EAAO,MAGzC,mBAAXwW,EACFqS,GAAcrS,EAAOqS,EAAYzrB,KAAKyG,UAGxC2S,CACT,CAEAgS,mBACE,MAAMM,EAAwB,CAC5BzZ,UAAWjS,KAAKqrB,gBAChBvE,UAAW,CAAC,CACV1rB,KAAM,kBACN+Y,QAAS,CACP0K,SAAU7e,KAAK0G,QAAQmY,WAG3B,CACEzjB,KAAM,SACN+Y,QAAS,CACPiF,OAAQpZ,KAAKwrB,iBAcnB,OARIxrB,KAAK6qB,WAAsC,WAAzB7qB,KAAK0G,QAAQ6jB,WACjCjmB,EAAYC,iBAAiBvE,KAAK4qB,MAAO,SAAU,UACnDc,EAAsB5E,UAAY,CAAC,CACjC1rB,KAAM,cACNmY,SAAS,KAIN,IACFmY,KACA5vB,EAAQkE,KAAK0G,QAAQ8jB,aAAc,CAACkB,IAE3C,CAEAC,iBAAgBhpB,IAAEA,EAAG3F,OAAEA,IACrB,MAAMsQ,EAAQ9F,EAAevI,KA5QF,8DA4Q+Be,KAAK4qB,OAAO7lB,QAAO1M,GAAWW,EAAUX,KAE7FiV,EAAMzU,QAMXsE,EAAqBmQ,EAAOtQ,EAAQ2F,IAAQgnB,IAAiBrc,EAAMlM,SAASpE,IAASguB,OACvF,CAGA9jB,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOihB,GAASthB,oBAAoBnJ,KAAMwF,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,CAEA0B,kBAAkBhI,GAChB,GA/TuB,IA+TnBA,EAAM2K,QAAiD,UAAf3K,EAAMuB,MAlUtC,QAkU0DvB,EAAMyD,IAC1E,OAGF,MAAMipB,EAAcpkB,EAAevI,KAAK6qB,IAExC,IAAK,MAAMlgB,KAAUgiB,EAAa,CAChC,MAAMC,EAAUpB,GAAStjB,YAAYyC,GACrC,IAAKiiB,IAAyC,IAA9BA,EAAQnlB,QAAQ4jB,UAC9B,SAGF,MAAMwB,EAAe5sB,EAAM4sB,eACrBC,EAAeD,EAAa1qB,SAASyqB,EAAQjB,OACnD,GACEkB,EAAa1qB,SAASyqB,EAAQplB,WACC,WAA9BolB,EAAQnlB,QAAQ4jB,YAA2ByB,GACb,YAA9BF,EAAQnlB,QAAQ4jB,WAA2ByB,EAE5C,SAIF,GAAIF,EAAQjB,MAAM/wB,SAASqF,EAAMlC,UAA4B,UAAfkC,EAAMuB,MAzV1C,QAyV8DvB,EAAMyD,KAAoB,qCAAqCyD,KAAKlH,EAAMlC,OAAOkM,UACvJ,SAGF,MAAMrJ,EAAgB,CAAEA,cAAegsB,EAAQplB,UAE5B,UAAfvH,EAAMuB,OACRZ,EAAcoJ,WAAa/J,GAG7B2sB,EAAQZ,cAAcprB,EACxB,CACF,CAEAqH,6BAA6BhI,GAI3B,MAAM8sB,EAAU,kBAAkB5lB,KAAKlH,EAAMlC,OAAOkM,SAC9C+iB,EA7WS,WA6WO/sB,EAAMyD,IACtBupB,EAAkB,CAACxC,GAAcC,IAAgBvoB,SAASlC,EAAMyD,KAEtE,IAAKupB,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGF/sB,EAAMsD,iBAGN,MAAM2pB,EAAkBnsB,KAAK8H,QAAQ4B,IACnC1J,KACCwH,EAAeS,KAAKjI,KAAM0J,IAAsB,IAC/ClC,EAAeY,KAAKpI,KAAM0J,IAAsB,IAChDlC,EAAeG,QAAQ+B,GAAsBxK,EAAMY,eAAetG,YAEhE6J,EAAWonB,GAASthB,oBAAoBgjB,GAE9C,GAAID,EAIF,OAHAhtB,EAAMktB,kBACN/oB,EAASiN,YACTjN,EAASsoB,gBAAgBzsB,GAIvBmE,EAAS+M,aACXlR,EAAMktB,kBACN/oB,EAASgN,OACT8b,EAAgBnB,QAEpB,EAOFzqB,EAAac,GAAGvI,SAAU+wB,GAAwBngB,GAAsB+gB,GAAS4B,uBACjF9rB,EAAac,GAAGvI,SAAU+wB,GAAwBE,GAAeU,GAAS4B,uBAC1E9rB,EAAac,GAAGvI,SAAU8wB,GAAsBa,GAAS6B,YACzD/rB,EAAac,GAAGvI,SA7Yc,6BA6YkB2xB,GAAS6B,YACzD/rB,EAAac,GAAGvI,SAAU8wB,GAAsBlgB,IAAsB,SAAUxK,GAC9EA,EAAMsD,iBACNioB,GAASthB,oBAAoBnJ,MAAM4J,QACrC,IAMA5O,EAAmByvB,ICrbnB,MAAM8B,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ9mB,cACE7F,KAAKyG,SAAW3N,SAAS8B,IAC3B,CAGAgyB,WAEE,MAAMC,EAAgB/zB,SAASoB,gBAAgB+e,YAC/C,OAAOtb,KAAKoN,IAAIjT,OAAOg1B,WAAaD,EACtC,CAEAxc,OACE,MAAMsF,EAAQ3V,KAAK4sB,WACnB5sB,KAAK+sB,mBAEL/sB,KAAKgtB,sBAAsBhtB,KAAKyG,SAAUgmB,IAAkBQ,GAAmBA,EAAkBtX,IAEjG3V,KAAKgtB,sBAAsBT,GAAwBE,IAAkBQ,GAAmBA,EAAkBtX,IAC1G3V,KAAKgtB,sBAAsBR,GAAyBE,IAAiBO,GAAmBA,EAAkBtX,GAC5G,CAEAiN,QACE5iB,KAAKktB,wBAAwBltB,KAAKyG,SAAU,YAC5CzG,KAAKktB,wBAAwBltB,KAAKyG,SAAUgmB,IAC5CzsB,KAAKktB,wBAAwBX,GAAwBE,IACrDzsB,KAAKktB,wBAAwBV,GAAyBE,GACxD,CAEAS,gBACE,OAAOntB,KAAK4sB,WAAa,CAC3B,CAGAG,mBACE/sB,KAAKotB,sBAAsBptB,KAAKyG,SAAU,YAC1CzG,KAAKyG,SAASmK,MAAMkM,SAAW,QACjC,CAEAkQ,sBAAsBn1B,EAAUw1B,EAAenyB,GAC7C,MAAMoyB,EAAiBttB,KAAK4sB,WAW5B5sB,KAAKutB,2BAA2B11B,GAVHQ,IAC3B,GAAIA,IAAY2H,KAAKyG,UAAY3O,OAAOg1B,WAAaz0B,EAAQ4gB,YAAcqU,EACzE,OAGFttB,KAAKotB,sBAAsB/0B,EAASg1B,GACpC,MAAMJ,EAAkBn1B,OAAOqB,iBAAiBd,GAASe,iBAAiBi0B,GAC1Eh1B,EAAQuY,MAAM4c,YAAYH,EAAgB,GAAEnyB,EAASuB,OAAOC,WAAWuwB,QAAsB,GAIjG,CAEAG,sBAAsB/0B,EAASg1B,GAC7B,MAAMI,EAAcp1B,EAAQuY,MAAMxX,iBAAiBi0B,GAC/CI,GACFnpB,EAAYC,iBAAiBlM,EAASg1B,EAAeI,EAEzD,CAEAP,wBAAwBr1B,EAAUw1B,GAahCrtB,KAAKutB,2BAA2B11B,GAZHQ,IAC3B,MAAMuK,EAAQ0B,EAAYY,iBAAiB7M,EAASg1B,GAEtC,OAAVzqB,GAKJ0B,EAAYG,oBAAoBpM,EAASg1B,GACzCh1B,EAAQuY,MAAM4c,YAAYH,EAAezqB,IALvCvK,EAAQuY,MAAM8c,eAAeL,EAKgB,GAInD,CAEAE,2BAA2B11B,EAAU81B,GACnC,GAAIn1B,EAAUX,GACZ81B,EAAS91B,QAIX,IAAK,MAAM+1B,KAAOpmB,EAAevI,KAAKpH,EAAUmI,KAAKyG,UACnDknB,EAASC,EAEb,EC/FF,MAEMre,GAAkB,OAClBse,GAAmB,wBAEnBzoB,GAAU,CACd0oB,UAAW,iBACXC,cAAe,KACf9mB,YAAY,EACZjO,WAAW,EACXg1B,YAAa,QAGT3oB,GAAc,CAClByoB,UAAW,SACXC,cAAe,kBACf9mB,WAAY,UACZjO,UAAW,UACXg1B,YAAa,oBAOf,MAAMC,WAAiB9oB,EACrBU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAKkuB,aAAc,EACnBluB,KAAKyG,SAAW,IAClB,CAGWrB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA3CS,UA4CX,CAGAiV,KAAKpV,GACH,IAAK8E,KAAK0G,QAAQ1N,UAEhB,YADA8C,EAAQZ,GAIV8E,KAAKmuB,UAEL,MAAM91B,EAAU2H,KAAKouB,cACjBpuB,KAAK0G,QAAQO,YACfzM,EAAOnC,GAGTA,EAAQuB,UAAUqR,IAAIsE,IAEtBvP,KAAKquB,mBAAkB,KACrBvyB,EAAQZ,EAAS,GAErB,CAEAmV,KAAKnV,GACE8E,KAAK0G,QAAQ1N,WAKlBgH,KAAKouB,cAAcx0B,UAAUgK,OAAO2L,IAEpCvP,KAAKquB,mBAAkB,KACrBruB,KAAK4G,UACL9K,EAAQZ,EAAS,KARjBY,EAAQZ,EAUZ,CAEA0L,UACO5G,KAAKkuB,cAIV3tB,EAAaC,IAAIR,KAAKyG,SAAUonB,IAEhC7tB,KAAKyG,SAAS7C,SACd5D,KAAKkuB,aAAc,EACrB,CAGAE,cACE,IAAKpuB,KAAKyG,SAAU,CAClB,MAAM6nB,EAAWx1B,SAASy1B,cAAc,OACxCD,EAASR,UAAY9tB,KAAK0G,QAAQonB,UAC9B9tB,KAAK0G,QAAQO,YACfqnB,EAAS10B,UAAUqR,IAjGH,QAoGlBjL,KAAKyG,SAAW6nB,CAClB,CAEA,OAAOtuB,KAAKyG,QACd,CAEAf,kBAAkBF,GAGhB,OADAA,EAAOwoB,YAAcp1B,EAAW4M,EAAOwoB,aAChCxoB,CACT,CAEA2oB,UACE,GAAInuB,KAAKkuB,YACP,OAGF,MAAM71B,EAAU2H,KAAKouB,cACrBpuB,KAAK0G,QAAQsnB,YAAYQ,OAAOn2B,GAEhCkI,EAAac,GAAGhJ,EAASw1B,IAAiB,KACxC/xB,EAAQkE,KAAK0G,QAAQqnB,cAAc,IAGrC/tB,KAAKkuB,aAAc,CACrB,CAEAG,kBAAkBnzB,GAChBgB,EAAuBhB,EAAU8E,KAAKouB,cAAepuB,KAAK0G,QAAQO,WACpE,EClIF,MAEMJ,GAAa,gBAMb4nB,GAAmB,WAEnBrpB,GAAU,CACdspB,WAAW,EACXC,YAAa,MAGTtpB,GAAc,CAClBqpB,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkBzpB,EACtBU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,GAC/BxF,KAAK6uB,WAAY,EACjB7uB,KAAK8uB,qBAAuB,IAC9B,CAGW1pB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA1CS,WA2CX,CAGA0zB,WACM/uB,KAAK6uB,YAIL7uB,KAAK0G,QAAQgoB,WACf1uB,KAAK0G,QAAQioB,YAAY3D,QAG3BzqB,EAAaC,IAAI1H,SAAU+N,IAC3BtG,EAAac,GAAGvI,SArDG,wBAqDsBoG,GAASc,KAAKgvB,eAAe9vB,KACtEqB,EAAac,GAAGvI,SArDO,4BAqDsBoG,GAASc,KAAKivB,eAAe/vB,KAE1Ec,KAAK6uB,WAAY,EACnB,CAEAK,aACOlvB,KAAK6uB,YAIV7uB,KAAK6uB,WAAY,EACjBtuB,EAAaC,IAAI1H,SAAU+N,IAC7B,CAGAmoB,eAAe9vB,GACb,MAAMyvB,YAAEA,GAAgB3uB,KAAK0G,QAE7B,GAAIxH,EAAMlC,SAAWlE,UAAYoG,EAAMlC,SAAW2xB,GAAeA,EAAY90B,SAASqF,EAAMlC,QAC1F,OAGF,MAAM2W,EAAWnM,EAAec,kBAAkBqmB,GAE1B,IAApBhb,EAAS9a,OACX81B,EAAY3D,QACHhrB,KAAK8uB,uBAAyBL,GACvC9a,EAASA,EAAS9a,OAAS,GAAGmyB,QAE9BrX,EAAS,GAAGqX,OAEhB,CAEAiE,eAAe/vB,GApFD,QAqFRA,EAAMyD,MAIV3C,KAAK8uB,qBAAuB5vB,EAAMiwB,SAAWV,GAxFzB,UAyFtB,EC3FF,MAQMW,GAAgB,kBAChBC,GAAc,gBAQdC,GAAkB,aAElB/f,GAAkB,OAClBggB,GAAoB,eAOpBnqB,GAAU,CACdkpB,UAAU,EACVtD,OAAO,EACPhf,UAAU,GAGN3G,GAAc,CAClBipB,SAAU,mBACVtD,MAAO,UACPhf,SAAU,WAOZ,MAAMwjB,WAAcjpB,EAClBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKyvB,QAAUjoB,EAAeG,QAxBV,gBAwBmC3H,KAAKyG,UAC5DzG,KAAK0vB,UAAY1vB,KAAK2vB,sBACtB3vB,KAAK4vB,WAAa5vB,KAAK6vB,uBACvB7vB,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EACxB5P,KAAK8vB,WAAa,IAAInD,GAEtB3sB,KAAK4M,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAnES,OAoEX,CAGAuO,OAAO/J,GACL,OAAOG,KAAKoQ,SAAWpQ,KAAKqQ,OAASrQ,KAAKsQ,KAAKzQ,EACjD,CAEAyQ,KAAKzQ,GACCG,KAAKoQ,UAAYpQ,KAAK4P,kBAIRrP,EAAauB,QAAQ9B,KAAKyG,SAAU4oB,GAAY,CAChExvB,kBAGYqC,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EAExB5P,KAAK8vB,WAAWzf,OAEhBvX,SAAS8B,KAAKhB,UAAUqR,IAAIqkB,IAE5BtvB,KAAK+vB,gBAEL/vB,KAAK0vB,UAAUpf,MAAK,IAAMtQ,KAAKgwB,aAAanwB,KAC9C,CAEAwQ,OACOrQ,KAAKoQ,WAAYpQ,KAAK4P,mBAITrP,EAAauB,QAAQ9B,KAAKyG,SAnG5B,iBAqGFvE,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAK4P,kBAAmB,EACxB5P,KAAK4vB,WAAWV,aAEhBlvB,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAE/BvP,KAAKgH,gBAAe,IAAMhH,KAAKiwB,cAAcjwB,KAAKyG,SAAUzG,KAAKiP,gBACnE,CAEArI,UACE,IAAK,MAAMspB,IAAe,CAACp4B,OAAQkI,KAAKyvB,SACtClvB,EAAaC,IAAI0vB,EAxHJ,aA2HflwB,KAAK0vB,UAAU9oB,UACf5G,KAAK4vB,WAAWV,aAChB1oB,MAAMI,SACR,CAEAupB,eACEnwB,KAAK+vB,eACP,CAGAJ,sBACE,OAAO,IAAI1B,GAAS,CAClBj1B,UAAW8H,QAAQd,KAAK0G,QAAQ4nB,UAChCrnB,WAAYjH,KAAKiP,eAErB,CAEA4gB,uBACE,OAAO,IAAIjB,GAAU,CACnBD,YAAa3uB,KAAKyG,UAEtB,CAEAupB,aAAanwB,GAEN/G,SAAS8B,KAAKf,SAASmG,KAAKyG,WAC/B3N,SAAS8B,KAAK4zB,OAAOxuB,KAAKyG,UAG5BzG,KAAKyG,SAASmK,MAAM2Z,QAAU,QAC9BvqB,KAAKyG,SAAS/B,gBAAgB,eAC9B1E,KAAKyG,SAASjC,aAAa,cAAc,GACzCxE,KAAKyG,SAASjC,aAAa,OAAQ,UACnCxE,KAAKyG,SAASgW,UAAY,EAE1B,MAAM2T,EAAY5oB,EAAeG,QAxIT,cAwIsC3H,KAAKyvB,SAC/DW,IACFA,EAAU3T,UAAY,GAGxBjiB,EAAOwF,KAAKyG,UAEZzG,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAa5BvP,KAAKgH,gBAXsB,KACrBhH,KAAK0G,QAAQskB,OACfhrB,KAAK4vB,WAAWb,WAGlB/uB,KAAK4P,kBAAmB,EACxBrP,EAAauB,QAAQ9B,KAAKyG,SArKX,iBAqKkC,CAC/C5G,iBACA,GAGoCG,KAAKyvB,QAASzvB,KAAKiP,cAC7D,CAEArC,qBACErM,EAAac,GAAGrB,KAAKyG,SA1KM,4BA0K2BvH,IACpD,GArLa,WAqLTA,EAAMyD,IAIV,OAAI3C,KAAK0G,QAAQsF,UACf9M,EAAMsD,sBACNxC,KAAKqQ,aAIPrQ,KAAKqwB,4BAA4B,IAGnC9vB,EAAac,GAAGvJ,OA3LE,mBA2LoB,KAChCkI,KAAKoQ,WAAapQ,KAAK4P,kBACzB5P,KAAK+vB,eACP,IAGFxvB,EAAac,GAAGrB,KAAKyG,SA/LQ,8BA+L2BvH,IAEtDqB,EAAae,IAAItB,KAAKyG,SAlMC,0BAkM8B6pB,IAC/CtwB,KAAKyG,WAAavH,EAAMlC,QAAUgD,KAAKyG,WAAa6pB,EAAOtzB,SAIjC,WAA1BgD,KAAK0G,QAAQ4nB,SAKbtuB,KAAK0G,QAAQ4nB,UACftuB,KAAKqQ,OALLrQ,KAAKqwB,6BAMP,GACA,GAEN,CAEAJ,aACEjwB,KAAKyG,SAASmK,MAAM2Z,QAAU,OAC9BvqB,KAAKyG,SAASjC,aAAa,eAAe,GAC1CxE,KAAKyG,SAAS/B,gBAAgB,cAC9B1E,KAAKyG,SAAS/B,gBAAgB,QAC9B1E,KAAK4P,kBAAmB,EAExB5P,KAAK0vB,UAAUrf,MAAK,KAClBvX,SAAS8B,KAAKhB,UAAUgK,OAAO0rB,IAC/BtvB,KAAKuwB,oBACLvwB,KAAK8vB,WAAWlN,QAChBriB,EAAauB,QAAQ9B,KAAKyG,SAAU2oB,GAAa,GAErD,CAEAngB,cACE,OAAOjP,KAAKyG,SAAS7M,UAAUC,SA7NX,OA8NtB,CAEAw2B,6BAEE,GADkB9vB,EAAauB,QAAQ9B,KAAKyG,SA5OlB,0BA6OZvE,iBACZ,OAGF,MAAMsuB,EAAqBxwB,KAAKyG,SAASyX,aAAeplB,SAASoB,gBAAgB8e,aAC3EyX,EAAmBzwB,KAAKyG,SAASmK,MAAMoM,UAEpB,WAArByT,GAAiCzwB,KAAKyG,SAAS7M,UAAUC,SAAS01B,MAIjEiB,IACHxwB,KAAKyG,SAASmK,MAAMoM,UAAY,UAGlChd,KAAKyG,SAAS7M,UAAUqR,IAAIskB,IAC5BvvB,KAAKgH,gBAAe,KAClBhH,KAAKyG,SAAS7M,UAAUgK,OAAO2rB,IAC/BvvB,KAAKgH,gBAAe,KAClBhH,KAAKyG,SAASmK,MAAMoM,UAAYyT,CAAgB,GAC/CzwB,KAAKyvB,QAAQ,GACfzvB,KAAKyvB,SAERzvB,KAAKyG,SAASukB,QAChB,CAMA+E,gBACE,MAAMS,EAAqBxwB,KAAKyG,SAASyX,aAAeplB,SAASoB,gBAAgB8e,aAC3EsU,EAAiBttB,KAAK8vB,WAAWlD,WACjC8D,EAAoBpD,EAAiB,EAE3C,GAAIoD,IAAsBF,EAAoB,CAC5C,MAAMzqB,EAAWjL,IAAU,cAAgB,eAC3CkF,KAAKyG,SAASmK,MAAM7K,GAAa,GAAEunB,KACrC,CAEA,IAAKoD,GAAqBF,EAAoB,CAC5C,MAAMzqB,EAAWjL,IAAU,eAAiB,cAC5CkF,KAAKyG,SAASmK,MAAM7K,GAAa,GAAEunB,KACrC,CACF,CAEAiD,oBACEvwB,KAAKyG,SAASmK,MAAM+f,YAAc,GAClC3wB,KAAKyG,SAASmK,MAAMggB,aAAe,EACrC,CAGA1pB,uBAAuB1B,EAAQ3F,GAC7B,OAAOG,KAAKuJ,MAAK,WACf,MAAMC,EAAOgmB,GAAMrmB,oBAAoBnJ,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQ3F,EANb,CAOF,GACF,EAOFU,EAAac,GAAGvI,SA9Sc,0BAUD,4BAoSyC,SAAUoG,GAC9E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGRjC,EAAae,IAAItE,EAAQqyB,IAAYwB,IAC/BA,EAAU3uB,kBAKd3B,EAAae,IAAItE,EAAQoyB,IAAc,KACjCp2B,EAAUgH,OACZA,KAAKgrB,OACP,GACA,IAIJ,MAAM8F,EAActpB,EAAeG,QA5Tf,eA6ThBmpB,GACFtB,GAAMroB,YAAY2pB,GAAazgB,OAGpBmf,GAAMrmB,oBAAoBnM,GAElC4M,OAAO5J,KACd,IAEA8I,EAAqB0mB,IAMrBx0B,EAAmBw0B,IC9VnB,MAOMjgB,GAAkB,OAClBwhB,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxB9B,GAAgB,sBAOhBhqB,GAAU,CACdkpB,UAAU,EACVtiB,UAAU,EACV4P,QAAQ,GAGJvW,GAAc,CAClBipB,SAAU,mBACVtiB,SAAU,UACV4P,OAAQ,WAOV,MAAMuV,WAAkB5qB,EACtBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAKoQ,UAAW,EAChBpQ,KAAK0vB,UAAY1vB,KAAK2vB,sBACtB3vB,KAAK4vB,WAAa5vB,KAAK6vB,uBACvB7vB,KAAK4M,oBACP,CAGWxH,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA5DS,WA6DX,CAGAuO,OAAO/J,GACL,OAAOG,KAAKoQ,SAAWpQ,KAAKqQ,OAASrQ,KAAKsQ,KAAKzQ,EACjD,CAEAyQ,KAAKzQ,GACCG,KAAKoQ,UAIS7P,EAAauB,QAAQ9B,KAAKyG,SA5D5B,oBA4DkD,CAAE5G,kBAEtDqC,mBAIdlC,KAAKoQ,UAAW,EAChBpQ,KAAK0vB,UAAUpf,OAEVtQ,KAAK0G,QAAQkV,SAChB,IAAI+Q,IAAkBtc,OAGxBrQ,KAAKyG,SAASjC,aAAa,cAAc,GACzCxE,KAAKyG,SAASjC,aAAa,OAAQ,UACnCxE,KAAKyG,SAAS7M,UAAUqR,IAAI8lB,IAY5B/wB,KAAKgH,gBAVoB,KAClBhH,KAAK0G,QAAQkV,SAAU5b,KAAK0G,QAAQ4nB,UACvCtuB,KAAK4vB,WAAWb,WAGlB/uB,KAAKyG,SAAS7M,UAAUqR,IAAIsE,IAC5BvP,KAAKyG,SAAS7M,UAAUgK,OAAOmtB,IAC/BxwB,EAAauB,QAAQ9B,KAAKyG,SAnFX,qBAmFkC,CAAE5G,iBAAgB,GAG/BG,KAAKyG,UAAU,GACvD,CAEA4J,OACOrQ,KAAKoQ,WAIQ7P,EAAauB,QAAQ9B,KAAKyG,SA7F5B,qBA+FFvE,mBAIdlC,KAAK4vB,WAAWV,aAChBlvB,KAAKyG,SAAS2qB,OACdpxB,KAAKoQ,UAAW,EAChBpQ,KAAKyG,SAAS7M,UAAUqR,IAAI+lB,IAC5BhxB,KAAK0vB,UAAUrf,OAcfrQ,KAAKgH,gBAZoB,KACvBhH,KAAKyG,SAAS7M,UAAUgK,OAAO2L,GAAiByhB,IAChDhxB,KAAKyG,SAAS/B,gBAAgB,cAC9B1E,KAAKyG,SAAS/B,gBAAgB,QAEzB1E,KAAK0G,QAAQkV,SAChB,IAAI+Q,IAAkB/J,QAGxBriB,EAAauB,QAAQ9B,KAAKyG,SAAU2oB,GAAa,GAGbpvB,KAAKyG,UAAU,IACvD,CAEAG,UACE5G,KAAK0vB,UAAU9oB,UACf5G,KAAK4vB,WAAWV,aAChB1oB,MAAMI,SACR,CAGA+oB,sBACE,MAUM32B,EAAY8H,QAAQd,KAAK0G,QAAQ4nB,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtB90B,YACAiO,YAAY,EACZ+mB,YAAahuB,KAAKyG,SAASjN,WAC3Bu0B,cAAe/0B,EAjBK,KACU,WAA1BgH,KAAK0G,QAAQ4nB,SAKjBtuB,KAAKqQ,OAJH9P,EAAauB,QAAQ9B,KAAKyG,SAAUyqB,GAI3B,EAWgC,MAE/C,CAEArB,uBACE,OAAO,IAAIjB,GAAU,CACnBD,YAAa3uB,KAAKyG,UAEtB,CAEAmG,qBACErM,EAAac,GAAGrB,KAAKyG,SAvJM,gCAuJ2BvH,IAtKvC,WAuKTA,EAAMyD,MAIL3C,KAAK0G,QAAQsF,SAKlBhM,KAAKqQ,OAJH9P,EAAauB,QAAQ9B,KAAKyG,SAAUyqB,IAI3B,GAEf,CAGAhqB,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO2nB,GAAUhoB,oBAAoBnJ,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KANb,CAOF,GACF,EAOFO,EAAac,GAAGvI,SA5Lc,8BAGD,gCAyLyC,SAAUoG,GAC9E,MAAMlC,EAASwK,EAAeoB,uBAAuB5I,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,MACb,OAGFO,EAAae,IAAItE,EAAQoyB,IAAc,KAEjCp2B,EAAUgH,OACZA,KAAKgrB,OACP,IAIF,MAAM8F,EAActpB,EAAeG,QAAQspB,IACvCH,GAAeA,IAAgB9zB,GACjCm0B,GAAUhqB,YAAY2pB,GAAazgB,OAGxB8gB,GAAUhoB,oBAAoBnM,GACtC4M,OAAO5J,KACd,IAEAO,EAAac,GAAGvJ,OAvOa,8BAuOgB,KAC3C,IAAK,MAAMD,KAAY2P,EAAevI,KAAKgyB,IACzCE,GAAUhoB,oBAAoBtR,GAAUyY,MAC1C,IAGF/P,EAAac,GAAGvJ,OA/NM,uBA+NgB,KACpC,IAAK,MAAMO,KAAWmP,EAAevI,KAAK,gDACG,UAAvC9F,iBAAiBd,GAAS6b,UAC5Bid,GAAUhoB,oBAAoB9Q,GAASgY,MAE3C,IAGFvH,EAAqBqoB,IAMrBn2B,EAAmBm2B,IChRnB,MAAME,GAAgB,IAAI9yB,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI+yB,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAAChd,EAAWid,KACnC,MAAMC,EAAgBld,EAAU1B,SAASzO,cAEzC,OAAIotB,EAAqBrwB,SAASswB,IAC5BL,GAAc5xB,IAAIiyB,IACb5wB,QAAQwwB,GAAiBlrB,KAAKoO,EAAUmd,YAAcJ,GAAiBnrB,KAAKoO,EAAUmd,YAO1FF,EAAqB1sB,QAAO6sB,GAAkBA,aAA0BzrB,SAC5E6c,MAAK6O,GAASA,EAAMzrB,KAAKsrB,IAAe,EAGhCI,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAlCP,kBAmC7BjR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BkR,KAAM,GACNjR,EAAG,GACHkR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ3Q,EAAG,GACHjU,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChD6kB,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IC/DAnuB,GAAU,CACdouB,UAAW1B,GACX2B,QAAS,GACTC,WAAY,GACZhW,MAAM,EACNiW,UAAU,EACVC,WAAY,KACZC,SAAU,eAGNxuB,GAAc,CAClBmuB,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZhW,KAAM,UACNiW,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACPl8B,SAAU,oBAOZ,MAAMm8B,WAAwB7uB,EAC5BU,YAAYL,GACVgB,QACAxG,KAAK0G,QAAU1G,KAAKuF,WAAWC,EACjC,CAGWJ,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MA/CS,iBAgDX,CAGA44B,aACE,OAAOl1B,OAAOC,OAAOgB,KAAK0G,QAAQ+sB,SAC/BjrB,KAAIhD,GAAUxF,KAAKk0B,yBAAyB1uB,KAC5CT,OAAOjE,QACZ,CAEAqzB,aACE,OAAOn0B,KAAKi0B,aAAap7B,OAAS,CACpC,CAEAu7B,cAAcX,GAGZ,OAFAzzB,KAAKq0B,cAAcZ,GACnBzzB,KAAK0G,QAAQ+sB,QAAU,IAAKzzB,KAAK0G,QAAQ+sB,WAAYA,GAC9CzzB,IACT,CAEAs0B,SACE,MAAMC,EAAkBz7B,SAASy1B,cAAc,OAC/CgG,EAAgBC,UAAYx0B,KAAKy0B,eAAez0B,KAAK0G,QAAQmtB,UAE7D,IAAK,MAAOh8B,EAAU68B,KAAS31B,OAAOoC,QAAQnB,KAAK0G,QAAQ+sB,SACzDzzB,KAAK20B,YAAYJ,EAAiBG,EAAM78B,GAG1C,MAAMg8B,EAAWU,EAAgB3sB,SAAS,GACpC8rB,EAAa1zB,KAAKk0B,yBAAyBl0B,KAAK0G,QAAQgtB,YAM9D,OAJIA,GACFG,EAASj6B,UAAUqR,OAAOyoB,EAAW92B,MAAM,MAGtCi3B,CACT,CAGAluB,iBAAiBH,GACfgB,MAAMb,iBAAiBH,GACvBxF,KAAKq0B,cAAc7uB,EAAOiuB,QAC5B,CAEAY,cAAcO,GACZ,IAAK,MAAO/8B,EAAU47B,KAAY10B,OAAOoC,QAAQyzB,GAC/CpuB,MAAMb,iBAAiB,CAAE9N,WAAUk8B,MAAON,GAAWK,GAEzD,CAEAa,YAAYd,EAAUJ,EAAS57B,GAC7B,MAAMg9B,EAAkBrtB,EAAeG,QAAQ9P,EAAUg8B,GAEpDgB,KAILpB,EAAUzzB,KAAKk0B,yBAAyBT,IAOpCj7B,EAAUi7B,GACZzzB,KAAK80B,sBAAsBl8B,EAAW66B,GAAUoB,GAI9C70B,KAAK0G,QAAQgX,KACfmX,EAAgBL,UAAYx0B,KAAKy0B,eAAehB,GAIlDoB,EAAgBE,YAActB,EAd5BoB,EAAgBjxB,SAepB,CAEA6wB,eAAeG,GACb,OAAO50B,KAAK0G,QAAQitB,SDzDjB,SAAsBqB,EAAYxB,EAAWyB,GAClD,IAAKD,EAAWn8B,OACd,OAAOm8B,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIp9B,OAAOq9B,WACKC,gBAAgBJ,EAAY,aACxDrhB,EAAW,GAAGlM,UAAUytB,EAAgBt6B,KAAKwF,iBAAiB,MAEpE,IAAK,MAAM/H,KAAWsb,EAAU,CAC9B,MAAM0hB,EAAch9B,EAAQya,SAASzO,cAErC,IAAKtF,OAAO4C,KAAK6xB,GAAWpyB,SAASi0B,GAAc,CACjDh9B,EAAQuL,SAER,QACF,CAEA,MAAM0xB,EAAgB,GAAG7tB,UAAUpP,EAAQuM,YACrC2wB,EAAoB,GAAG9tB,OAAO+rB,EAAU,MAAQ,GAAIA,EAAU6B,IAAgB,IAEpF,IAAK,MAAM7gB,KAAa8gB,EACjB9D,GAAiBhd,EAAW+gB,IAC/Bl9B,EAAQqM,gBAAgB8P,EAAU1B,SAGxC,CAEA,OAAOoiB,EAAgBt6B,KAAK45B,SAC9B,CCwBmCgB,CAAaZ,EAAK50B,KAAK0G,QAAQ8sB,UAAWxzB,KAAK0G,QAAQktB,YAAcgB,CACtG,CAEAV,yBAAyBU,GACvB,OAAO94B,EAAQ84B,EAAK,CAAC50B,MACvB,CAEA80B,sBAAsBz8B,EAASw8B,GAC7B,GAAI70B,KAAK0G,QAAQgX,KAGf,OAFAmX,EAAgBL,UAAY,QAC5BK,EAAgBrG,OAAOn2B,GAIzBw8B,EAAgBE,YAAc18B,EAAQ08B,WACxC,ECzIF,MACMU,GAAwB,IAAIl3B,IAAI,CAAC,WAAY,YAAa,eAE1Dm3B,GAAkB,OAElBnmB,GAAkB,OAGlBomB,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOp7B,IAAU,OAAS,QAC1Bq7B,OAAQ,SACRC,KAAMt7B,IAAU,QAAU,QAGtBsK,GAAU,CACdouB,UAAW1B,GACXuE,WAAW,EACXxX,SAAU,kBACVyX,WAAW,EACXC,YAAa,GACbC,MAAO,EACPjV,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C7D,MAAM,EACNtE,OAAQ,CAAC,EAAG,GACZnH,UAAW,MACXuY,aAAc,KACdmJ,UAAU,EACVC,WAAY,KACZ/7B,UAAU,EACVg8B,SAAU,+GAIV4C,MAAO,GACP30B,QAAS,eAGLuD,GAAc,CAClBmuB,UAAW,SACX6C,UAAW,UACXxX,SAAU,mBACVyX,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPjV,mBAAoB,QACpB7D,KAAM,UACNtE,OAAQ,0BACRnH,UAAW,oBACXuY,aAAc,yBACdmJ,SAAU,UACVC,WAAY,kBACZ/7B,SAAU,mBACVg8B,SAAU,SACV4C,MAAO,4BACP30B,QAAS,UAOX,MAAM40B,WAAgBnwB,EACpBV,YAAYxN,EAASmN,GACnB,QAAsB,IAAX0lB,GACT,MAAM,IAAI7kB,UAAU,+DAGtBG,MAAMnO,EAASmN,GAGfxF,KAAK22B,YAAa,EAClB32B,KAAK42B,SAAW,EAChB52B,KAAK62B,WAAa,KAClB72B,KAAK82B,eAAiB,GACtB92B,KAAK0qB,QAAU,KACf1qB,KAAK+2B,iBAAmB,KACxB/2B,KAAKg3B,YAAc,KAGnBh3B,KAAKi3B,IAAM,KAEXj3B,KAAKk3B,gBAEAl3B,KAAK0G,QAAQ7O,UAChBmI,KAAKm3B,WAET,CAGW/xB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAxHS,SAyHX,CAGA+7B,SACEp3B,KAAK22B,YAAa,CACpB,CAEAU,UACEr3B,KAAK22B,YAAa,CACpB,CAEAW,gBACEt3B,KAAK22B,YAAc32B,KAAK22B,UAC1B,CAEA/sB,SACO5J,KAAK22B,aAIV32B,KAAK82B,eAAeS,OAASv3B,KAAK82B,eAAeS,MAC7Cv3B,KAAKoQ,WACPpQ,KAAKw3B,SAIPx3B,KAAKy3B,SACP,CAEA7wB,UACEsH,aAAalO,KAAK42B,UAElBr2B,EAAaC,IAAIR,KAAKyG,SAASnN,QAAQq8B,IAAiBC,GAAkB51B,KAAK03B,mBAE3E13B,KAAKyG,SAASzM,aAAa,2BAC7BgG,KAAKyG,SAASjC,aAAa,QAASxE,KAAKyG,SAASzM,aAAa,2BAGjEgG,KAAK23B,iBACLnxB,MAAMI,SACR,CAEA0J,OACE,GAAoC,SAAhCtQ,KAAKyG,SAASmK,MAAM2Z,QACtB,MAAM,IAAIjlB,MAAM,uCAGlB,IAAMtF,KAAK43B,mBAAoB53B,KAAK22B,WAClC,OAGF,MAAM9F,EAAYtwB,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAzJxD,SA2JTipB,GADa59B,EAAe+F,KAAKyG,WACLzG,KAAKyG,SAASwM,cAAc/Y,iBAAiBL,SAASmG,KAAKyG,UAE7F,GAAIoqB,EAAU3uB,mBAAqB21B,EACjC,OAIF73B,KAAK23B,iBAEL,MAAMV,EAAMj3B,KAAK83B,iBAEjB93B,KAAKyG,SAASjC,aAAa,mBAAoByyB,EAAIj9B,aAAa,OAEhE,MAAMs8B,UAAEA,GAAct2B,KAAK0G,QAe3B,GAbK1G,KAAKyG,SAASwM,cAAc/Y,gBAAgBL,SAASmG,KAAKi3B,OAC7DX,EAAU9H,OAAOyI,GACjB12B,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA1KpC,cA6KnB5O,KAAK0qB,QAAU1qB,KAAK+qB,cAAckM,GAElCA,EAAIr9B,UAAUqR,IAAIsE,IAMd,iBAAkBzW,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAac,GAAGhJ,EAAS,YAAakC,GAc1CyF,KAAKgH,gBAVY,KACfzG,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA7LvC,WA+LU,IAApB5O,KAAK62B,YACP72B,KAAKw3B,SAGPx3B,KAAK62B,YAAa,CAAK,GAGK72B,KAAKi3B,IAAKj3B,KAAKiP,cAC/C,CAEAoB,OACE,GAAKrQ,KAAKoQ,aAIQ7P,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAjNxD,SAkND1M,iBAAd,CASA,GALYlC,KAAK83B,iBACbl+B,UAAUgK,OAAO2L,IAIjB,iBAAkBzW,SAASoB,gBAC7B,IAAK,MAAM7B,IAAW,GAAGoP,UAAU3O,SAAS8B,KAAKgN,UAC/CrH,EAAaC,IAAInI,EAAS,YAAakC,GAI3CyF,KAAK82B,eAA4B,OAAI,EACrC92B,KAAK82B,eAA4B,OAAI,EACrC92B,KAAK82B,eAA4B,OAAI,EACrC92B,KAAK62B,WAAa,KAelB72B,KAAKgH,gBAbY,KACXhH,KAAK+3B,yBAIJ/3B,KAAK62B,YACR72B,KAAK23B,iBAGP33B,KAAKyG,SAAS/B,gBAAgB,oBAC9BnE,EAAauB,QAAQ9B,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UA/OtC,WA+O8D,GAGjD5O,KAAKi3B,IAAKj3B,KAAKiP,cA/B7C,CAgCF,CAEAgN,SACMjc,KAAK0qB,SACP1qB,KAAK0qB,QAAQzO,QAEjB,CAGA2b,iBACE,OAAO92B,QAAQd,KAAKg4B,YACtB,CAEAF,iBAKE,OAJK93B,KAAKi3B,MACRj3B,KAAKi3B,IAAMj3B,KAAKi4B,kBAAkBj4B,KAAKg3B,aAAeh3B,KAAKk4B,2BAGtDl4B,KAAKi3B,GACd,CAEAgB,kBAAkBxE,GAChB,MAAMwD,EAAMj3B,KAAKm4B,oBAAoB1E,GAASa,SAG9C,IAAK2C,EACH,OAAO,KAGTA,EAAIr9B,UAAUgK,OAAO8xB,GAAiBnmB,IAEtC0nB,EAAIr9B,UAAUqR,IAAK,MAAKjL,KAAK6F,YAAYxK,aAEzC,MAAM+8B,E5EnRKC,KACb,GACEA,GAAU16B,KAAK26B,MAjCH,IAiCS36B,KAAK46B,gBACnBz/B,SAAS0/B,eAAeH,IAEjC,OAAOA,CAAM,E4E8QGI,CAAOz4B,KAAK6F,YAAYxK,MAAM0I,WAQ5C,OANAkzB,EAAIzyB,aAAa,KAAM4zB,GAEnBp4B,KAAKiP,eACPgoB,EAAIr9B,UAAUqR,IAAIyqB,IAGbuB,CACT,CAEAyB,WAAWjF,GACTzzB,KAAKg3B,YAAcvD,EACfzzB,KAAKoQ,aACPpQ,KAAK23B,iBACL33B,KAAKsQ,OAET,CAEA6nB,oBAAoB1E,GAalB,OAZIzzB,KAAK+2B,iBACP/2B,KAAK+2B,iBAAiB3C,cAAcX,GAEpCzzB,KAAK+2B,iBAAmB,IAAI/C,GAAgB,IACvCh0B,KAAK0G,QAGR+sB,UACAC,WAAY1zB,KAAKk0B,yBAAyBl0B,KAAK0G,QAAQ6vB,eAIpDv2B,KAAK+2B,gBACd,CAEAmB,yBACE,MAAO,CACL,iBAA0Bl4B,KAAKg4B,YAEnC,CAEAA,YACE,OAAOh4B,KAAKk0B,yBAAyBl0B,KAAK0G,QAAQ+vB,QAAUz2B,KAAKyG,SAASzM,aAAa,yBACzF,CAGA2+B,6BAA6Bz5B,GAC3B,OAAOc,KAAK6F,YAAYsD,oBAAoBjK,EAAMY,eAAgBE,KAAK44B,qBACzE,CAEA3pB,cACE,OAAOjP,KAAK0G,QAAQ2vB,WAAcr2B,KAAKi3B,KAAOj3B,KAAKi3B,IAAIr9B,UAAUC,SAAS67B,GAC5E,CAEAtlB,WACE,OAAOpQ,KAAKi3B,KAAOj3B,KAAKi3B,IAAIr9B,UAAUC,SAAS0V,GACjD,CAEAwb,cAAckM,GACZ,MAAMhlB,EAAYnW,EAAQkE,KAAK0G,QAAQuL,UAAW,CAACjS,KAAMi3B,EAAKj3B,KAAKyG,WAC7DoyB,EAAa9C,GAAc9jB,EAAU3L,eAC3C,OAAO4kB,GAAoBlrB,KAAKyG,SAAUwwB,EAAKj3B,KAAKorB,iBAAiByN,GACvE,CAEArN,aACE,MAAMpS,OAAEA,GAAWpZ,KAAK0G,QAExB,MAAsB,iBAAX0S,EACFA,EAAOxc,MAAM,KAAK4L,KAAI5F,GAASnG,OAAO8R,SAAS3L,EAAO,MAGzC,mBAAXwW,EACFqS,GAAcrS,EAAOqS,EAAYzrB,KAAKyG,UAGxC2S,CACT,CAEA8a,yBAAyBU,GACvB,OAAO94B,EAAQ84B,EAAK,CAAC50B,KAAKyG,UAC5B,CAEA2kB,iBAAiByN,GACf,MAAMnN,EAAwB,CAC5BzZ,UAAW4mB,EACX/R,UAAW,CACT,CACE1rB,KAAM,OACN+Y,QAAS,CACPoN,mBAAoBvhB,KAAK0G,QAAQ6a,qBAGrC,CACEnmB,KAAM,SACN+Y,QAAS,CACPiF,OAAQpZ,KAAKwrB,eAGjB,CACEpwB,KAAM,kBACN+Y,QAAS,CACP0K,SAAU7e,KAAK0G,QAAQmY,WAG3B,CACEzjB,KAAM,QACN+Y,QAAS,CACP9b,QAAU,IAAG2H,KAAK6F,YAAYxK,eAGlC,CACED,KAAM,kBACNmY,SAAS,EACTC,MAAO,aACPjY,GAAIiO,IAGFxJ,KAAK83B,iBAAiBtzB,aAAa,wBAAyBgF,EAAKkK,MAAMzB,UAAU,KAMzF,MAAO,IACFyZ,KACA5vB,EAAQkE,KAAK0G,QAAQ8jB,aAAc,CAACkB,IAE3C,CAEAwL,gBACE,MAAM4B,EAAW94B,KAAK0G,QAAQ5E,QAAQlF,MAAM,KAE5C,IAAK,MAAMkF,KAAWg3B,EACpB,GAAgB,UAAZh3B,EACFvB,EAAac,GAAGrB,KAAKyG,SAAUzG,KAAK6F,YAAY+I,UAtZpC,SAsZ4D5O,KAAK0G,QAAQ7O,UAAUqH,IAC7Ec,KAAK24B,6BAA6Bz5B,GAC1C0K,QAAQ,SAEb,GAjaU,WAiaN9H,EAA4B,CACrC,MAAMi3B,EAAUj3B,IAAY+zB,GAC1B71B,KAAK6F,YAAY+I,UAzZF,cA0Zf5O,KAAK6F,YAAY+I,UA5ZL,WA6ZRoqB,EAAWl3B,IAAY+zB,GAC3B71B,KAAK6F,YAAY+I,UA3ZF,cA4Zf5O,KAAK6F,YAAY+I,UA9ZJ,YAgafrO,EAAac,GAAGrB,KAAKyG,SAAUsyB,EAAS/4B,KAAK0G,QAAQ7O,UAAUqH,IAC7D,MAAM2sB,EAAU7rB,KAAK24B,6BAA6Bz5B,GAClD2sB,EAAQiL,eAA8B,YAAf53B,EAAMuB,KAAqBq1B,GAAgBD,KAAiB,EACnFhK,EAAQ4L,QAAQ,IAElBl3B,EAAac,GAAGrB,KAAKyG,SAAUuyB,EAAUh5B,KAAK0G,QAAQ7O,UAAUqH,IAC9D,MAAM2sB,EAAU7rB,KAAK24B,6BAA6Bz5B,GAClD2sB,EAAQiL,eAA8B,aAAf53B,EAAMuB,KAAsBq1B,GAAgBD,IACjEhK,EAAQplB,SAAS5M,SAASqF,EAAMW,eAElCgsB,EAAQ2L,QAAQ,GAEpB,CAGFx3B,KAAK03B,kBAAoB,KACnB13B,KAAKyG,UACPzG,KAAKqQ,MACP,EAGF9P,EAAac,GAAGrB,KAAKyG,SAASnN,QAAQq8B,IAAiBC,GAAkB51B,KAAK03B,kBAChF,CAEAP,YACE,MAAMV,EAAQz2B,KAAKyG,SAASzM,aAAa,SAEpCy8B,IAIAz2B,KAAKyG,SAASzM,aAAa,eAAkBgG,KAAKyG,SAASsuB,YAAYxtB,QAC1EvH,KAAKyG,SAASjC,aAAa,aAAciyB,GAG3Cz2B,KAAKyG,SAASjC,aAAa,yBAA0BiyB,GACrDz2B,KAAKyG,SAAS/B,gBAAgB,SAChC,CAEA+yB,SACMz3B,KAAKoQ,YAAcpQ,KAAK62B,WAC1B72B,KAAK62B,YAAa,GAIpB72B,KAAK62B,YAAa,EAElB72B,KAAKi5B,aAAY,KACXj5B,KAAK62B,YACP72B,KAAKsQ,MACP,GACCtQ,KAAK0G,QAAQ8vB,MAAMlmB,MACxB,CAEAknB,SACMx3B,KAAK+3B,yBAIT/3B,KAAK62B,YAAa,EAElB72B,KAAKi5B,aAAY,KACVj5B,KAAK62B,YACR72B,KAAKqQ,MACP,GACCrQ,KAAK0G,QAAQ8vB,MAAMnmB,MACxB,CAEA4oB,YAAYl8B,EAASm8B,GACnBhrB,aAAalO,KAAK42B,UAClB52B,KAAK42B,SAAW15B,WAAWH,EAASm8B,EACtC,CAEAnB,uBACE,OAAOh5B,OAAOC,OAAOgB,KAAK82B,gBAAgB11B,UAAS,EACrD,CAEAmE,WAAWC,GACT,MAAM2zB,EAAiB70B,EAAYK,kBAAkB3E,KAAKyG,UAE1D,IAAK,MAAM2yB,KAAiBr6B,OAAO4C,KAAKw3B,GAClC1D,GAAsBh2B,IAAI25B,WACrBD,EAAeC,GAW1B,OAPA5zB,EAAS,IACJ2zB,KACmB,iBAAX3zB,GAAuBA,EAASA,EAAS,IAEtDA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAO8wB,WAAiC,IAArB9wB,EAAO8wB,UAAsBx9B,SAAS8B,KAAOhC,EAAW4M,EAAO8wB,WAEtD,iBAAjB9wB,EAAOgxB,QAChBhxB,EAAOgxB,MAAQ,CACblmB,KAAM9K,EAAOgxB,MACbnmB,KAAM7K,EAAOgxB,QAIW,iBAAjBhxB,EAAOixB,QAChBjxB,EAAOixB,MAAQjxB,EAAOixB,MAAM1yB,YAGA,iBAAnByB,EAAOiuB,UAChBjuB,EAAOiuB,QAAUjuB,EAAOiuB,QAAQ1vB,YAG3ByB,CACT,CAEAozB,qBACE,MAAMpzB,EAAS,GAEf,IAAK,MAAO7C,EAAKC,KAAU7D,OAAOoC,QAAQnB,KAAK0G,SACzC1G,KAAK6F,YAAYT,QAAQzC,KAASC,IACpC4C,EAAO7C,GAAOC,GAUlB,OANA4C,EAAO3N,UAAW,EAClB2N,EAAO1D,QAAU,SAKV0D,CACT,CAEAmyB,iBACM33B,KAAK0qB,UACP1qB,KAAK0qB,QAAQtB,UACbppB,KAAK0qB,QAAU,MAGb1qB,KAAKi3B,MACPj3B,KAAKi3B,IAAIrzB,SACT5D,KAAKi3B,IAAM,KAEf,CAGA/vB,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOktB,GAAQvtB,oBAAoBnJ,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFxK,EAAmB07B,ICtmBnB,MAKMtxB,GAAU,IACXsxB,GAAQtxB,QACXquB,QAAS,GACTra,OAAQ,CAAC,EAAG,GACZnH,UAAW,QACX4hB,SAAU,8IAKV/xB,QAAS,SAGLuD,GAAc,IACfqxB,GAAQrxB,YACXouB,QAAS,kCAOX,MAAM4F,WAAgB3C,GAETtxB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAtCS,SAuCX,CAGAu8B,iBACE,OAAO53B,KAAKg4B,aAAeh4B,KAAKs5B,aAClC,CAGApB,yBACE,MAAO,CACL,kBAAkBl4B,KAAKg4B,YACvB,gBAAoBh4B,KAAKs5B,cAE7B,CAEAA,cACE,OAAOt5B,KAAKk0B,yBAAyBl0B,KAAK0G,QAAQ+sB,QACpD,CAGAvsB,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAO6vB,GAAQlwB,oBAAoBnJ,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFxK,EAAmBq+B,IC9EnB,MAMME,GAAe,qBAIf5tB,GAAoB,SAGpB6tB,GAAwB,SASxBp0B,GAAU,CACdgU,OAAQ,KACRqgB,WAAY,eACZC,cAAc,EACd18B,OAAQ,KACR28B,UAAW,CAAC,GAAK,GAAK,IAGlBt0B,GAAc,CAClB+T,OAAQ,gBACRqgB,WAAY,SACZC,aAAc,UACd18B,OAAQ,UACR28B,UAAW,SAOb,MAAMC,WAAkBrzB,EACtBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAGfxF,KAAK65B,aAAe,IAAI32B,IACxBlD,KAAK85B,oBAAsB,IAAI52B,IAC/BlD,KAAK+5B,aAA6D,YAA9C5gC,iBAAiB6G,KAAKyG,UAAUuW,UAA0B,KAAOhd,KAAKyG,SAC1FzG,KAAKg6B,cAAgB,KACrBh6B,KAAKi6B,UAAY,KACjBj6B,KAAKk6B,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBp6B,KAAKq6B,SACP,CAGWj1B,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MArES,WAsEX,CAGAg/B,UACEr6B,KAAKs6B,mCACLt6B,KAAKu6B,2BAEDv6B,KAAKi6B,UACPj6B,KAAKi6B,UAAUO,aAEfx6B,KAAKi6B,UAAYj6B,KAAKy6B,kBAGxB,IAAK,MAAMC,KAAW16B,KAAK85B,oBAAoB96B,SAC7CgB,KAAKi6B,UAAUU,QAAQD,EAE3B,CAEA9zB,UACE5G,KAAKi6B,UAAUO,aACfh0B,MAAMI,SACR,CAGAlB,kBAAkBF,GAWhB,OATAA,EAAOxI,OAASpE,EAAW4M,EAAOxI,SAAWlE,SAAS8B,KAGtD4K,EAAOi0B,WAAaj0B,EAAO4T,OAAU,GAAE5T,EAAO4T,oBAAsB5T,EAAOi0B,WAE3C,iBAArBj0B,EAAOm0B,YAChBn0B,EAAOm0B,UAAYn0B,EAAOm0B,UAAU/8B,MAAM,KAAK4L,KAAI5F,GAASnG,OAAOC,WAAWkG,MAGzE4C,CACT,CAEA+0B,2BACOv6B,KAAK0G,QAAQgzB,eAKlBn5B,EAAaC,IAAIR,KAAK0G,QAAQ1J,OAAQu8B,IAEtCh5B,EAAac,GAAGrB,KAAK0G,QAAQ1J,OAAQu8B,GAAaC,IAAuBt6B,IACvE,MAAM07B,EAAoB56B,KAAK85B,oBAAoB92B,IAAI9D,EAAMlC,OAAOkf,MACpE,GAAI0e,EAAmB,CACrB17B,EAAMsD,iBACN,MAAMnI,EAAO2F,KAAK+5B,cAAgBjiC,OAC5B8d,EAASglB,EAAkB1kB,UAAYlW,KAAKyG,SAASyP,UAC3D,GAAI7b,EAAKwgC,SAEP,YADAxgC,EAAKwgC,SAAS,CAAE3pB,IAAK0E,EAAQklB,SAAU,WAKzCzgC,EAAKoiB,UAAY7G,CACnB,KAEJ,CAEA6kB,kBACE,MAAMtmB,EAAU,CACd9Z,KAAM2F,KAAK+5B,aACXJ,UAAW35B,KAAK0G,QAAQizB,UACxBF,WAAYz5B,KAAK0G,QAAQ+yB,YAG3B,OAAO,IAAIsB,sBAAqB55B,GAAWnB,KAAKg7B,kBAAkB75B,IAAUgT,EAC9E,CAGA6mB,kBAAkB75B,GAChB,MAAM85B,EAAgBlH,GAAS/zB,KAAK65B,aAAa72B,IAAK,IAAG+wB,EAAM/2B,OAAO7E,MAChE42B,EAAWgF,IACf/zB,KAAKk6B,oBAAoBC,gBAAkBpG,EAAM/2B,OAAOkZ,UACxDlW,KAAKk7B,SAASD,EAAclH,GAAO,EAG/BqG,GAAmBp6B,KAAK+5B,cAAgBjhC,SAASoB,iBAAiBuiB,UAClE0e,EAAkBf,GAAmBp6B,KAAKk6B,oBAAoBE,gBACpEp6B,KAAKk6B,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAMrG,KAAS5yB,EAAS,CAC3B,IAAK4yB,EAAMqH,eAAgB,CACzBp7B,KAAKg6B,cAAgB,KACrBh6B,KAAKq7B,kBAAkBJ,EAAclH,IAErC,QACF,CAEA,MAAMuH,EAA2BvH,EAAM/2B,OAAOkZ,WAAalW,KAAKk6B,oBAAoBC,gBAEpF,GAAIgB,GAAmBG,GAGrB,GAFAvM,EAASgF,IAEJqG,EACH,YAOCe,GAAoBG,GACvBvM,EAASgF,EAEb,CACF,CAEAuG,mCACEt6B,KAAK65B,aAAe,IAAI32B,IACxBlD,KAAK85B,oBAAsB,IAAI52B,IAE/B,MAAMq4B,EAAc/zB,EAAevI,KAAKu6B,GAAuBx5B,KAAK0G,QAAQ1J,QAE5E,IAAK,MAAMw+B,KAAUD,EAAa,CAEhC,IAAKC,EAAOtf,MAAQziB,EAAW+hC,GAC7B,SAGF,MAAMZ,EAAoBpzB,EAAeG,QAAQ6zB,EAAOtf,KAAMlc,KAAKyG,UAG/DzN,EAAU4hC,KACZ56B,KAAK65B,aAAaz2B,IAAIo4B,EAAOtf,KAAMsf,GACnCx7B,KAAK85B,oBAAoB12B,IAAIo4B,EAAOtf,KAAM0e,GAE9C,CACF,CAEAM,SAASl+B,GACHgD,KAAKg6B,gBAAkBh9B,IAI3BgD,KAAKq7B,kBAAkBr7B,KAAK0G,QAAQ1J,QACpCgD,KAAKg6B,cAAgBh9B,EACrBA,EAAOpD,UAAUqR,IAAIU,IACrB3L,KAAKy7B,iBAAiBz+B,GAEtBuD,EAAauB,QAAQ9B,KAAKyG,SAjNN,wBAiNgC,CAAE5G,cAAe7C,IACvE,CAEAy+B,iBAAiBz+B,GAEf,GAAIA,EAAOpD,UAAUC,SAlNQ,iBAmN3B2N,EAAeG,QAxMY,mBAwMsB3K,EAAO1D,QAzMpC,cA0MjBM,UAAUqR,IAAIU,SAInB,IAAK,MAAM+vB,KAAal0B,EAAeO,QAAQ/K,EAnNnB,qBAsN1B,IAAK,MAAMgY,KAAQxN,EAAeS,KAAKyzB,EAlNhB,sDAmNrB1mB,EAAKpb,UAAUqR,IAAIU,GAGzB,CAEA0vB,kBAAkB3rB,GAChBA,EAAO9V,UAAUgK,OAAO+H,IAExB,MAAMgwB,EAAcn0B,EAAevI,KAAM,gBAAgDyQ,GACzF,IAAK,MAAMsD,KAAQ2oB,EACjB3oB,EAAKpZ,UAAUgK,OAAO+H,GAE1B,CAGAzE,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOowB,GAAUzwB,oBAAoBnJ,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjF,EAAac,GAAGvJ,OAlQa,8BAkQgB,KAC3C,IAAK,MAAM8jC,KAAOp0B,EAAevI,KA9PT,0BA+PtB26B,GAAUzwB,oBAAoByyB,EAChC,IAOF5gC,EAAmB4+B,ICnRnB,MAYMiC,GAAiB,YACjBC,GAAkB,aAClBpS,GAAe,UACfC,GAAiB,YAEjBhe,GAAoB,SACpB+pB,GAAkB,OAClBnmB,GAAkB,OAUlB7F,GAAuB,2EACvBqyB,GAAuB,gHAAqBryB,KAQlD,MAAMsyB,WAAYz1B,EAChBV,YAAYxN,GACVmO,MAAMnO,GACN2H,KAAK2qB,QAAU3qB,KAAKyG,SAASnN,QAfN,uCAiBlB0G,KAAK2qB,UAOV3qB,KAAKi8B,sBAAsBj8B,KAAK2qB,QAAS3qB,KAAKk8B,gBAE9C37B,EAAac,GAAGrB,KAAKyG,SA3CF,kBA2C2BvH,GAASc,KAAK6N,SAAS3O,KACvE,CAGW7D,kBACT,MAzDS,KA0DX,CAGAiV,OACE,MAAM6rB,EAAYn8B,KAAKyG,SACvB,GAAIzG,KAAKo8B,cAAcD,GACrB,OAIF,MAAME,EAASr8B,KAAKs8B,iBAEdC,EAAYF,EAChB97B,EAAauB,QAAQu6B,EAnEP,cAmE2B,CAAEx8B,cAAes8B,IAC1D,KAEgB57B,EAAauB,QAAQq6B,EApEvB,cAoE8C,CAAEt8B,cAAew8B,IAEjEn6B,kBAAqBq6B,GAAaA,EAAUr6B,mBAI1DlC,KAAKw8B,YAAYH,EAAQF,GACzBn8B,KAAKy8B,UAAUN,EAAWE,GAC5B,CAGAI,UAAUpkC,EAASqkC,GACZrkC,IAILA,EAAQuB,UAAUqR,IAAIU,IAEtB3L,KAAKy8B,UAAUj1B,EAAeoB,uBAAuBvQ,IAgBrD2H,KAAKgH,gBAdY,KACsB,QAAjC3O,EAAQ2B,aAAa,SAKzB3B,EAAQqM,gBAAgB,YACxBrM,EAAQmM,aAAa,iBAAiB,GACtCxE,KAAK28B,gBAAgBtkC,GAAS,GAC9BkI,EAAauB,QAAQzJ,EAhGN,eAgG4B,CACzCwH,cAAe68B,KARfrkC,EAAQuB,UAAUqR,IAAIsE,GAStB,GAG0BlX,EAASA,EAAQuB,UAAUC,SAAS67B,KACpE,CAEA8G,YAAYnkC,EAASqkC,GACdrkC,IAILA,EAAQuB,UAAUgK,OAAO+H,IACzBtT,EAAQ+4B,OAERpxB,KAAKw8B,YAAYh1B,EAAeoB,uBAAuBvQ,IAcvD2H,KAAKgH,gBAZY,KACsB,QAAjC3O,EAAQ2B,aAAa,SAKzB3B,EAAQmM,aAAa,iBAAiB,GACtCnM,EAAQmM,aAAa,WAAY,MACjCxE,KAAK28B,gBAAgBtkC,GAAS,GAC9BkI,EAAauB,QAAQzJ,EA7HL,gBA6H4B,CAAEwH,cAAe68B,KAP3DrkC,EAAQuB,UAAUgK,OAAO2L,GAOgD,GAG/ClX,EAASA,EAAQuB,UAAUC,SAAS67B,KACpE,CAEA7nB,SAAS3O,GACP,IAAM,CAAC28B,GAAgBC,GAAiBpS,GAAcC,IAAgBvoB,SAASlC,EAAMyD,KACnF,OAGFzD,EAAMktB,kBACNltB,EAAMsD,iBACN,MAAMgM,EAAS,CAACstB,GAAiBnS,IAAgBvoB,SAASlC,EAAMyD,KAC1Di6B,EAAoBz/B,EAAqB6C,KAAKk8B,eAAen3B,QAAO1M,IAAYoB,EAAWpB,KAAW6G,EAAMlC,OAAQwR,GAAQ,GAE9HouB,IACFA,EAAkB5R,MAAM,CAAE6R,eAAe,IACzCb,GAAI7yB,oBAAoByzB,GAAmBtsB,OAE/C,CAEA4rB,eACE,OAAO10B,EAAevI,KAAK88B,GAAqB/7B,KAAK2qB,QACvD,CAEA2R,iBACE,OAAOt8B,KAAKk8B,eAAej9B,MAAK4I,GAAS7H,KAAKo8B,cAAcv0B,MAAW,IACzE,CAEAo0B,sBAAsBvsB,EAAQ9H,GAC5B5H,KAAK88B,yBAAyBptB,EAAQ,OAAQ,WAE9C,IAAK,MAAM7H,KAASD,EAClB5H,KAAK+8B,6BAA6Bl1B,EAEtC,CAEAk1B,6BAA6Bl1B,GAC3BA,EAAQ7H,KAAKg9B,iBAAiBn1B,GAC9B,MAAMo1B,EAAWj9B,KAAKo8B,cAAcv0B,GAC9Bq1B,EAAYl9B,KAAKm9B,iBAAiBt1B,GACxCA,EAAMrD,aAAa,gBAAiBy4B,GAEhCC,IAAcr1B,GAChB7H,KAAK88B,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHp1B,EAAMrD,aAAa,WAAY,MAGjCxE,KAAK88B,yBAAyBj1B,EAAO,OAAQ,OAG7C7H,KAAKo9B,mCAAmCv1B,EAC1C,CAEAu1B,mCAAmCv1B,GACjC,MAAM7K,EAASwK,EAAeoB,uBAAuBf,GAEhD7K,IAILgD,KAAK88B,yBAAyB9/B,EAAQ,OAAQ,YAE1C6K,EAAM1P,IACR6H,KAAK88B,yBAAyB9/B,EAAQ,kBAAoB,IAAG6K,EAAM1P,MAEvE,CAEAwkC,gBAAgBtkC,EAASglC,GACvB,MAAMH,EAAYl9B,KAAKm9B,iBAAiB9kC,GACxC,IAAK6kC,EAAUtjC,UAAUC,SAxLN,YAyLjB,OAGF,MAAM+P,EAAS,CAAC/R,EAAUi2B,KACxB,MAAMz1B,EAAUmP,EAAeG,QAAQ9P,EAAUqlC,GAC7C7kC,GACFA,EAAQuB,UAAUgQ,OAAOkkB,EAAWuP,EACtC,EAGFzzB,EAjM6B,mBAiMI+B,IACjC/B,EAjM2B,iBAiMI2F,IAC/B2tB,EAAU14B,aAAa,gBAAiB64B,EAC1C,CAEAP,yBAAyBzkC,EAASmc,EAAW5R,GACtCvK,EAAQ0B,aAAaya,IACxBnc,EAAQmM,aAAagQ,EAAW5R,EAEpC,CAEAw5B,cAAcrsB,GACZ,OAAOA,EAAKnW,UAAUC,SAAS8R,GACjC,CAGAqxB,iBAAiBjtB,GACf,OAAOA,EAAKjI,QAAQi0B,IAAuBhsB,EAAOvI,EAAeG,QAAQo0B,GAAqBhsB,EAChG,CAGAotB,iBAAiBptB,GACf,OAAOA,EAAKzW,QAlNO,gCAkNoByW,CACzC,CAGA7I,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOwyB,GAAI7yB,oBAAoBnJ,MAErC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqBiE,IAAjBD,EAAKhE,IAAyBA,EAAO/D,WAAW,MAAmB,gBAAX+D,EAC1D,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,IANL,CAOF,GACF,EAOFjF,EAAac,GAAGvI,SA9Pc,eA8PkB4Q,IAAsB,SAAUxK,GAC1E,CAAC,IAAK,QAAQkC,SAASpB,KAAKkJ,UAC9BhK,EAAMsD,iBAGJ/I,EAAWuG,OAIfg8B,GAAI7yB,oBAAoBnJ,MAAMsQ,MAChC,IAKA/P,EAAac,GAAGvJ,OA3Qa,eA2QgB,KAC3C,IAAK,MAAMO,KAAWmP,EAAevI,KAtPF,iGAuPjC+8B,GAAI7yB,oBAAoB9Q,EAC1B,IAMF2C,EAAmBghC,IC9RnB,MAcMsB,GAAkB,OAClB/tB,GAAkB,OAClBwhB,GAAqB,UAErB1rB,GAAc,CAClBgxB,UAAW,UACXkH,SAAU,UACV/G,MAAO,UAGHpxB,GAAU,CACdixB,WAAW,EACXkH,UAAU,EACV/G,MAAO,KAOT,MAAMgH,WAAcj3B,EAClBV,YAAYxN,EAASmN,GACnBgB,MAAMnO,EAASmN,GAEfxF,KAAK42B,SAAW,KAChB52B,KAAKy9B,sBAAuB,EAC5Bz9B,KAAK09B,yBAA0B,EAC/B19B,KAAKk3B,eACP,CAGW9xB,qBACT,OAAOA,EACT,CAEWC,yBACT,OAAOA,EACT,CAEWhK,kBACT,MAtDS,OAuDX,CAGAiV,OACoB/P,EAAauB,QAAQ9B,KAAKyG,SAjD5B,iBAmDFvE,mBAIdlC,KAAK29B,gBAED39B,KAAK0G,QAAQ2vB,WACfr2B,KAAKyG,SAAS7M,UAAUqR,IAvDN,QAiEpBjL,KAAKyG,SAAS7M,UAAUgK,OAAO05B,IAC/B9iC,EAAOwF,KAAKyG,UACZzG,KAAKyG,SAAS7M,UAAUqR,IAAIsE,GAAiBwhB,IAE7C/wB,KAAKgH,gBAXY,KACfhH,KAAKyG,SAAS7M,UAAUgK,OAAOmtB,IAC/BxwB,EAAauB,QAAQ9B,KAAKyG,SA9DX,kBAgEfzG,KAAK49B,oBAAoB,GAOG59B,KAAKyG,SAAUzG,KAAK0G,QAAQ2vB,WAC5D,CAEAhmB,OACOrQ,KAAK69B,YAIQt9B,EAAauB,QAAQ9B,KAAKyG,SAlF5B,iBAoFFvE,mBAUdlC,KAAKyG,SAAS7M,UAAUqR,IAAI8lB,IAC5B/wB,KAAKgH,gBAPY,KACfhH,KAAKyG,SAAS7M,UAAUqR,IAAIqyB,IAC5Bt9B,KAAKyG,SAAS7M,UAAUgK,OAAOmtB,GAAoBxhB,IACnDhP,EAAauB,QAAQ9B,KAAKyG,SA1FV,kBA0FiC,GAIrBzG,KAAKyG,SAAUzG,KAAK0G,QAAQ2vB,YAC5D,CAEAzvB,UACE5G,KAAK29B,gBAED39B,KAAK69B,WACP79B,KAAKyG,SAAS7M,UAAUgK,OAAO2L,IAGjC/I,MAAMI,SACR,CAEAi3B,UACE,OAAO79B,KAAKyG,SAAS7M,UAAUC,SAAS0V,GAC1C,CAIAquB,qBACO59B,KAAK0G,QAAQ62B,WAIdv9B,KAAKy9B,sBAAwBz9B,KAAK09B,0BAItC19B,KAAK42B,SAAW15B,YAAW,KACzB8C,KAAKqQ,MAAM,GACVrQ,KAAK0G,QAAQ8vB,QAClB,CAEAsH,eAAe5+B,EAAO6+B,GACpB,OAAQ7+B,EAAMuB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKy9B,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACH/9B,KAAK09B,wBAA0BK,EASnC,GAAIA,EAEF,YADA/9B,KAAK29B,gBAIP,MAAMlvB,EAAcvP,EAAMW,cACtBG,KAAKyG,WAAagI,GAAezO,KAAKyG,SAAS5M,SAAS4U,IAI5DzO,KAAK49B,oBACP,CAEA1G,gBACE32B,EAAac,GAAGrB,KAAKyG,SArKA,sBAqK2BvH,GAASc,KAAK89B,eAAe5+B,GAAO,KACpFqB,EAAac,GAAGrB,KAAKyG,SArKD,qBAqK2BvH,GAASc,KAAK89B,eAAe5+B,GAAO,KACnFqB,EAAac,GAAGrB,KAAKyG,SArKF,oBAqK2BvH,GAASc,KAAK89B,eAAe5+B,GAAO,KAClFqB,EAAac,GAAGrB,KAAKyG,SArKD,qBAqK2BvH,GAASc,KAAK89B,eAAe5+B,GAAO,IACrF,CAEAy+B,gBACEzvB,aAAalO,KAAK42B,UAClB52B,KAAK42B,SAAW,IAClB,CAGA1vB,uBAAuB1B,GACrB,OAAOxF,KAAKuJ,MAAK,WACf,MAAMC,EAAOg0B,GAAMr0B,oBAAoBnJ,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBgE,EAAKhE,GACd,MAAM,IAAIa,UAAW,oBAAmBb,MAG1CgE,EAAKhE,GAAQxF,KACf,CACF,GACF,E,OAOF8I,EAAqB00B,IAMrBxiC,EAAmBwiC,IC1MJ,CACbp0B,QACAO,SACA0C,YACAsD,YACA8a,YACA+E,SACA2B,aACAkI,WACAO,aACAoC,OACAwB,SACA9G,W"}