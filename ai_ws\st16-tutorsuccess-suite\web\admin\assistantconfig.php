<?php
require_once('check_admin.php');
require_once('mysqlconn.php');
$tools = '';
$toolsname = '';
if ((isset($_POST["action"])) && ($_POST["action"] == "addnew")) {
    if ($_POST['retrieval']) {
        $tools .= ',{"type": "retrieval"}';
        $toolsname .= ',文本解析';
    }
    if ($_POST['code_interpreter']) {
        $tools .= ',{"type": "code_interpreter"}';
        $toolsname .= ',代码翻译';
    }
    if ($_POST['function']) { //function有点复杂，先不做实现
        $tools .= ',{"type": "function","function":"{"name":"test","description": "test function","parameters": {"type": "object","properties": {}}}"}';
        $toolsname .= ',函数处理';
    }
    if (!empty($tools)) {
        $tools = '[' . substr($tools, 1) . ']';
        $toolsname = substr($toolsname, 1);
    } else {
        $tools = '[]';
    }

    $headers  = [
        'Content-Type: application/json',
        'OpenAI-Beta: assistants=v1',
        'Authorization: Bearer ' . $_POST["apikey"]
    ];
    $postdata = array(
        'model' => $_POST["modelvalue"],
        'name' => $_POST["assistantname"],
        'description' => $_POST["description"],
        'instructions' => $_POST["instructions"],
        'tools' => json_decode($tools, true)
    );
    $postdata = json_encode($postdata);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_URL, $_POST["apiaddress"] . "/v1/assistants");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_AUTOREFERER, true);
    $responsedata = curl_exec($ch);
    curl_close($ch);


    if (empty($responsedata)) {
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';alert('API地址无法访问，请确认地址正确且服务器可以正常访问该地址！');</script>";
        exit;
    }

    $complete = json_decode($responsedata);
    if (isset($complete->error)) {
        $errcode = "";
        $errmsg = "";
        if (isset($complete->error->code)) {
            $errcode = $complete->error->code;
        }
        if (isset($complete->error->message)) {
            $errmsg = $complete->error->message;
        }
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';alert('接口返回错误！\\n错误代码：" . addslashes($errcode) . "\\n错误提示：" . addslashes($errmsg) . "');</script>";
        exit;
    } else if (isset($complete->id)) {
        $assistantid = $complete->id;
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';alert('接口返回错误！\\n错误消息：" . json_encode($responsedata) . "');</script>";
        exit;
    }

    $conn->insert('assistant', ['apikey' => $_POST["apikey"], 'apiaddress' => $_POST["apiaddress"], 'modelvalue' => $_POST["modelvalue"], 'assistantname' => $_POST["assistantname"], 'description' => $_POST["description"], 'instructions' => $_POST["instructions"], 'tools' => $toolsname, 'assistantid' => $assistantid, 'memo' => $_POST["memo"], 'createtime' => date('Y-m-d H:i:s')]);
    echo "<html><head><meta charset=utf-8></head><body><script>alert('创建成功！');parent.location.href='assistantconfig.php';</script>";
    exit;
} elseif ((isset($_GET["action"])) && ($_GET["action"] == "delete")) {
    $result = $conn->get('assistant', '*', ['id' => $_GET['id']]);
    if ($result) {
        $conn->delete('assistant', ['id' => $_GET['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>alert('删除成功！');parent.location.href='assistantconfig.php';</script>";
    } else {
        echo "<html><head><meta charset=utf-8></head><body><script>alert('该知识库已不存在！');parent.location.reload();</script>";
    }
    exit;
} elseif ((isset($_POST["action"])) && ($_POST["action"] == "update")) {
    $result = $conn->get('assistant', '*', ['id' => $_POST['id']]);
    if ($result) {
        $conn->update('assistant', ['memo' => $_POST['memo']], ['id' => $_POST['id']]);
        echo "<html><head><meta charset=utf-8></head><body><script>parent.document.getElementById('onthego').style.display='none';</script></body></html>";
    } else {
        echo '<html><head><meta charset=utf-8></head><body><script>alert("该知识库已不存在！");parent.location.reload();</script>';
    }
    exit;
} elseif ((isset($_POST["action"])) && ($_POST["action"] == "upload")) {
    if (!empty($_FILES['file']['name'])) {
        $filename = htmlspecialchars($_FILES['file']['name'], ENT_QUOTES, 'UTF-8');
        $temp = explode(".", $filename);
        $newfilename = time() . '.' . end($temp);
        $destination = "../upload/assistantfile/" . $newfilename;
        move_uploaded_file($_FILES['file']['tmp_name'], $destination);
        $row = $conn->get('assistant', '*', ['id' => $_POST["assistantid"]]);
        $apikey = $row["apikey"];
        $apiaddress = $row["apiaddress"];
        $assistantid = $row["assistantid"];
        $filelist = $row["filelist"];
        $headers = array(
            "Authorization: Bearer " . $apikey
        );
        $postfields = array(
            'purpose' => 'assistants',
            'file' => new CURLFile($destination)
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiaddress . "/v1/files");
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);

        $complete = json_decode($response);
        if (isset($complete->error)) {
            $errcode = "";
            $errmsg = "";
            if (isset($complete->error->code)) {
                $errcode = $complete->error->code;
            }
            if (isset($complete->error->message)) {
                $errmsg = $complete->error->message;
            }
            echo '{"success":false,"errmsg":"接口返回错误！\n错误代码：' . addslashes($errcode) . '\n错误提示：' . addslashes($errmsg) . '"}';
            exit;
        } else if (isset($complete->id)) {
            $fileid = $complete->id;
            if (!empty($filelist)) {
                $filelist .= ";" . $fileid . "," . $destination;
            } else {
                $filelist = $fileid . "," . $destination;
            }

            $headers  = [
                'Content-Type: application/json',
                'OpenAI-Beta: assistants=v1',
                "Authorization: Bearer " . $apikey
            ];
            $postdata = array(
                'file_id' => $fileid
            );
            $postdata = json_encode($postdata);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_URL, $apiaddress . "/v1/assistants/" . $assistantid . "/files");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_AUTOREFERER, true);
            $responsedata = curl_exec($ch);
            curl_close($ch);

            $conn->update('assistant', ['filelist' => $filelist], ['id' => $_POST['assistantid']]);
            echo '{"success":true,"fileinfo":"' . $fileid . ',' . $destination . '"}';
        } else {
            echo '{"success":false,"errmsg":"' . json_encode($responsedata) . '"}';
            exit;
        }
    }

    exit;
}

$row = $conn->get('asr', '*', ['id' => 1]);
$asrmodel = $row["asrmodel"];
$openaiapikey = $row["openaiapikey"];
$openaiapiaddress = $row["openaiapiaddress"];
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>知识库配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link href="img/admin.css" rel="stylesheet" type="text/css" />

    <meta name="description" content="overview &amp; stats" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />

    <link rel="stylesheet" href="../css/layui.css">
    <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="bootstrap/font-awesome.min.css" />

    <link rel="stylesheet" href="bootstrap/ace.min.css" />
    <script src="bootstrap/jquery.min.js"></script>
    <script src="bootstrap/jquery-ui.custom.min.js"></script>
    <script src="bootstrap/jquery.ui.touch-punch.min.js"></script>
    <script src="bootstrap/jquery.easypiechart.min.js"></script>
    <script src="bootstrap/jquery.sparkline.min.js"></script>
    <script src="bootstrap/ace.min.js"></script>
    <script src="bootstrap/ace-elements.min.js"></script>
    <script src="js/md5.js"></script>

    <link rel="stylesheet" href="bootstrap/ace.onpage-help.css" />
    <link rel="stylesheet" href="bootstrap/sunburst.css" />
    <script type="text/javascript">
        ace.vars['base'] = '.';
    </script>
    <script src="bootstrap/elements.onpage-help.js"></script>
    <script src="bootstrap/ace.onpage-help.js"></script>
    <script src="bootstrap/rainbow.js"></script>
    <script src="bootstrap/generic.js"></script>
    <script src="bootstrap/html.js"></script>
    <script src="bootstrap/css.js"></script>
    <script src="bootstrap/javascript.js"></script>
    <script src="/js/howler.min.js"></script>

    <script type="text/javascript" src="bootstrap/common.js"></script>
    <script src="../js/layui.js" type="application/javascript"></script>
    <script type="text/javascript" src="bootstrap/DatePicker.js"></script>
    <style>
        td {
            text-align: left;
        }

        input {
            text-align: left;
        }

        #onthego {
            position: absolute;
            top: 350px;
            left: 2%;
            right: 2%;
            width: 96%;
            height: 60px;
            text-align: center;
            font-size: 30px;
            line-height: 40px;
            z-index: 99;
            display: none;
        }

        div.mycollapse {
            overflow: hidden;
            white-space: normal;
            text-overflow: ellipsis;
            width: 100%;
            height: 40px;
        }

        div.myexpand {
            white-space: normal;
            width: 100%;
            height: auto;
        }

        table th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: white;
        }

        .multiselect {
            position: relative;
        }

        .multiselect label {
            position: relative;
            cursor: pointer;
            padding: 5px 15px;
            background-color: #eee;
            user-select: none;
        }
    </style>

</head>

<body class="no-skin" style="font-family:'微软雅黑'">

    <div id="onthego"><img border=0 src=img/loading.gif></div>

    <div class="main-container" id="main-container">
        <div class="breadcrumbs" id="breadcrumbs">
            <script type="text/javascript">
                try {
                    ace.settings.check('breadcrumbs', 'fixed')
                } catch (e) {}
            </script>
            <ul class="breadcrumb" style="padding-top:5px;">
                <li><i class="ace-icon fa fa-cog"></i> 系统管理</li>
                <li class="active">知识库配置</li>
            </ul>
        </div>
        <div class="main-content" style="margin:20px 0;">
            <input type=hidden name=action value=set>
            <div class="space-6"></div>
            <div class="col-xs-12">
                <div class="mycollapse" id="info" style="border-radius: 10px;padding:10px 20px;line-height:20px;margin-bottom:20px;border:1px solid silver;">
                    <button type="button" onclick="toggleCollapse(this)" style="float:right;font-weight:bold;padding:0 10px;margin-top:-3px;">点击展开模型配置说明</button>
                    <p>OpenAI的知识库是通过Assistant助理机器人的方式实现的。可以简单的理解为：用户先建立一个assistant，然后将相关文档喂给这个机器人，最后对这个机器人进行提问。</p>
                    <p>由于机器人必须属于某个账号，因此，创建知识库机器人的时候需要指定账号对应的APIKEY。知识库功能不需要开通gpt-4模型，5美元的体验账号也可以上传文档并使用gpt-3.5模型进行问答。</p>
                    <p>您可以在本页面建立多个机器人，并为每个机器人上传相关文档，然后在模型配置页面上选择对应的模型即可问答。</p>
                    <p>两种解析工具能够解析的文件类型不同，上传时请注意，具体列表可以 <a target="_blank" href="https://bbs.ipfei.cn/forum.php?mod=viewthread&tid=126">点击这里</a> 查看。</p>
                    <p>在本页面配置完毕后可以通过/assistantapi.php提供的接口调用知识库进行对话，该页面的接口规范兼容OpenAI，因此也支持通过其他网站调用。配置时把模型参数值设置成知识库ID即可。</p>
                    <p>OpenAI官方基于知识库的问答不支持流式输出，因此对话时等待时间可能较长。</p>
                </div>
                <button class="btn btn-sm btn-info" style="padding:2px 10px;margin-bottom:10px;" onclick="$('#addnew').show();">创建新的知识库</button>
                <div style="margin:20px 0;display:none;" id="addnew">
                    <form method=post name=addassistant target="temp" onsubmit="return checkform();">
                        <input name="action" value="addnew" type=hidden>
                        <table>
                            <tr>
                                <td style="text-align:right">
                                    <p>APIKEY：</p>
                                </td>
                                <td>
                                    <p><input name="apikey" class="bg-focus" autoComplete="off" size=80></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>API地址：</p>
                                </td>
                                <td>
                                    <p><input name="apiaddress" class="bg-focus" autoComplete="off" size=80 value="https://api.openai.com"></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>模型参数值：</p>
                                </td>
                                <td>
                                    <p><input name="modelvalue" class="bg-focus" autoComplete="off" value="gpt-3.5-turbo-0125" size=80></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>名字（可选）：</p>
                                </td>
                                <td>
                                    <p><input name="assistantname" class="bg-focus" autoComplete="off" size=80></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>描述（可选）：</p>
                                </td>
                                <td>
                                    <p><input name="description" class="bg-focus" autoComplete="off" size=80></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>回答问题的指示（可选）：</p>
                                </td>
                                <td>
                                    <p><input name="instructions" class="bg-focus" autoComplete="off" size=80 placeholder="你是一个客服人员。当遇到提问的时候，请结合文档中的内容用礼貌的语言进行回答。"></p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>解析工具（可选，支持多选）：</p>
                                </td>
                                <td>
                                    <p>
                                    <div class="multiselect">
                                        <input type="hidden" value=0 name="retrieval" id="retrieval">
                                        <label onclick='toggleselect(this,"retrieval");'>文本解析</label>
                                        <input type="hidden" value=0 name="code_interpreter" id="code_interpreter">
                                        <label onclick='toggleselect(this,"code_interpreter");'>代码翻译</label>
                                        <!--
                                        <input type="hidden" value=0 name="function" id="function">
                                        <label onclick='toggleselect(this,"function");'>函数处理</label>
                                        -->
                                    </div>
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td style="text-align:right">
                                    <p>备注：</p>
                                </td>
                                <td>
                                    <p><input name=memo class="bg-focus" autoComplete="off" size=80></p>
                                </td>
                            </tr>
                            <tr>
                                <td colspan=2 style="text-align:center">
                                    <button type=submit class="btn-primary" style="width:80px;">确认添加</button>
                                </td>
                            </tr>
                        </table>

                    </form>
                </div>
                <?php
                $sql = "select * from assistant order by id desc";
                $sqlcount = "select count(t.id) from (" . $sql . ") t";
                $result = $conn->query($sqlcount);
                $row = $result->fetch();
                $totalnumber = $row[0];
                if ($totalnumber == 0) {
                    echo "<tr><td colspan=12>暂未建立任何知识库。</td></tr>";
                } else {
                    $count = 0;
                    $result = $conn->query($sql);
                    while ($row = $result->fetch()) {
                        $count++;
                ?>
                        <form id="form<?php echo $row["id"] ?>" method=post target="temp" onsubmit="checkandsubmit('<?php echo $row["id"] ?>');">
                            <input type=hidden name=id value="<?php echo $row["id"] ?>"><input type=hidden name=action value=update>

                            <table class="table table-striped table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th class="text-center" style="width:60px;">序号</th>
                                        <th class="text-center" style="width:230px;">知识库ID</th>
                                        <th class="text-center" style="width:400px;">APIKEY</th>
                                        <th class="text-center" style="width:150px;">API地址</th>
                                        <th class="text-center" style="width:150px;">对话模型</th>
                                        <th class="text-center" style="width:160px;">创建时间</th>
                                        <th class="text-center">备注</th>
                                        <th class="text-center" style="width:60px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td rowspan=3 class="text-center"><?php echo $count ?></td>
                                        <td class="text-center" title="双击复制"><?php echo $row["assistantid"] ?></td>
                                        <td class="text-center" title="双击复制" style="cursor:pointer;min-width:400px;max-width:400px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;"><?php echo $row["apikey"] ?></td>
                                        <td class="text-center"><?php echo $row["apiaddress"] ?></td>
                                        <td class="text-center"><?php echo $row["modelvalue"] ?></td>
                                        <td class="text-center"><?php echo $row["createtime"] ?></td>
                                        <td class="text-center"><input name=memo style="width:100%;" value="<?php echo $row["memo"] ?>" onchange="checkandsubmit('<?php echo $row["id"] ?>');"></td>
                                        <td class="text-center"><input type=button style="width:45px;text-align:center;" onclick='deleteid(<?php echo $row["id"] ?>);' value="删除"></td>
                                    </tr>
                                    <tr>
                                        <th class="text-center" style="width:260px;">名字</th>
                                        <th class="text-center" style="width:260px;">回答指示</th>
                                        <th class="text-center" style="width:260px;">描述</th>
                                        <th class="text-center" style="width:260px;">解析工具</th>
                                        <th colspan=3 class="text-center">文档列表</th>
                                    </tr>
                                    <tr>
                                        <td class="text-center"><?php echo $row["assistantname"] ?></td>
                                        <td class="text-center"><?php echo $row["instructions"] ?></td>
                                        <td class="text-center"><?php echo $row["description"] ?></td>
                                        <td class="text-center"><?php echo $row["tools"] ?></td>
                                        <td colspan=3 style="text-align:left;">
                                            <div id=filelist style="display:flex;flex-wrap: wrap;justify-content: flex-start;align-items: center;max-width: 100%;">
                                                <?php
                                                if (!empty($row["filelist"])) {
                                                    foreach (explode(";", $row["filelist"]) as $onefile) {
                                                        echo '<span style="border:1px solid silver;padding:2px;margin:2px;white-space:nowrap;"><a target="_blank" href="' . explode(",", $onefile)[1] . '">' . explode(",", $onefile)[0] . '</a></span>';
                                                    }
                                                }
                                                ?>
                                            </div><input type=button value="上传" style="width:45px;text-align:center;" onclick="uploadfile('<?php echo $row["id"] ?>');">
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </form>
                <?php
                    }
                }

                ?>
            </div>
        </div>
    </div>
    <script>
        var currentid;

        function checkandsubmit(boxid) {
            var newid = "form" + boxid;
            document.getElementById('onthego').style.display = 'block';
            document.getElementById(newid).submit();
        }

        function deleteid(id) {
            if (confirm("确认删除该知识库吗？")) {
                document.getElementById("temp").src = "assistantconfig.php?action=delete&id=" + id;
            }
        }

        function uploadfile(id) {
            currentid = id;
            $("#uploadFile").click();
        }

        function doUpload() {
            document.getElementById('onthego').style.display = 'block';
            file = document.getElementById('uploadFile').files[0];
            var formData = new FormData();
            formData.append('action', 'upload');
            formData.append('assistantid', currentid);
            formData.append('file', file);
            $.ajax({
                url: 'assistantconfig.php',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(data) {
                    console.log('upload successful!\n' + data);
                    document.getElementById('onthego').style.display = 'none';
                    data = JSON.parse(data);
                    if (data.success) {
                        $("#filelist").append('<span style="border:1px solid silver;padding:2px;margin:2px;white-space:nowrap;"><a target="_blank" href="' + data.fileinfo.split(",")[1] + '">' + data.fileinfo.split(",")[0] + '</a></span><br>');
                    } else {
                        layer.msg("文件上传失败：" + data.errmsg);
                    }
                },
                error: function(error) {
                    document.getElementById('onthego').style.display = 'none';
                    layer.msg("文件上传失败：" + error.responseText);
                }
            });
        };

        function toggleCollapse(elem) {
            var myDiv = document.getElementById("info");
            if (myDiv.className == "mycollapse") {
                myDiv.className = "myexpand";
                elem.innerText = "点击收起模型配置说明";
            } else {
                myDiv.className = "mycollapse";
                elem.innerText = "点击展开模型配置说明";
            }
        }

        function toggleselect(it, elem) {
            if (document.getElementById(elem).value == 0) {
                document.getElementById(elem).value = 1;
                it.style.backgroundColor = '#ccc';
            } else {
                document.getElementById(elem).value = 0;
                it.style.backgroundColor = '#eee';
            }
        }

        function checkform() {
            if (window.addassistant.apikey.value == "") {
                alert("API_KEY不能为空！");
                return false;
            } else if (window.addassistant.apiaddress.value == "") {
                alert("API地址不能为空！");
                return false;
            } else if (window.addassistant.modelvalue.value == "") {
                alert("模型参数不能为空！");
                return false;
            } else {
                document.getElementById('onthego').style.display = 'block';
                return true;
            }
        }
        var cells = document.querySelectorAll('.text-center');

        cells.forEach(function(cell) {
            cell.addEventListener('dblclick', function() {
                if (cell.innerText != "") {
                    var range = document.createRange();
                    range.selectNodeContents(cell);
                    var selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);
                    document.execCommand('copy');
                    selection.removeAllRanges();
                    layer.msg('复制完成');
                }
            });
        });
    </script>
    <input type="file" id="uploadFile" onchange="doUpload();" style="display:none;">
    <iframe style="display:none;" id=temp name=temp></iframe>
</body>

</html>